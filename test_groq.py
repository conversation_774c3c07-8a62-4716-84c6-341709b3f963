#!/usr/bin/env python3
"""
Test script to use LiteLLM with Groq.
"""

import asyncio
import json
import os
from typing import Any, Dict

from plugginger.plugins.core.llm_provider.services.litellm_provider import Li<PERSON><PERSON>MProvider
from plugginger.plugins.core.llm_provider.services.litellm_factory import LiteLLMProviderFactory


async def test_groq_direct() -> None:
    """Test Groq with direct provider creation."""
    print("🚀 Testing Groq with direct provider creation...")
    
    # Direkte Groq-Provider Erstellung
    provider = LiteLLMProvider(
        provider="groq",
        model="llama3-8b-8192",
        api_key=os.getenv("GROQ_API_KEY")
    )
    
    # Test basic text generation
    result = await provider.generate_text(
        prompt="Explain what Groq is in one sentence.",
        max_tokens=100,
        temperature=0.1
    )
    
    print(f"✅ Success: {result['success']}")
    print(f"🤖 Provider: {result['provider']}")
    print(f"📱 Model: {result['model']}")
    print(f"📝 Content: {result['content']}")
    print(f"🔢 Tokens: {result['tokens_used']}")
    print()


async def test_groq_factory() -> None:
    """Test Groq with factory auto-detection."""
    print("🏭 Testing Groq with factory auto-detection...")
    
    # Factory auto-detection
    provider = LiteLLMProviderFactory.create_from_env()
    
    # Test structured generation
    result = await provider.generate_structured(
        system_message="You are a helpful assistant that responds in JSON format.",
        user_message="Create a simple profile for a software developer named Alice",
        max_tokens=200,
        temperature=0.2
    )
    
    print(f"✅ Success: {result['success']}")
    print(f"🤖 Provider: {result['provider']}")
    print(f"📱 Model: {result['model']}")
    print(f"✅ Validated: {result.get('validated', False)}")
    print(f"🔢 Tokens: {result['tokens_used']}")
    
    if result['success']:
        try:
            content = json.loads(result['content'])
            print(f"📋 JSON Content: {json.dumps(content, indent=2)}")
        except json.JSONDecodeError:
            print(f"📝 Raw Content: {result['content']}")
    print()


async def test_groq_schema() -> None:
    """Test Groq with schema generation (Outlines)."""
    print("📋 Testing Groq with schema generation...")
    
    provider = LiteLLMProvider(
        provider="groq",
        model="llama3-8b-8192",
        api_key=os.getenv("GROQ_API_KEY")
    )
    
    # Test schema generation
    schema = {
        "type": "object",
        "properties": {
            "name": {"type": "string"},
            "role": {"type": "string"},
            "experience_years": {"type": "integer"},
            "skills": {
                "type": "array",
                "items": {"type": "string"}
            },
            "remote_work": {"type": "boolean"}
        },
        "required": ["name", "role", "experience_years"]
    }
    
    result = await provider.generate_with_schema(
        prompt="Create a profile for a senior Python developer",
        schema=schema,
        max_tokens=300,
        temperature=0.3
    )
    
    print(f"✅ Success: {result['success']}")
    print(f"🤖 LiteLLM Provider: {result.get('litellm_provider', 'N/A')}")
    print(f"📱 LiteLLM Model: {result.get('litellm_model', 'N/A')}")
    print(f"⚡ Outlines Enabled: {result.get('outlines_enabled', False)}")
    print(f"🔢 Tokens: {result.get('tokens_used', 0)}")
    
    if result['success']:
        try:
            content = json.loads(result['content'])
            print(f"📋 Structured Content:")
            print(json.dumps(content, indent=2))
        except json.JSONDecodeError:
            print(f"📝 Raw Content: {result['content']}")
    else:
        print(f"❌ Error: {result.get('error', 'Unknown error')}")
    print()


async def test_different_groq_models() -> None:
    """Test different Groq models."""
    print("🔄 Testing different Groq models...")
    
    models = [
        "llama3-8b-8192",      # Standard
        "llama3-70b-8192",     # Larger
        "mixtral-8x7b-32768",  # Mixtral
    ]
    
    for model in models:
        print(f"  Testing {model}...")
        try:
            provider = LiteLLMProvider(
                provider="groq",
                model=model,
                api_key=os.getenv("GROQ_API_KEY")
            )
            
            result = await provider.generate_text(
                prompt="Say hello and tell me your model name",
                max_tokens=50,
                temperature=0.1
            )
            
            if result['success']:
                print(f"    ✅ {model}: {result['content'][:80]}...")
                print(f"    🔢 Tokens: {result['tokens_used']}")
            else:
                print(f"    ❌ {model}: {result.get('error', 'Failed')}")
                
        except Exception as e:
            print(f"    ❌ {model}: Exception - {e}")
        print()


async def main() -> None:
    """Main function."""
    print("🔍 Groq LiteLLM Test Suite")
    print("=" * 50)
    
    # Check API key
    if not os.getenv("GROQ_API_KEY"):
        print("❌ GROQ_API_KEY not found in environment!")
        print("Please set it in your .env file or environment.")
        return
    
    print(f"✅ GROQ_API_KEY found: {os.getenv('GROQ_API_KEY')[:20]}...")
    print()
    
    try:
        await test_groq_direct()
        await test_groq_factory()
        await test_groq_schema()
        await test_different_groq_models()
        
        print("🎉 All Groq tests completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
