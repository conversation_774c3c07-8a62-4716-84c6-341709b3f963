# Plugginger: Your AI Co-Pilot for Modular Python Applications

**Plugginger is a Python framework that redefines application development. Instead of writing boilerplate, you describe features in plain English. Plugginger's AI Co-Pilot generates clean, independent, and pre-wired plugins, letting you focus instantly on your core business logic.**

It's designed for developers who want to build complex, maintainable systems faster by treating AI as a core development partner.

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python Version](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![Status](https://img.shields.io/badge/status-alpha-orange.svg)](#api-stability)

---

## What Makes Plugginger Different?

Plugginger is built on a few core principles that set it apart from traditional frameworks.

*   **🤖 AI-Powered Scaffolding**  
    Describe a feature, get an intelligent starting point. The AI Co-Pilot generates a tailor-made code structure with the correct services, events, and dependencies already proposed—not just a dumb template.

*   **🔌 True Modularity (Beyond `import`)**  
    Build with independent "Lego bricks." Plugginger replaces rigid `import` statements with a dynamic Dependency Injection system. This allows you to develop, test, and swap plugins without creating a tangled mess of dependencies.

*   **💡 A Foundation for an Ecosystem (The Vision)**  
    Designed from day one to support community-driven "Plug-Stores." Every plugin's API is described in a machine-readable `manifest.yaml`, creating the foundation for a future where you can `plugginger install` community-built plugins.

*   **🔍 Introspectable by Design**  
    Your application's architecture is never a mystery. `plugginger inspect` provides a complete, machine-readable map of all your plugins and their connections, enabling powerful analysis and AI-assisted maintenance.

*   **🚀 The "Compile to Anywhere" Vision**  
    *(Future Goal)* Develop with Plugginger's magic, deploy anywhere. The long-term vision is a compiler that exports your app to a standard framework like Flask or FastAPI, eliminating lock-in.

---

## The Plugginger Contract: Your Logic, Our Plumbing

The core of Plugginger is a clear division of labor. **You, the developer, write the business logic.** The framework handles all the infrastructure—the "plumbing"—that brings that logic to life. This contract is established through simple Python **decorators**.

| Decorator | Your Instruction to the Framework |
| :--- | :--- |
| **`@plugin`** | "Treat this class as a self-contained, modular component." |
| **`@service`** | "Make this `async` method securely callable from anywhere in the app." |
| **`@on_event`** | "Execute this `async` method whenever a specific event occurs." |

### How It Works: An Example

```python
# calculator_plugin.py
from plugginger import PluginBase, plugin, service

@plugin(name="calculator", version="1.0.0") # <- Your instruction
class CalculatorPlugin(PluginBase):
    """A simple plugin for arithmetic operations."""

    # Your Job: The actual addition logic
    @service(name="add") # <- Your instruction
    async def add(self, a: int, b: int) -> int:
        # This is your business logic.
        result = a + b
        # The framework handles making this method callable via app.call_service().
        return result
```

When you run this code, Plugginger's runtime environment reads your `@plugin` and `@service` labels and automatically handles the dependency injection, service discovery, and request routing. You only need to focus on the logic inside the `add` method.

---

## Quick Start: 5 Minutes to Your First AI-Generated Plugin

### Prerequisites
- Python 3.11+
- An LLM provider: OpenAI API key, Google API key, or a local [Ollama](https://ollama.com/) instance.

### 1. Install Plugginger
```bash
pip install plugginger
# Install optional dependencies for your chosen LLM provider
pip install openai google-generativeai
```

### 2. Configure Your LLM
```bash
# Set your API key (choose one)
export OPENAI_API_KEY="your-openai-key"
export GOOGLE_API_KEY="your-google-key"
# OR start Ollama for local, private generation: ollama serve
```

### 3. Generate Your First Plugin
```bash
plugginger new plugin email_service \
  --prompt "Create a plugin to send emails via SMTP. It should have a 'send' service."
```
Now, look inside the newly created `./email_service/` directory. You'll find a `plugin.py` with a pre-defined `send` method and a `manifest.yaml` describing its interface. Your task is to add the SMTP logic.

### 4. Build and Run an Application
Create a file `app.py`:
```python
from plugginger import PluggingerAppBuilder
# from email_service.plugin import EmailServicePlugin # This path depends on your project structure

def create_app():
    builder = PluggingerAppBuilder(app_name="MyApp")
    # builder.include(EmailServicePlugin) # Include your new plugin
    return builder.build()

if __name__ == "__main__":
    app = create_app()
    # To run the app, use the CLI:
    # plugginger run app:create_app
```

Run your app from the terminal:
```bash
plugginger run app:create_app
```

---

## API Stability

Plugginger is currently in **alpha**. APIs may change, but we follow a clear [stability policy](docs/BREAKING_CHANGE_POLICY.md) to make transitions as smooth as possible.

-   ✅ **Stable APIs:** `plugginger.api.*` and `plugginger.cli` are considered stable and safe for use.
-   🧪 **Experimental APIs:** `plugginger.experimental.*` contains features that are subject to change. Use with caution.

---

## Community & Support

-   **🐛 Issues & Ideas:** Found a bug or have a great idea? [Open an issue.](https://github.com/jkehrhahn/plugginger/issues)
-   **📚 Documentation:** Dive deeper into the concepts in our [docs/](docs/) directory.
-   **💬 Discussions:** Have questions? Join the [GitHub Discussions.](https://github.com/jkehrhahn/plugginger/discussions)

---

## License

Licensed under the [MIT License](LICENSE).