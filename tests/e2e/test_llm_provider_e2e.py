"""
End-to-End tests for LLM Provider with real API calls and structured outputs.
"""
import json
import pytest
from typing import Any, Dict

from plugginger.plugins.core.llm_provider.services.litellm_provider import LiteLLMProvider
from plugginger.plugins.core.llm_provider.services.litellm_factory import LiteLLMProviderFactory
from plugginger.core.env_loader import get_api_key


class TestLLMProviderE2E:
    """End-to-End tests for LLM Provider functionality."""

    @pytest.fixture
    def openai_provider(self, skip_if_no_openai: None, openai_api_key: str) -> LiteLLMProvider:
        """Create OpenAI provider if API key is available."""
        return LiteLLMProvider("openai", "gpt-4o-mini", api_key=openai_api_key)

    @pytest.fixture
    def gemini_provider(self, skip_if_no_google: None, google_api_key: str) -> LiteLLMProvider:
        """Create Gemini provider if API key is available."""
        return LiteLLMProvider("gemini", "gemini-1.5-flash", api_key=google_api_key)

    @pytest.fixture
    def groq_provider(self, skip_if_no_groq: None, groq_api_key: str) -> LiteLLMProvider:
        """Create Groq provider if API key is available."""
        return LiteLLMProvider("groq", "llama3-8b-8192", api_key=groq_api_key)

    @pytest.mark.asyncio
    async def test_e2e_code_generation_openai(self, openai_provider: LiteLLMProvider) -> None:
        """Test end-to-end code generation with OpenAI."""
        result = await openai_provider.generate_text(
            prompt="Write a Python function that calculates the factorial of a number. Include docstring and type hints.",
            max_tokens=200,
            temperature=0.1
        )
        
        assert result["success"] is True
        assert "def" in result["content"]
        assert "factorial" in result["content"].lower()
        assert ":" in result["content"]  # Type hints
        assert '"""' in result["content"] or "'''" in result["content"]  # Docstring
        assert result["tokens_used"] > 0

    @pytest.mark.asyncio
    async def test_e2e_structured_json_openai(self, openai_provider: LiteLLMProvider) -> None:
        """Test structured JSON generation with OpenAI."""
        result = await openai_provider.generate_structured(
            system_message="You are a helpful assistant that generates valid JSON schemas.",
            user_message="Create a JSON schema for a user profile with name, email, age, and optional bio fields.",
            max_tokens=300
        )
        
        assert result["success"] is True
        assert result["validated"] is True
        
        # Parse and validate JSON
        json_data = json.loads(result["content"])
        assert isinstance(json_data, dict)
        assert "properties" in json_data or "name" in json_data  # Either schema or example
        assert result["tokens_used"] > 0

    @pytest.mark.asyncio
    async def test_e2e_complex_reasoning_gemini(self, gemini_provider: LiteLLMProvider) -> None:
        """Test complex reasoning task with Gemini."""
        result = await gemini_provider.generate_text(
            prompt="""
            Analyze this problem step by step:
            A company has 100 employees. 60% work remotely, 25% work in office, and the rest work hybrid.
            If remote workers get $50/month internet allowance and hybrid workers get $25/month,
            what's the total monthly allowance cost?
            """,
            max_tokens=250,
            temperature=0.1
        )
        
        assert result["success"] is True
        content = result["content"].lower()
        assert "60" in content or "sixty" in content
        assert "25" in content or "twenty" in content
        assert "50" in content or "fifty" in content
        assert "$" in result["content"] or "dollar" in content
        assert result["tokens_used"] > 0

    @pytest.mark.asyncio
    async def test_e2e_json_schema_validation_groq(self, groq_provider: LiteLLMProvider) -> None:
        """Test JSON schema validation with Groq."""
        result = await groq_provider.generate_structured(
            system_message="Generate valid JSON that matches the requested schema exactly.",
            user_message="""
            Create a JSON object representing a book with these exact fields:
            - title (string)
            - author (string) 
            - year (number)
            - genres (array of strings)
            - available (boolean)
            """,
            max_tokens=200
        )
        
        assert result["success"] is True
        assert result["validated"] is True
        
        # Validate JSON structure
        json_data = json.loads(result["content"])
        assert "title" in json_data
        assert "author" in json_data
        assert "year" in json_data
        assert "genres" in json_data
        assert "available" in json_data
        
        assert isinstance(json_data["title"], str)
        assert isinstance(json_data["author"], str)
        assert isinstance(json_data["year"], int)
        assert isinstance(json_data["genres"], list)
        assert isinstance(json_data["available"], bool)

    @pytest.mark.asyncio
    async def test_e2e_factory_auto_detection(self) -> None:
        """Test factory auto-detection with real environment."""
        # This test uses whatever provider is available in the environment
        try:
            provider = LiteLLMProviderFactory.create_from_env()
            
            result = await provider.generate_text(
                prompt="Say 'Hello from LiteLLM!' and nothing else.",
                max_tokens=50,
                temperature=0.0
            )
            
            assert result["success"] is True
            assert "hello" in result["content"].lower()
            assert "litellm" in result["content"].lower()
            assert result["provider"] in ["openai", "gemini", "groq", "ollama"]
            
        except Exception as e:
            pytest.skip(f"No LLM provider available: {e}")

    @pytest.mark.asyncio
    async def test_e2e_error_handling_invalid_key(self) -> None:
        """Test error handling with invalid API key."""
        provider = LiteLLMProvider("openai", "gpt-4o-mini", api_key="invalid-key")
        
        result = await provider.generate_text(
            prompt="This should fail",
            max_tokens=50
        )
        
        assert result["success"] is False
        assert "error" in result
        assert "authentication" in result["error"].lower() or "api key" in result["error"].lower()

    @pytest.mark.asyncio
    async def test_e2e_retry_mechanism_openai(self, openai_provider: LiteLLMProvider) -> None:
        """Test retry mechanism with structured generation."""
        # This test might trigger retries due to JSON validation
        result = await openai_provider.generate_structured(
            system_message="You must respond with valid JSON only.",
            user_message="Create a JSON with 'message': 'test', 'number': 42, 'list': [1,2,3]",
            max_retries=2,
            max_tokens=100
        )
        
        assert result["success"] is True
        assert result["validated"] is True
        assert "retries_used" in result
        assert result["retries_used"] >= 0
        
        # Validate JSON content
        json_data = json.loads(result["content"])
        assert "message" in json_data
        assert "number" in json_data
        assert "list" in json_data

    @pytest.mark.asyncio
    async def test_e2e_large_context_gemini(self, gemini_provider: LiteLLMProvider) -> None:
        """Test large context handling with Gemini."""
        # Create a longer prompt to test context handling
        long_text = "This is a test sentence. " * 100  # ~500 words
        
        result = await gemini_provider.generate_text(
            prompt=f"""
            Analyze the following text and provide a summary:
            
            {long_text}
            
            Provide a brief summary of what you observed about the text structure and content.
            """,
            max_tokens=150,
            temperature=0.1
        )
        
        assert result["success"] is True
        assert "summary" in result["content"].lower() or "text" in result["content"].lower()
        assert len(result["content"]) > 50  # Should be a meaningful response
        assert result["tokens_used"] > 100  # Should use significant tokens for large input

    @pytest.mark.asyncio
    async def test_e2e_temperature_consistency_openai(self, openai_provider: LiteLLMProvider) -> None:
        """Test temperature consistency in responses."""
        prompt = "Complete this sentence: 'The weather today is'"
        
        # Low temperature - should be consistent
        results_low = []
        for _ in range(3):
            result = await openai_provider.generate_text(
                prompt=prompt,
                max_tokens=20,
                temperature=0.0
            )
            assert result["success"] is True
            results_low.append(result["content"].strip())
        
        # All low-temperature results should be very similar
        assert len(set(results_low)) <= 2  # Allow for minor variations
        
        # High temperature - should be more varied
        results_high = []
        for _ in range(3):
            result = await openai_provider.generate_text(
                prompt=prompt,
                max_tokens=20,
                temperature=0.9
            )
            assert result["success"] is True
            results_high.append(result["content"].strip())
        
        # High-temperature results should show more variation
        # (This is probabilistic, so we don't enforce strict requirements)
        assert all(len(r) > 0 for r in results_high)

    @pytest.mark.asyncio
    async def test_e2e_outlines_json_schema_openai(self, openai_provider: LiteLLMProvider) -> None:
        """Test end-to-end Outlines JSON schema generation."""
        from pydantic import BaseModel

        class ProductReview(BaseModel):
            product_name: str
            rating: int  # 1-5
            review_text: str
            recommended: bool

        result = await openai_provider.generate_with_schema(
            prompt="Create a brief product review for a smartphone with 4-star rating",
            schema=ProductReview,
            max_tokens=500,  # Increased to ensure complete JSON
            temperature=0.1
        )

        assert result["success"] is True
        assert result.get("outlines_enabled") is True

        # Validate content structure
        content = result["content"]
        assert "product_name" in content
        assert "rating" in content
        assert "review_text" in content
        assert "recommended" in content
        assert content["rating"] == 4
        assert isinstance(content["recommended"], bool)

    @pytest.mark.asyncio
    async def test_e2e_outlines_regex_pattern_openai(self, openai_provider: LiteLLMProvider) -> None:
        """Test end-to-end Outlines regex pattern generation."""
        # UUID pattern
        uuid_pattern = r"[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"

        result = await openai_provider.generate_with_regex(
            prompt="Generate a UUID for a new user account",
            pattern=uuid_pattern,
            max_tokens=100,
            temperature=0.1
        )

        # OpenAI doesn't support regex constraints, so this should fail gracefully
        assert result["success"] is False
        assert "Cannot use regex-structured generation with an OpenAI model" in result["error"]

    @pytest.mark.asyncio
    async def test_e2e_outlines_ebnf_grammar_openai(self, openai_provider: LiteLLMProvider) -> None:
        """Test end-to-end Outlines EBNF grammar generation."""
        # SQL SELECT grammar
        sql_grammar = """
            start: select_stmt
            select_stmt: "SELECT" column_list "FROM" table_name [where_clause]
            column_list: column ("," column)*
            column: WORD
            table_name: WORD
            where_clause: "WHERE" condition
            condition: column "=" value
            value: ESCAPED_STRING | NUMBER

            %import common.WORD
            %import common.ESCAPED_STRING
            %import common.NUMBER
            %import common.WS
            %ignore WS
        """

        result = await openai_provider.generate_with_grammar(
            prompt="Generate a SQL SELECT statement to get user names from users table",
            grammar=sql_grammar,
            max_tokens=150,
            temperature=0.1
        )

        # OpenAI doesn't support EBNF grammar constraints, so this should fail gracefully
        assert result["success"] is False
        assert "Cannot use grammar-structured generation with an OpenAI model" in result["error"]

    @pytest.mark.asyncio
    async def test_e2e_concurrent_requests_groq(self, groq_provider: LiteLLMProvider) -> None:
        """Test concurrent request handling."""
        import asyncio
        
        async def make_request(i: int) -> Dict[str, Any]:
            return await groq_provider.generate_text(
                prompt=f"Count to {i+1} and stop.",
                max_tokens=50,
                temperature=0.1
            )
        
        # Make 3 concurrent requests
        tasks = [make_request(i) for i in range(3)]
        results = await asyncio.gather(*tasks)
        
        # All requests should succeed
        for i, result in enumerate(results):
            assert result["success"] is True
            assert str(i+1) in result["content"]
            assert result["tokens_used"] > 0
