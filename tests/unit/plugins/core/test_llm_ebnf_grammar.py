"""
EBNF Grammar tests for structured LLM outputs.
These tests validate that LLM responses conform to specific grammar patterns.
"""
import json
import re
import pytest
from typing import Any, Dict, List, Optional

from plugginger.plugins.core.llm_provider.services.validation_service import (
    ResponseValidationService,
    ValidationResult
)


class EBNFGrammarValidator:
    """Validator for EBNF grammar patterns in LLM responses."""

    def __init__(self) -> None:
        self.patterns: Dict[str, str] = {
            # JSON patterns
            "json_object": r'^\s*\{.*\}\s*$',
            "json_array": r'^\s*\[.*\]\s*$',
            "json_string": r'^"[^"]*"$',
            "json_number": r'^-?\d+(\.\d+)?([eE][+-]?\d+)?$',
            "json_boolean": r'^(true|false)$',
            
            # Code patterns
            "python_function": r'def\s+\w+\s*\([^)]*\)\s*:',
            "python_class": r'class\s+\w+\s*(\([^)]*\))?\s*:',
            "python_import": r'(from\s+\w+\s+)?import\s+\w+',
            
            # Structured text patterns
            "email": r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
            "url": r'^https?://[^\s/$.?#].[^\s]*$',
            "phone": r'^\+?[\d\s\-\(\)]{10,}$',
            "uuid": r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$',
            
            # Markdown patterns
            "markdown_header": r'^#{1,6}\s+.+$',
            "markdown_list": r'^[\s]*[-*+]\s+.+$',
            "markdown_code_block": r'^```[\s\S]*?```$',
            "markdown_link": r'\[([^\]]+)\]\(([^)]+)\)',
            
            # SQL patterns
            "sql_select": r'SELECT\s+.+\s+FROM\s+\w+',
            "sql_insert": r'INSERT\s+INTO\s+\w+\s*\([^)]+\)\s+VALUES\s*\([^)]+\)',
            "sql_update": r'UPDATE\s+\w+\s+SET\s+.+',
            "sql_delete": r'DELETE\s+FROM\s+\w+',
        }

    def validate_pattern(self, text: str, pattern_name: str) -> bool:
        """Validate text against a named EBNF pattern."""
        if pattern_name not in self.patterns:
            raise ValueError(f"Unknown pattern: {pattern_name}")
        
        pattern = self.patterns[pattern_name]
        return bool(re.search(pattern, text, re.IGNORECASE | re.MULTILINE | re.DOTALL))

    def validate_json_schema(self, json_text: str, schema: Dict[str, Any]) -> ValidationResult:
        """Validate JSON text against a schema-like structure."""
        errors: List[str] = []
        warnings: List[str] = []
        
        try:
            data = json.loads(json_text)
        except json.JSONDecodeError as e:
            return ValidationResult(
                is_valid=False,
                errors=[f"Invalid JSON: {str(e)}"],
                warnings=[],
                confidence_score=0.0
            )
        
        # Check required fields
        if "required" in schema:
            for field in schema["required"]:
                if field not in data:
                    errors.append(f"Missing required field: {field}")
        
        # Check field types
        if "properties" in schema:
            for field, field_schema in schema["properties"].items():
                if field in data:
                    expected_type = field_schema.get("type")
                    actual_value = data[field]
                    
                    if expected_type == "string" and not isinstance(actual_value, str):
                        errors.append(f"Field '{field}' should be string, got {type(actual_value).__name__}")
                    elif expected_type == "number" and not isinstance(actual_value, (int, float)):
                        errors.append(f"Field '{field}' should be number, got {type(actual_value).__name__}")
                    elif expected_type == "boolean" and not isinstance(actual_value, bool):
                        errors.append(f"Field '{field}' should be boolean, got {type(actual_value).__name__}")
                    elif expected_type == "array" and not isinstance(actual_value, list):
                        errors.append(f"Field '{field}' should be array, got {type(actual_value).__name__}")
                    elif expected_type == "object" and not isinstance(actual_value, dict):
                        errors.append(f"Field '{field}' should be object, got {type(actual_value).__name__}")
        
        confidence_score = 1.0 - (len(errors) * 0.2) - (len(warnings) * 0.1)
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            confidence_score=max(confidence_score, 0.0),
            validated_content=data
        )

    def validate_code_structure(self, code: str, language: str) -> ValidationResult:
        """Validate code structure for specific programming languages."""
        errors: List[str] = []
        warnings: List[str] = []
        
        if language.lower() == "python":
            # Check for basic Python structure
            if not re.search(r'def\s+\w+', code):
                warnings.append("No function definitions found")
            
            if not re.search(r':\s*$', code, re.MULTILINE):
                errors.append("No colon-terminated lines found (invalid Python syntax)")
            
            # Check for proper indentation (basic check)
            lines = code.split('\n')
            has_indentation = any(line.startswith('    ') or line.startswith('\t') for line in lines)
            if not has_indentation and len(lines) > 2:
                warnings.append("No indentation found - may not be valid Python")
        
        elif language.lower() == "sql":
            # Check for SQL keywords
            sql_keywords = ['SELECT', 'FROM', 'WHERE', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'ALTER']
            has_sql_keyword = any(keyword in code.upper() for keyword in sql_keywords)
            if not has_sql_keyword:
                errors.append("No SQL keywords found")
        
        confidence_score = 1.0 - (len(errors) * 0.3) - (len(warnings) * 0.1)
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            confidence_score=max(confidence_score, 0.0),
            validated_content=code
        )


class TestEBNFGrammarValidation:
    """Test EBNF grammar validation for LLM outputs."""

    @pytest.fixture
    def grammar_validator(self) -> EBNFGrammarValidator:
        """Create EBNF grammar validator."""
        return EBNFGrammarValidator()

    @pytest.fixture
    def validation_service(self) -> ResponseValidationService:
        """Create response validation service."""
        return ResponseValidationService()

    def test_json_object_pattern(self, grammar_validator: EBNFGrammarValidator) -> None:
        """Test JSON object pattern validation."""
        valid_json = '{"name": "test", "value": 42}'
        invalid_json = '{"name": "test", "value": 42'
        
        assert grammar_validator.validate_pattern(valid_json, "json_object") is True
        assert grammar_validator.validate_pattern(invalid_json, "json_object") is False

    def test_json_array_pattern(self, grammar_validator: EBNFGrammarValidator) -> None:
        """Test JSON array pattern validation."""
        valid_array = '[1, 2, 3, "test"]'
        invalid_array = '[1, 2, 3, "test"'
        
        assert grammar_validator.validate_pattern(valid_array, "json_array") is True
        assert grammar_validator.validate_pattern(invalid_array, "json_array") is False

    def test_python_function_pattern(self, grammar_validator: EBNFGrammarValidator) -> None:
        """Test Python function pattern validation."""
        valid_function = 'def calculate_sum(a, b):'
        invalid_function = 'function calculate_sum(a, b) {'
        
        assert grammar_validator.validate_pattern(valid_function, "python_function") is True
        assert grammar_validator.validate_pattern(invalid_function, "python_function") is False

    def test_email_pattern(self, grammar_validator: EBNFGrammarValidator) -> None:
        """Test email pattern validation."""
        valid_email = '<EMAIL>'
        invalid_email = 'test@example'
        
        assert grammar_validator.validate_pattern(valid_email, "email") is True
        assert grammar_validator.validate_pattern(invalid_email, "email") is False

    def test_url_pattern(self, grammar_validator: EBNFGrammarValidator) -> None:
        """Test URL pattern validation."""
        valid_url = 'https://example.com/path'
        invalid_url = 'not-a-url'
        
        assert grammar_validator.validate_pattern(valid_url, "url") is True
        assert grammar_validator.validate_pattern(invalid_url, "url") is False

    def test_markdown_header_pattern(self, grammar_validator: EBNFGrammarValidator) -> None:
        """Test Markdown header pattern validation."""
        valid_headers = [
            '# Main Title',
            '## Subtitle',
            '### Section',
            '#### Subsection',
            '##### Sub-subsection',
            '###### Smallest Header'
        ]
        invalid_headers = [
            '####### Too many hashes',
            'Not a header',
            '#No space after hash'
        ]
        
        for header in valid_headers:
            assert grammar_validator.validate_pattern(header, "markdown_header") is True
        
        for header in invalid_headers:
            assert grammar_validator.validate_pattern(header, "markdown_header") is False

    def test_sql_select_pattern(self, grammar_validator: EBNFGrammarValidator) -> None:
        """Test SQL SELECT pattern validation."""
        valid_sql = 'SELECT name, age FROM users'
        invalid_sql = 'GET name, age FROM users'
        
        assert grammar_validator.validate_pattern(valid_sql, "sql_select") is True
        assert grammar_validator.validate_pattern(invalid_sql, "sql_select") is False

    def test_json_schema_validation(self, grammar_validator: EBNFGrammarValidator) -> None:
        """Test JSON schema validation."""
        schema = {
            "required": ["name", "age"],
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "number"},
                "email": {"type": "string"},
                "active": {"type": "boolean"}
            }
        }
        
        valid_json = '{"name": "John", "age": 30, "email": "<EMAIL>", "active": true}'
        result = grammar_validator.validate_json_schema(valid_json, schema)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
        assert result.confidence_score > 0.8

    def test_json_schema_validation_missing_required(self, grammar_validator: EBNFGrammarValidator) -> None:
        """Test JSON schema validation with missing required fields."""
        schema = {
            "required": ["name", "age"],
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "number"}
            }
        }
        
        invalid_json = '{"name": "John"}'  # Missing age
        result = grammar_validator.validate_json_schema(invalid_json, schema)
        
        assert result.is_valid is False
        assert any("Missing required field: age" in error for error in result.errors)

    def test_json_schema_validation_wrong_types(self, grammar_validator: EBNFGrammarValidator) -> None:
        """Test JSON schema validation with wrong field types."""
        schema = {
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "number"},
                "active": {"type": "boolean"}
            }
        }
        
        invalid_json = '{"name": 123, "age": "thirty", "active": "yes"}'
        result = grammar_validator.validate_json_schema(invalid_json, schema)
        
        assert result.is_valid is False
        assert len(result.errors) == 3  # All three fields have wrong types

    def test_python_code_structure_validation(self, grammar_validator: EBNFGrammarValidator) -> None:
        """Test Python code structure validation."""
        valid_python = '''
def calculate_factorial(n):
    if n <= 1:
        return 1
    return n * calculate_factorial(n - 1)
'''
        
        result = grammar_validator.validate_code_structure(valid_python, "python")
        assert result.is_valid is True
        assert result.confidence_score > 0.8

    def test_python_code_structure_validation_invalid(self, grammar_validator: EBNFGrammarValidator) -> None:
        """Test Python code structure validation with invalid code."""
        invalid_python = '''
function calculateFactorial(n) {
    if (n <= 1) {
        return 1;
    }
    return n * calculateFactorial(n - 1);
}
'''
        
        result = grammar_validator.validate_code_structure(invalid_python, "python")
        assert result.is_valid is False
        assert len(result.errors) > 0

    def test_sql_code_structure_validation(self, grammar_validator: EBNFGrammarValidator) -> None:
        """Test SQL code structure validation."""
        valid_sql = '''
SELECT u.name, u.email, p.title
FROM users u
JOIN posts p ON u.id = p.user_id
WHERE u.active = true
ORDER BY p.created_at DESC;
'''
        
        result = grammar_validator.validate_code_structure(valid_sql, "sql")
        assert result.is_valid is True
        assert result.confidence_score > 0.8

    def test_unknown_pattern_error(self, grammar_validator: EBNFGrammarValidator) -> None:
        """Test error handling for unknown patterns."""
        with pytest.raises(ValueError, match="Unknown pattern"):
            grammar_validator.validate_pattern("test", "unknown_pattern")

    @pytest.mark.asyncio
    async def test_integration_with_validation_service(
        self, 
        validation_service: ResponseValidationService,
        grammar_validator: EBNFGrammarValidator
    ) -> None:
        """Test integration between EBNF validator and validation service."""
        # Register EBNF validator as custom validator
        async def ebnf_json_validator(response: str, **kwargs: Any) -> ValidationResult:
            # First check if it's valid JSON
            json_result = await validation_service.validate_json_response(response)
            if not json_result.is_valid:
                return json_result
            
            # Then check EBNF pattern
            pattern_valid = grammar_validator.validate_pattern(response, "json_object")
            if not pattern_valid:
                json_result.is_valid = False
                json_result.errors.append("Does not match JSON object EBNF pattern")
            
            return json_result
        
        validation_service.register_custom_validator("ebnf_json", ebnf_json_validator)
        
        # Test with valid JSON
        valid_json = '{"test": "value"}'
        result = await validation_service.validate_with_custom(valid_json, "ebnf_json")
        assert result.is_valid is True
        
        # Test with invalid JSON
        invalid_json = '{"test": "value"'
        result = await validation_service.validate_with_custom(invalid_json, "ebnf_json")
        assert result.is_valid is False
