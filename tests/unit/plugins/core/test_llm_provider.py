"""
Unit tests for LiteLLM Provider services.
"""
import json
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Any, Dict

from plugginger.plugins.core.llm_provider.services.litellm_provider import Li<PERSON><PERSON><PERSON>rovider
from plugginger.plugins.core.llm_provider.services.litellm_factory import Lite<PERSON><PERSON>roviderFactory
from plugginger.plugins.core.llm_provider.services.validation_service import ResponseValidationService
from plugginger.core.exceptions import PluggingerConfigurationError


class TestLiteLLMProvider:
    """Test cases for LiteLLMProvider."""

    @pytest.fixture
    def mock_litellm_response(self) -> MagicMock:
        """Mock LiteLLM response object."""
        response = MagicMock()
        response.choices = [MagicMock()]
        response.choices[0].message.content = "Test response"
        response.model = "gpt-4o-mini"
        response.usage = MagicMock()
        response.usage.total_tokens = 50
        return response

    @pytest.fixture
    def provider(self) -> LiteLLMProvider:
        """Create a test provider instance."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            return LiteLLMProvider(
                provider="openai",
                model="gpt-4o-mini",
                api_key="test-key"
            )

    def test_init_without_litellm(self) -> None:
        """Test initialization when LiteLLM is not available."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', False):
            with pytest.raises(PluggingerConfigurationError, match="litellm.*not installed"):
                LiteLLMProvider("openai", "gpt-4o-mini")

    def test_get_litellm_model_string_openai(self, provider: LiteLLMProvider) -> None:
        """Test model string formatting for OpenAI."""
        assert provider._get_litellm_model_string() == "gpt-4o-mini"

    def test_get_litellm_model_string_gemini(self) -> None:
        """Test model string formatting for Gemini."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            provider = LiteLLMProvider("gemini", "gemini-1.5-flash")
            assert provider._get_litellm_model_string() == "gemini/gemini-1.5-flash"

    def test_get_litellm_model_string_groq(self) -> None:
        """Test model string formatting for Groq."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            provider = LiteLLMProvider("groq", "llama3-8b-8192")
            assert provider._get_litellm_model_string() == "groq/llama3-8b-8192"

    def test_get_litellm_model_string_ollama(self) -> None:
        """Test model string formatting for Ollama."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            provider = LiteLLMProvider("ollama", "llama3")
            assert provider._get_litellm_model_string() == "ollama/llama3"

    @pytest.mark.asyncio
    async def test_generate_text_success(self, provider: LiteLLMProvider, mock_litellm_response: MagicMock) -> None:
        """Test successful text generation."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.litellm') as mock_litellm:
            mock_litellm.acompletion = AsyncMock(return_value=mock_litellm_response)
            
            result = await provider.generate_text("Test prompt")
            
            assert result["success"] is True
            assert result["content"] == "Test response"
            assert result["provider"] == "openai"
            assert result["tokens_used"] == 50
            
            mock_litellm.acompletion.assert_called_once()
            call_args = mock_litellm.acompletion.call_args[1]
            assert call_args["model"] == "gpt-4o-mini"
            assert call_args["messages"] == [{"role": "user", "content": "Test prompt"}]
            assert call_args["api_key"] == "test-key"

    @pytest.mark.asyncio
    async def test_generate_text_failure(self, provider: LiteLLMProvider) -> None:
        """Test text generation failure."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.litellm') as mock_litellm:
            mock_litellm.acompletion = AsyncMock(side_effect=Exception("API Error"))
            
            result = await provider.generate_text("Test prompt")
            
            assert result["success"] is False
            assert "API Error" in result["error"]
            assert result["tokens_used"] == 0

    @pytest.mark.asyncio
    async def test_generate_structured_success(self, provider: LiteLLMProvider, mock_litellm_response: MagicMock) -> None:
        """Test successful structured generation."""
        mock_litellm_response.choices[0].message.content = '{"key": "value"}'
        
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.litellm') as mock_litellm:
            mock_litellm.acompletion = AsyncMock(return_value=mock_litellm_response)
            
            result = await provider.generate_structured("System", "User message")
            
            assert result["success"] is True
            assert result["validated"] is True
            assert result["content"] == '{"key": "value"}'
            assert result["retries_used"] == 0

    @pytest.mark.asyncio
    async def test_generate_structured_invalid_json_retry(self, provider: LiteLLMProvider, mock_litellm_response: MagicMock) -> None:
        """Test structured generation with invalid JSON that succeeds on retry."""
        responses = [
            MagicMock(choices=[MagicMock(message=MagicMock(content="invalid json"))]),
            mock_litellm_response
        ]
        responses[0].usage = MagicMock(total_tokens=10)
        responses[1].choices[0].message.content = '{"key": "value"}'
        
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.litellm') as mock_litellm:
            mock_litellm.acompletion = AsyncMock(side_effect=responses)
            
            result = await provider.generate_structured("System", "User message")
            
            assert result["success"] is True
            assert result["validated"] is True
            assert result["retries_used"] == 1

    @pytest.mark.asyncio
    async def test_generate_structured_max_retries(self, provider: LiteLLMProvider) -> None:
        """Test structured generation that fails after max retries."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.litellm') as mock_litellm:
            mock_response = MagicMock()
            mock_response.choices = [MagicMock()]
            mock_response.choices[0].message.content = "invalid json"
            mock_response.usage = MagicMock(total_tokens=10)
            mock_litellm.acompletion = AsyncMock(return_value=mock_response)
            
            result = await provider.generate_structured("System", "User message", max_retries=2)
            
            assert result["success"] is False
            assert result["validated"] is False
            assert result["retries_used"] == 2
            assert "Invalid JSON response" in result["error"]


class TestLiteLLMProviderFactory:
    """Test cases for LiteLLMProviderFactory."""

    def test_create_from_env_explicit_provider(self) -> None:
        """Test creating provider from explicit environment variables."""
        with patch.dict('os.environ', {
            'PLUGGINGER_LLM_PROVIDER': 'openai',
            'PLUGGINGER_LLM_API_KEY': 'test-key',
            'PLUGGINGER_LLM_MODEL': 'gpt-4'
        }):
            with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
                provider = LiteLLMProviderFactory.create_from_env()
                assert provider.provider == "openai"
                assert provider.model == "gpt-4"
                assert provider.api_key == "test-key"

    def test_create_from_env_auto_detect_openai(self) -> None:
        """Test auto-detection of OpenAI provider."""
        with patch.dict('os.environ', {'OPENAI_API_KEY': 'test-key'}, clear=True):
            with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
                provider = LiteLLMProviderFactory.create_from_env()
                assert provider.provider == "openai"
                assert provider.model == "gpt-4o-mini"
                assert provider.api_key == "test-key"

    def test_create_from_env_auto_detect_gemini(self) -> None:
        """Test auto-detection of Gemini provider."""
        with patch.dict('os.environ', {'GOOGLE_API_KEY': 'test-key'}, clear=True):
            with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
                provider = LiteLLMProviderFactory.create_from_env()
                assert provider.provider == "gemini"
                assert provider.model == "gemini-1.5-flash"
                assert provider.api_key == "test-key"

    def test_create_from_env_auto_detect_groq(self) -> None:
        """Test auto-detection of Groq provider."""
        with patch.dict('os.environ', {'GROQ_API_KEY': 'test-key'}, clear=True):
            with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
                provider = LiteLLMProviderFactory.create_from_env()
                assert provider.provider == "groq"
                assert provider.model == "llama3-8b-8192"
                assert provider.api_key == "test-key"

    def test_create_from_env_fallback_ollama(self) -> None:
        """Test fallback to Ollama when no API keys are found."""
        with patch.dict('os.environ', {}, clear=True):
            with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
                provider = LiteLLMProviderFactory.create_from_env()
                assert provider.provider == "ollama"
                assert provider.model == "qwen2.5-coder:7b"
                assert provider.api_key is None

    def test_create_from_env_no_providers(self) -> None:
        """Test error when no providers are available."""
        with patch.dict('os.environ', {}, clear=True):
            with patch.object(LiteLLMProviderFactory, 'PROVIDER_DETECTION_ORDER', []):
                with pytest.raises(PluggingerConfigurationError, match="Could not auto-detect"):
                    LiteLLMProviderFactory.create_from_env()

    def test_create_from_env_unsupported_provider(self) -> None:
        """Test error for unsupported provider."""
        with patch.dict('os.environ', {'PLUGGINGER_LLM_PROVIDER': 'unsupported'}, clear=True):
            with pytest.raises(PluggingerConfigurationError, match="Unsupported provider"):
                LiteLLMProviderFactory.create_from_env()


class TestResponseValidationService:
    """Test cases for ResponseValidationService."""

    @pytest.fixture
    def validation_service(self) -> ResponseValidationService:
        """Create a validation service instance."""
        return ResponseValidationService()

    @pytest.mark.asyncio
    async def test_validate_json_response_valid(self, validation_service: ResponseValidationService) -> None:
        """Test validation of valid JSON response."""
        json_response = '{"name": "test", "value": 42}'
        result = await validation_service.validate_json_response(json_response)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
        assert result.validated_content == {"name": "test", "value": 42}
        assert result.confidence_score > 0.5

    @pytest.mark.asyncio
    async def test_validate_json_response_invalid(self, validation_service: ResponseValidationService) -> None:
        """Test validation of invalid JSON response."""
        invalid_json = '{"name": "test", "value": 42'  # Missing closing brace
        result = await validation_service.validate_json_response(invalid_json)
        
        assert result.is_valid is False
        assert len(result.errors) > 0
        assert "Invalid JSON format" in result.errors[0]
        assert result.confidence_score == 0.0

    @pytest.mark.asyncio
    async def test_validate_text_response_valid(self, validation_service: ResponseValidationService) -> None:
        """Test validation of valid text response."""
        text_response = "This is a valid text response with sufficient length."
        result = await validation_service.validate_text_response(text_response)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
        assert result.validated_content == text_response
        assert result.confidence_score > 0.5

    @pytest.mark.asyncio
    async def test_validate_text_response_too_short(self, validation_service: ResponseValidationService) -> None:
        """Test validation of text response that's too short."""
        short_text = ""
        result = await validation_service.validate_text_response(short_text, min_length=10)
        
        assert result.is_valid is False
        assert any("too short" in error for error in result.errors)

    @pytest.mark.asyncio
    async def test_validate_text_response_empty(self, validation_service: ResponseValidationService) -> None:
        """Test validation of empty text response."""
        empty_text = "   "  # Only whitespace
        result = await validation_service.validate_text_response(empty_text)
        
        assert result.is_valid is False
        assert any("empty" in error for error in result.errors)

    def test_register_custom_validator(self, validation_service: ResponseValidationService) -> None:
        """Test registering a custom validator."""
        def custom_validator(response: str, **kwargs: Any) -> bool:
            return "custom" in response.lower()
        
        validation_service.register_custom_validator("custom_test", custom_validator)
        assert "custom_test" in validation_service.custom_validators

    @pytest.mark.asyncio
    async def test_validate_with_custom_validator(self, validation_service: ResponseValidationService) -> None:
        """Test validation with custom validator."""
        async def custom_validator(response: str, **kwargs: Any) -> bool:
            return "valid" in response.lower()

        validation_service.register_custom_validator("test_validator", custom_validator)

        # Test valid response
        result = await validation_service.validate_with_custom("This is valid", "test_validator")
        assert result.is_valid is True

        # Test invalid response
        result = await validation_service.validate_with_custom("This is bad", "test_validator")
        assert result.is_valid is False
