"""
Unit tests for Structured Generation Plugin.
"""
import json
import pytest
from typing import Any, Dict, List
from unittest.mock import AsyncMock, MagicMock, patch
from enum import Enum

from pydantic import BaseModel

from plugginger.plugins.core.structured_generation.services.outlines_provider import (
    OutlinesProvider,
    OutlinesProviderFactory,
)
from plugginger.plugins.core.structured_generation.services.schema_service import (
    SchemaService,
    SchemaValidationResult,
)
from plugginger.plugins.core.structured_generation.services.grammar_service import (
    GrammarService,
    EBNFGrammar,
    GrammarValidationResult,
)


class TestUser(BaseModel):
    """Test Pydantic model."""
    name: str
    age: int
    email: str


class TestColor(str, Enum):
    """Test Enum."""
    RED = "red"
    GREEN = "green"
    BLUE = "blue"


class TestOutlinesProvider:
    """Test Outlines Provider functionality."""

    @pytest.fixture
    def mock_outlines(self) -> MagicMock:
        """Mock outlines module."""
        with patch('plugginger.plugins.core.structured_generation.services.outlines_provider.outlines') as mock:
            mock_model = MagicMock()
            mock.models.transformers.return_value = mock_model
            mock.models.openai.return_value = mock_model
            yield mock

    @pytest.fixture
    def provider(self, mock_outlines: MagicMock) -> OutlinesProvider:
        """Create test provider."""
        return OutlinesProvider("test-model", "transformers")

    def test_init_transformers_provider(self, mock_outlines: MagicMock) -> None:
        """Test initialization of transformers provider."""
        provider = OutlinesProvider("gpt2", "transformers", device="cpu")
        
        assert provider.model_name == "gpt2"
        assert provider.provider == "transformers"
        assert provider.device == "cpu"

    def test_init_openai_provider(self, mock_outlines: MagicMock) -> None:
        """Test initialization of OpenAI provider."""
        provider = OutlinesProvider("gpt-4", "openai", api_key="test-key")
        
        assert provider.model_name == "gpt-4"
        assert provider.provider == "openai"
        assert provider.model_kwargs.get("api_key") == "test-key"

    def test_model_creation_transformers(self, mock_outlines: MagicMock) -> None:
        """Test model creation for transformers."""
        provider = OutlinesProvider("gpt2", "transformers")
        model = provider.model
        
        mock_outlines.models.transformers.assert_called_once_with(
            "gpt2", device=None
        )
        assert model is not None

    def test_model_creation_openai(self, mock_outlines: MagicMock) -> None:
        """Test model creation for OpenAI."""
        provider = OutlinesProvider("gpt-4", "openai", api_key="test-key")
        model = provider.model
        
        mock_outlines.models.openai.assert_called_once_with(
            "gpt-4", api_key="test-key"
        )
        assert model is not None

    @pytest.mark.asyncio
    async def test_generate_json_with_pydantic(self, provider: OutlinesProvider, mock_outlines: MagicMock) -> None:
        """Test JSON generation with Pydantic model."""
        # Mock generator
        mock_generator = MagicMock()
        mock_result = TestUser(name="John", age=30, email="<EMAIL>")
        mock_generator.return_value = mock_result
        mock_outlines.generate.json.return_value = mock_generator
        
        result = await provider.generate_json(
            prompt="Create a user",
            schema=TestUser,
            max_tokens=100
        )
        
        assert result["success"] is True
        assert result["schema_validated"] is True
        assert result["content"]["name"] == "John"
        assert result["content"]["age"] == 30
        assert result["provider"] == "outlines"

    @pytest.mark.asyncio
    async def test_generate_json_with_dict_schema(self, provider: OutlinesProvider, mock_outlines: MagicMock) -> None:
        """Test JSON generation with dict schema."""
        # Mock generator
        mock_generator = MagicMock()
        mock_result = {"name": "Alice", "age": 25}
        mock_generator.return_value = mock_result
        mock_outlines.generate.json.return_value = mock_generator
        
        schema = {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "integer"}
            }
        }
        
        result = await provider.generate_json(
            prompt="Create a person",
            schema=schema,
            temperature=0.5
        )
        
        assert result["success"] is True
        assert result["content"]["name"] == "Alice"
        assert result["content"]["age"] == 25

    @pytest.mark.asyncio
    async def test_generate_choice_with_list(self, provider: OutlinesProvider, mock_outlines: MagicMock) -> None:
        """Test choice generation with list."""
        # Mock generator
        mock_generator = MagicMock()
        mock_generator.return_value = "red"
        mock_outlines.generate.choice.return_value = mock_generator
        
        choices = ["red", "green", "blue"]
        result = await provider.generate_choice(
            prompt="Pick a color",
            choices=choices
        )
        
        assert result["success"] is True
        assert result["choice_validated"] is True
        assert result["content"] == "red"
        assert result["available_choices"] == choices

    @pytest.mark.asyncio
    async def test_generate_choice_with_enum(self, provider: OutlinesProvider, mock_outlines: MagicMock) -> None:
        """Test choice generation with Enum."""
        # Mock generator
        mock_generator = MagicMock()
        mock_generator.return_value = TestColor.BLUE
        mock_outlines.generate.choice.return_value = mock_generator
        
        result = await provider.generate_choice(
            prompt="Pick a color",
            choices=TestColor
        )
        
        assert result["success"] is True
        assert result["content"] == TestColor.BLUE
        assert "blue" in result["available_choices"]

    @pytest.mark.asyncio
    async def test_generate_regex(self, provider: OutlinesProvider, mock_outlines: MagicMock) -> None:
        """Test regex generation."""
        # Mock generator
        mock_generator = MagicMock()
        mock_generator.return_value = "***********"
        mock_outlines.generate.regex.return_value = mock_generator
        
        pattern = r"\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}"
        result = await provider.generate_regex(
            prompt="Generate an IP address",
            pattern=pattern
        )
        
        assert result["success"] is True
        assert result["pattern_validated"] is True
        assert result["content"] == "***********"
        assert result["regex_pattern"] == pattern

    @pytest.mark.asyncio
    async def test_generate_grammar(self, provider: OutlinesProvider, mock_outlines: MagicMock) -> None:
        """Test EBNF grammar generation."""
        # Mock generator
        mock_generator = MagicMock()
        mock_generator.return_value = "2 + 3 * 4"
        mock_outlines.generate.cfg.return_value = mock_generator
        
        grammar = """
            ?start: expression
            ?expression: term (("+" | "-") term)*
            ?term: factor (("*" | "/") factor)*
            ?factor: NUMBER
            %import common.NUMBER
        """
        
        result = await provider.generate_grammar(
            prompt="Generate an arithmetic expression",
            grammar=grammar
        )
        
        assert result["success"] is True
        assert result["grammar_validated"] is True
        assert result["content"] == "2 + 3 * 4"
        assert result["ebnf_grammar"] == grammar

    @pytest.mark.asyncio
    async def test_generate_format(self, provider: OutlinesProvider, mock_outlines: MagicMock) -> None:
        """Test format generation."""
        # Mock generator
        mock_generator = MagicMock()
        mock_generator.return_value = 42
        mock_outlines.generate.format.return_value = mock_generator
        
        result = await provider.generate_format(
            prompt="Generate a number",
            format_type=int
        )
        
        assert result["success"] is True
        assert result["type_validated"] is True
        assert result["content"] == 42
        assert result["expected_type"] == "int"
        assert result["actual_type"] == "int"

    @pytest.mark.asyncio
    async def test_error_handling(self, provider: OutlinesProvider, mock_outlines: MagicMock) -> None:
        """Test error handling."""
        # Mock generator that raises exception
        mock_generator = MagicMock()
        mock_generator.side_effect = Exception("Test error")
        mock_outlines.generate.json.return_value = mock_generator
        
        result = await provider.generate_json(
            prompt="This will fail",
            schema=TestUser
        )
        
        assert result["success"] is False
        assert "Test error" in result["error"]
        assert result["provider"] == "outlines"


class TestOutlinesProviderFactory:
    """Test Outlines Provider Factory."""

    @patch('plugginger.plugins.core.structured_generation.services.outlines_provider.OutlinesProvider')
    def test_create_transformers_provider(self, mock_provider_class: MagicMock) -> None:
        """Test creating transformers provider."""
        OutlinesProviderFactory.create_transformers_provider(
            "gpt2", device="cuda", custom_param="test"
        )
        
        mock_provider_class.assert_called_once_with(
            model_name="gpt2",
            provider="transformers",
            device="cuda",
            custom_param="test"
        )

    @patch('plugginger.plugins.core.structured_generation.services.outlines_provider.OutlinesProvider')
    def test_create_openai_provider(self, mock_provider_class: MagicMock) -> None:
        """Test creating OpenAI provider."""
        OutlinesProviderFactory.create_openai_provider(
            "gpt-4", api_key="test-key"
        )
        
        mock_provider_class.assert_called_once_with(
            model_name="gpt-4",
            provider="openai",
            api_key="test-key"
        )

    @patch('plugginger.plugins.core.structured_generation.services.outlines_provider.OutlinesProvider')
    def test_create_llamacpp_provider(self, mock_provider_class: MagicMock) -> None:
        """Test creating llama.cpp provider."""
        OutlinesProviderFactory.create_llamacpp_provider(
            "/path/to/model.gguf", n_ctx=2048
        )
        
        mock_provider_class.assert_called_once_with(
            model_name="/path/to/model.gguf",
            provider="llamacpp",
            n_ctx=2048
        )


class TestSchemaService:
    """Test Schema Service functionality."""

    @pytest.fixture
    def schema_service(self) -> SchemaService:
        """Create test schema service."""
        return SchemaService()

    def test_register_schema(self, schema_service: SchemaService) -> None:
        """Test schema registration."""
        schema = {
            "type": "object",
            "properties": {"name": {"type": "string"}}
        }
        
        schema_service.register_schema("test_schema", schema)
        
        assert schema_service.get_schema("test_schema") == schema

    def test_register_pydantic_model(self, schema_service: SchemaService) -> None:
        """Test Pydantic model registration."""
        schema_service.register_pydantic_model("test_user", TestUser)
        
        assert schema_service.get_pydantic_model("test_user") == TestUser

    def test_pydantic_to_schema(self, schema_service: SchemaService) -> None:
        """Test Pydantic to JSON schema conversion."""
        schema = schema_service.pydantic_to_schema(TestUser)
        
        assert schema["type"] == "object"
        assert "name" in schema["properties"]
        assert "age" in schema["properties"]
        assert "email" in schema["properties"]

    def test_validate_with_schema_valid(self, schema_service: SchemaService) -> None:
        """Test schema validation with valid data."""
        schema = {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "number"}
            },
            "required": ["name"]
        }
        
        data = {"name": "John", "age": 30}
        result = schema_service.validate_with_schema(data, schema)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
        assert result.validated_data == data

    def test_validate_with_schema_invalid(self, schema_service: SchemaService) -> None:
        """Test schema validation with invalid data."""
        schema = {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "number"}
            },
            "required": ["name", "age"]
        }
        
        data = {"name": "John"}  # Missing required age
        result = schema_service.validate_with_schema(data, schema)

        assert result.is_valid is False
        # Check for missing field error (may vary based on jsonschema availability)
        assert len(result.errors) > 0
        assert any("age" in error.lower() for error in result.errors)

    def test_validate_with_pydantic_valid(self, schema_service: SchemaService) -> None:
        """Test Pydantic validation with valid data."""
        data = {"name": "John", "age": 30, "email": "<EMAIL>"}
        result = schema_service.validate_with_pydantic(data, TestUser)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
        assert isinstance(result.validated_data, TestUser)

    def test_validate_with_pydantic_invalid(self, schema_service: SchemaService) -> None:
        """Test Pydantic validation with invalid data."""
        data = {"name": "John", "age": "thirty"}  # Invalid age type
        result = schema_service.validate_with_pydantic(data, TestUser)
        
        assert result.is_valid is False
        assert len(result.errors) > 0

    def test_create_common_schemas(self, schema_service: SchemaService) -> None:
        """Test creation of common schemas."""
        schema_service.create_common_schemas()
        
        assert schema_service.get_schema("user_profile") is not None
        assert schema_service.get_schema("api_response") is not None
        assert schema_service.get_schema("code_generation") is not None


class TestGrammarService:
    """Test Grammar Service functionality."""

    @pytest.fixture
    def grammar_service(self) -> GrammarService:
        """Create test grammar service."""
        return GrammarService()

    def test_register_grammar(self, grammar_service: GrammarService) -> None:
        """Test grammar registration."""
        grammar = EBNFGrammar(
            name="test_grammar",
            grammar="start: 'hello'",
            description="Test grammar"
        )
        
        grammar_service.register_grammar(grammar)
        
        assert grammar_service.get_grammar("test_grammar") == grammar

    def test_list_grammars(self, grammar_service: GrammarService) -> None:
        """Test listing grammars."""
        grammars = grammar_service.list_grammars()
        
        # Should include common grammars
        assert "arithmetic" in grammars
        assert "json" in grammars
        assert "python_function" in grammars

    def test_validate_grammar_syntax_valid(self, grammar_service: GrammarService) -> None:
        """Test grammar syntax validation with valid grammar."""
        grammar = """
            start: expression
            expression: NUMBER
            %import common.NUMBER
        """
        
        result = grammar_service.validate_grammar_syntax(grammar)
        
        assert result.is_valid is True
        assert len(result.errors) == 0

    def test_validate_grammar_syntax_invalid(self, grammar_service: GrammarService) -> None:
        """Test grammar syntax validation with invalid grammar."""
        grammar = """
            start expression  # Missing colon
            expression: undefined_rule
        """
        
        result = grammar_service.validate_grammar_syntax(grammar)
        
        assert result.is_valid is False
        assert len(result.errors) > 0

    def test_common_grammars_available(self, grammar_service: GrammarService) -> None:
        """Test that common grammars are available."""
        arithmetic = grammar_service.get_grammar("arithmetic")
        assert arithmetic is not None
        assert "expression" in arithmetic.grammar
        
        json_grammar = grammar_service.get_grammar("json")
        assert json_grammar is not None
        assert "object" in json_grammar.grammar
