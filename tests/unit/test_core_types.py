# tests/unit/test_core_types.py

"""
Unit tests for plugginger.core.types module.

Tests all type definitions and type aliases to ensure they are properly
defined and have the expected characteristics.
"""

from __future__ import annotations

from typing import Any, ParamSpec, TypeVar, get_args, get_origin

from plugginger.core.types import (
    APP_BUILDER_TYPE,
    APP_INSTANCE_TYPE,
    DI_CONTAINER_TYPE,
    PLUGIN_BASE_TYPE,
    AnyDict,
    AnyList,
    ConfigMetadata,
    ConfigValue,
    DependencyMetadata,
    EventBridgeConfigEntryType,
    EventBridgeListType,
    EventData,
    EventDataTransformer,
    EventHandlerType,
    EventListenerMetadata,
    EventListenerMethodName,
    EventPattern,
    EventPatternInput,
    EventPatternList,
    EventType,
    JsonValue,
    LoggerCallable,
    Optional,
    P,
    PluginClass,
    PluginDependencyList,
    PluginInstance,
    PluginInstanceId,
    PluginMetadata,
    PluginNameList,
    R,
    ServiceMetadata,
    ServiceMethodType,
    ServiceName,
    StringAnyTuple,
    StringDict,
    StringList,
    TupleList,
)


class TestGenericTypeVariables:
    """Test generic type variables."""

    def test_r_type_variable(self) -> None:
        """Test R TypeVar is properly defined."""
        assert isinstance(R, TypeVar)
        assert R.__name__ == "R"

    def test_p_param_spec(self) -> None:
        """Test P ParamSpec is properly defined."""
        assert isinstance(P, ParamSpec)
        assert P.__name__ == "P"

    def test_optional_type_variable(self) -> None:
        """Test Optional TypeVar is properly defined."""
        assert isinstance(Optional, TypeVar)
        assert Optional.__name__ == "Optional"


class TestCoreTypeAliases:
    """Test core type aliases."""

    def test_logger_callable_type(self) -> None:
        """Test LoggerCallable type alias."""
        # Test that it's a callable type
        origin = get_origin(LoggerCallable)
        assert origin is not None

        # Test the signature: Callable[[str], None]
        args = get_args(LoggerCallable)
        assert len(args) == 2
        assert args[0] == [str]  # Parameters
        assert args[1] is None  # Return type (None, not type(None))

    def test_service_method_type(self) -> None:
        """Test ServiceMethodType type alias."""
        # Should be Callable[P, Awaitable[R]]
        origin = get_origin(ServiceMethodType)
        assert origin is not None

        args = get_args(ServiceMethodType)
        assert len(args) == 2
        assert args[0] is P  # Parameters
        # args[1] should be Awaitable[R]
        awaitable_origin = get_origin(args[1])
        assert awaitable_origin is not None

    def test_event_handler_type(self) -> None:
        """Test EventHandlerType type alias."""
        # Should be Callable[..., Awaitable[None]]
        origin = get_origin(EventHandlerType)
        assert origin is not None

        args = get_args(EventHandlerType)
        assert len(args) == 2
        assert args[0] is ...  # Ellipsis for variable args
        # args[1] should be Awaitable[None]
        awaitable_origin = get_origin(args[1])
        assert awaitable_origin is not None

    def test_event_pattern_input_type(self) -> None:
        """Test EventPatternInput type alias."""
        # Should be str | Sequence[str]
        args = get_args(EventPatternInput)

        # Check that it's a union type
        assert len(args) == 2
        assert str in args

        # Check that one of the args is Sequence[str]
        sequence_type = None
        for arg in args:
            if get_origin(arg) is not None:
                sequence_type = arg
                break

        assert sequence_type is not None
        sequence_args = get_args(sequence_type)
        assert str in sequence_args


class TestEventBridgeTypes:
    """Test event bridge related types."""

    def test_event_data_transformer_type(self) -> None:
        """Test EventDataTransformer type alias."""
        # Should be Callable[[dict[str, Any], str], dict[str, Any]]
        origin = get_origin(EventDataTransformer)
        assert origin is not None

        args = get_args(EventDataTransformer)
        assert len(args) == 2
        # First arg should be [dict[str, Any], str]
        params = args[0]
        assert len(params) == 2
        # Return type should be dict[str, Any]
        return_type = args[1]
        assert get_origin(return_type) is dict

    def test_event_bridge_config_entry_type(self) -> None:
        """Test EventBridgeConfigEntryType type alias."""
        # Should be dict[str, Any]
        assert EventBridgeConfigEntryType == dict[str, Any]

    def test_event_bridge_list_type(self) -> None:
        """Test EventBridgeListType type alias."""
        # Should be list[EventBridgeConfigEntryType]
        origin = get_origin(EventBridgeListType)
        assert origin is list

        args = get_args(EventBridgeListType)
        assert len(args) == 1
        assert args[0] == EventBridgeConfigEntryType


class TestInstanceIdTypes:
    """Test instance ID related types."""

    def test_plugin_instance_id_type(self) -> None:
        """Test PluginInstanceId type alias."""
        assert PluginInstanceId is str

    def test_service_name_type(self) -> None:
        """Test ServiceName type alias."""
        assert ServiceName is str

    def test_event_type_type(self) -> None:
        """Test EventType type alias."""
        assert EventType is str


class TestEventSystemTypes:
    """Test event system related types."""

    def test_event_data_type(self) -> None:
        """Test EventData type alias."""
        assert EventData == dict[str, Any]

    def test_event_pattern_type(self) -> None:
        """Test EventPattern type alias."""
        assert EventPattern is str

    def test_event_pattern_list_type(self) -> None:
        """Test EventPatternList type alias."""
        origin = get_origin(EventPatternList)
        assert origin is list

        args = get_args(EventPatternList)
        assert len(args) == 1
        assert args[0] == EventPattern

    def test_event_listener_method_name_type(self) -> None:
        """Test EventListenerMethodName type alias."""
        assert EventListenerMethodName is str


class TestMetadataTypes:
    """Test metadata related types."""

    def test_plugin_metadata_type(self) -> None:
        """Test PluginMetadata type alias."""
        assert PluginMetadata == dict[str, Any]

    def test_service_metadata_type(self) -> None:
        """Test ServiceMetadata type alias."""
        assert ServiceMetadata == dict[str, Any]

    def test_event_listener_metadata_type(self) -> None:
        """Test EventListenerMetadata type alias."""
        assert EventListenerMetadata == dict[str, Any]

    def test_dependency_metadata_type(self) -> None:
        """Test DependencyMetadata type alias."""
        assert DependencyMetadata == dict[str, Any]

    def test_config_metadata_type(self) -> None:
        """Test ConfigMetadata type alias."""
        assert ConfigMetadata == dict[str, Any]


class TestUtilityTypes:
    """Test utility types."""

    def test_json_value_type(self) -> None:
        """Test JsonValue type alias."""
        # Should be str | int | float | bool | None | dict[str, Any] | list[Any]
        args = get_args(JsonValue)

        # Check that it includes basic JSON types
        expected_types = {str, int, float, bool, type(None)}
        for expected_type in expected_types:
            assert expected_type in args

    def test_config_value_type(self) -> None:
        """Test ConfigValue type alias."""
        assert ConfigValue == JsonValue


class TestCollectionTypes:
    """Test collection types."""

    def test_string_list_type(self) -> None:
        """Test StringList type alias."""
        origin = get_origin(StringList)
        assert origin is list

        args = get_args(StringList)
        assert len(args) == 1
        assert args[0] is str

    def test_string_dict_type(self) -> None:
        """Test StringDict type alias."""
        origin = get_origin(StringDict)
        assert origin is dict

        args = get_args(StringDict)
        assert len(args) == 2
        assert args[0] is str
        assert args[1] is str

    def test_any_dict_type(self) -> None:
        """Test AnyDict type alias."""
        origin = get_origin(AnyDict)
        assert origin is dict

        args = get_args(AnyDict)
        assert len(args) == 2
        assert args[0] is str
        assert args[1] is Any

    def test_any_list_type(self) -> None:
        """Test AnyList type alias."""
        origin = get_origin(AnyList)
        assert origin is list

        args = get_args(AnyList)
        assert len(args) == 1
        assert args[0] == Any

    def test_string_any_tuple_type(self) -> None:
        """Test StringAnyTuple type alias."""
        origin = get_origin(StringAnyTuple)
        assert origin is tuple

        args = get_args(StringAnyTuple)
        assert len(args) == 2
        assert args[0] is str
        assert args[1] is Any

    def test_tuple_list_type(self) -> None:
        """Test TupleList type alias."""
        origin = get_origin(TupleList)
        assert origin is list

        args = get_args(TupleList)
        assert len(args) == 1
        # args[0] should be tuple[str, Any]
        tuple_type = args[0]
        tuple_origin = get_origin(tuple_type)
        assert tuple_origin is tuple


class TestPluginSystemTypes:
    """Test plugin system types."""

    def test_plugin_class_type(self) -> None:
        """Test PluginClass type alias."""
        origin = get_origin(PluginClass)
        assert origin is type

        args = get_args(PluginClass)
        assert len(args) == 1
        assert args[0] == 'PluginBase'  # Forward reference string

    def test_plugin_instance_type(self) -> None:
        """Test PluginInstance type alias."""
        assert PluginInstance == 'PluginBase'  # Forward reference string

    def test_plugin_name_list_type(self) -> None:
        """Test PluginNameList type alias."""
        origin = get_origin(PluginNameList)
        assert origin is list

        args = get_args(PluginNameList)
        assert len(args) == 1
        assert args[0] is str

    def test_plugin_dependency_list_type(self) -> None:
        """Test PluginDependencyList type alias."""
        origin = get_origin(PluginDependencyList)
        assert origin is list

        args = get_args(PluginDependencyList)
        assert len(args) == 1
        assert args[0] == 'Depends'  # Forward reference string


class TestForwardReferenceStrings:
    """Test forward reference string constants."""

    def test_plugin_base_type_string(self) -> None:
        """Test PLUGIN_BASE_TYPE constant."""
        assert isinstance(PLUGIN_BASE_TYPE, str)
        assert PLUGIN_BASE_TYPE == "PluginBase"

    def test_app_instance_type_string(self) -> None:
        """Test APP_INSTANCE_TYPE constant."""
        assert isinstance(APP_INSTANCE_TYPE, str)
        assert APP_INSTANCE_TYPE == "PluggingerAppInstance"

    def test_app_builder_type_string(self) -> None:
        """Test APP_BUILDER_TYPE constant."""
        assert isinstance(APP_BUILDER_TYPE, str)
        assert APP_BUILDER_TYPE == "PluggingerAppBuilder"

    def test_di_container_type_string(self) -> None:
        """Test DI_CONTAINER_TYPE constant."""
        assert isinstance(DI_CONTAINER_TYPE, str)
        assert DI_CONTAINER_TYPE == "DIContainer"

    def test_forward_reference_strings_are_unique(self) -> None:
        """Test that all forward reference strings are unique."""
        references = [
            PLUGIN_BASE_TYPE,
            APP_INSTANCE_TYPE,
            APP_BUILDER_TYPE,
            DI_CONTAINER_TYPE,
        ]
        assert len(references) == len(set(references))

    def test_forward_reference_strings_are_non_empty(self) -> None:
        """Test that all forward reference strings are non-empty."""
        references = [
            PLUGIN_BASE_TYPE,
            APP_INSTANCE_TYPE,
            APP_BUILDER_TYPE,
            DI_CONTAINER_TYPE,
        ]
        for ref in references:
            assert len(ref) > 0
