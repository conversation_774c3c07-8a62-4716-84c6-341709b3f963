"""
Global pytest configuration and fixtures.
"""
import asyncio
import logging
import os
import pytest
import warnings
from typing import Any, Dict, Generator, Optional

from plugginger.core.env_loader import get_env_loader, has_api_key, get_api_key

# Suppress warnings
warnings.filterwarnings("ignore", category=ResourceWarning, message=".*Unclosed client session.*")
warnings.filterwarnings("ignore", category=ResourceWarning, message=".*Unclosed connector.*")
warnings.filterwarnings("ignore", category=UserWarning, module="outlines")
warnings.filterwarnings("ignore", category=FutureWarning, module="transformers")

# Configure logging for tests
logging.basicConfig(level=logging.WARNING)
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("openai").setLevel(logging.WARNING)
logging.getLogger("litellm").setLevel(logging.WARNING)


@pytest.fixture(scope="session")
def env_loader() -> Any:
    """Get environment loader for tests."""
    loader = get_env_loader()
    loader.create_env_file_if_missing()
    return loader


@pytest.fixture(scope="session")
def test_config(env_loader: Any) -> Dict[str, Any]:
    """Get test configuration."""
    return env_loader.get_test_config()


@pytest.fixture(scope="session")
def available_providers(env_loader: Any) -> Dict[str, bool]:
    """Get available providers based on API keys."""
    return env_loader.get_available_providers()


@pytest.fixture
def skip_if_no_openai() -> None:
    """Skip test if OpenAI API key is not available."""
    if not has_api_key("openai"):
        pytest.skip("OpenAI API key not available. Set OPENAI_API_KEY in .env file.")


@pytest.fixture
def skip_if_no_google() -> None:
    """Skip test if Google API key is not available."""
    if not has_api_key("google"):
        pytest.skip("Google API key not available. Set GOOGLE_API_KEY in .env file.")


@pytest.fixture
def skip_if_no_groq() -> None:
    """Skip test if Groq API key is not available."""
    if not has_api_key("groq"):
        pytest.skip("Groq API key not available. Set GROQ_API_KEY in .env file.")


@pytest.fixture
def skip_if_no_integration_tests(env_loader: Any) -> None:
    """Skip test if integration tests are disabled."""
    if not env_loader.should_run_integration_tests():
        pytest.skip("Integration tests disabled. Set RUN_INTEGRATION_TESTS=true in .env file.")


@pytest.fixture
def skip_if_no_e2e_tests(env_loader: Any) -> None:
    """Skip test if E2E tests are disabled."""
    if not env_loader.should_run_e2e_tests():
        pytest.skip("E2E tests disabled. Set RUN_E2E_TESTS=true in .env file.")


@pytest.fixture
def openai_api_key() -> Optional[str]:
    """Get OpenAI API key."""
    return get_api_key("openai")


@pytest.fixture
def google_api_key() -> Optional[str]:
    """Get Google API key."""
    return get_api_key("google")


@pytest.fixture
def groq_api_key() -> Optional[str]:
    """Get Groq API key."""
    return get_api_key("groq")


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(autouse=True)
async def cleanup_aiohttp_sessions() -> None:
    """Cleanup aiohttp sessions after each test."""
    yield
    
    # Force garbage collection to cleanup any remaining sessions
    import gc
    gc.collect()
    
    # Give a moment for cleanup
    await asyncio.sleep(0.1)


@pytest.fixture
def mock_env_vars(monkeypatch: pytest.MonkeyPatch) -> Dict[str, str]:
    """Mock environment variables for testing."""
    mock_vars = {
        "OPENAI_API_KEY": "sk-test-key-for-testing-only",
        "GOOGLE_API_KEY": "test-google-key-for-testing",
        "GROQ_API_KEY": "gsk_test-groq-key-for-testing"
    }
    
    for key, value in mock_vars.items():
        monkeypatch.setenv(key, value)
    
    return mock_vars


# Pytest configuration
def pytest_configure(config: pytest.Config) -> None:
    """Configure pytest."""
    # Add custom markers
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "e2e: mark test as end-to-end test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


def pytest_collection_modifyitems(config: pytest.Config, items: list) -> None:
    """Modify test collection."""
    env_loader = get_env_loader()
    
    # Skip integration tests if not enabled
    if not env_loader.should_run_integration_tests():
        skip_integration = pytest.mark.skip(reason="Integration tests disabled")
        for item in items:
            if "integration" in item.keywords:
                item.add_marker(skip_integration)
    
    # Skip E2E tests if not enabled  
    if not env_loader.should_run_e2e_tests():
        skip_e2e = pytest.mark.skip(reason="E2E tests disabled")
        for item in items:
            if "e2e" in item.keywords:
                item.add_marker(skip_e2e)


def pytest_runtest_setup(item: pytest.Item) -> None:
    """Setup for each test."""
    # Check for API key requirements
    if "openai" in item.keywords and not has_api_key("openai"):
        pytest.skip("OpenAI API key not available")
    
    if "google" in item.keywords and not has_api_key("google"):
        pytest.skip("Google API key not available")
    
    if "groq" in item.keywords and not has_api_key("groq"):
        pytest.skip("Groq API key not available")


@pytest.fixture(autouse=True)
def suppress_warnings() -> None:
    """Suppress common warnings during tests."""
    warnings.filterwarnings("ignore", category=DeprecationWarning)
    warnings.filterwarnings("ignore", category=PendingDeprecationWarning)
    warnings.filterwarnings("ignore", message=".*Unclosed client session.*")
    warnings.filterwarnings("ignore", message=".*Unclosed connector.*")


# Session cleanup
@pytest.fixture(scope="session", autouse=True)
async def session_cleanup() -> None:
    """Cleanup at end of test session."""
    yield
    
    # Final cleanup
    import gc
    gc.collect()
    
    # Close any remaining event loops
    try:
        loop = asyncio.get_running_loop()
        if loop and not loop.is_closed():
            await asyncio.sleep(0.1)
    except RuntimeError:
        pass
