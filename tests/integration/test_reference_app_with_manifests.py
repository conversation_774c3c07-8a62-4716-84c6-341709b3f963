"""
Integration test for Reference App with enabled manifests.

Tests that the AI Chat Reference App can successfully start and run
with manifest loading enabled, using the new ManifestConverter to
handle user-friendly YAML manifests.
"""

from __future__ import annotations

import sys
from pathlib import Path
from typing import Any

import pytest

from plugginger.api.app import PluggingerAppInstance
from plugginger.api.builder import PluggingerAppBuilder
from plugginger.api.plugin import PluginB<PERSON>, plugin
from plugginger.api.service import service
from plugginger.config.models import GlobalAppConfig


# Mock plugins for testing instead of using the complex reference app
@plugin(name="memory_store", version="1.0.0")
class MockMemoryStorePlugin(PluginBase):
    """Mock memory store plugin for testing."""

    @service("create_conversation")
    async def create_conversation(self, title: str) -> str:
        """Create a new conversation."""
        return f"conv_{hash(title)}"

    @service("store_message")
    async def store_message(self, conversation_id: str, role: str, content: str) -> None:
        """Store a message in conversation."""
        pass

    @service("get_conversation_history")
    async def get_conversation_history(self, conversation_id: str) -> list[dict[str, str]]:
        """Get conversation history."""
        return [{"role": "user", "content": "Hello, this is a test message"}]

@plugin(name="chat_ai", version="1.0.0")
class MockChatAIPlugin(PluginBase):
    """Mock chat AI plugin for testing."""

    @service("generate_response")
    async def generate_response(self, message: str) -> str:
        """Generate AI response."""
        return f"AI response to: {message}"

    @service("get_model_info")
    async def get_model_info(self) -> dict[str, str]:
        """Get model information."""
        return {"model": "mock-gpt", "version": "1.0"}

@plugin(name="web_api", version="1.0.0")
class MockWebAPIPlugin(PluginBase):
    """Mock web API plugin for testing."""

    @service("get_health_status")
    async def get_health_status(self) -> dict[str, str]:
        """Get health status."""
        return {"status": "healthy"}

def create_mock_app(enable_manifests: bool = True) -> PluggingerAppInstance:
    """Create a mock reference app for testing."""

    builder = PluggingerAppBuilder(app_name="ai_chat_reference")

    if enable_manifests:
        builder.enable_manifest_loading(require_manifests=False)

    builder.include(MockMemoryStorePlugin)
    builder.include(MockChatAIPlugin)
    builder.include(MockWebAPIPlugin)

    global_config = GlobalAppConfig(max_fractal_depth=5)
    return builder.build(global_config)

# Try to import the real reference app, fall back to mock
reference_app_path = Path(__file__).parent.parent.parent / "examples" / "ai-chat-reference"
REFERENCE_APP_AVAILABLE = False

if reference_app_path.exists():
    sys.path.insert(0, str(reference_app_path))
    try:
        from app import create_app, start_app, stop_app
        REFERENCE_APP_AVAILABLE = True
    except ImportError:
        # Fall back to mock
        create_app = create_mock_app

        async def start_app(app: PluggingerAppInstance, start_web_server: bool = True, **kwargs: Any) -> None:
            await app.start_all_plugins()

        async def stop_app(app: PluggingerAppInstance) -> None:
            await app.stop_all_plugins()
else:
    # Use mock functions
    create_app = create_mock_app

    async def start_app(app: PluggingerAppInstance, start_web_server: bool = True, **kwargs: Any) -> None:
        await app.start_all_plugins()

    async def stop_app(app: PluggingerAppInstance) -> None:
        await app.stop_all_plugins()


class TestReferenceAppWithManifests:
    """Test cases for Reference App with manifest loading enabled."""

    def test_create_app_with_manifests_enabled(self) -> None:
        """Test creating the Reference App with manifest loading enabled."""
        try:
            from app import create_app
        except ImportError:
            pytest.skip("Reference app not available")

        # Create app with manifests enabled
        app = create_app(enable_manifests=True)

        # Verify app was created successfully
        assert app is not None
        assert app.app_name == "ai_chat_reference"

        # Verify plugins were loaded by checking registered plugin classes
        registered_plugins = app._registered_plugin_classes
        assert len(registered_plugins) >= 3  # memory_store, chat_ai, web_api

        plugin_names = list(registered_plugins.keys())
        assert "memory_store" in plugin_names
        assert "chat_ai" in plugin_names
        assert "web_api" in plugin_names

    def test_app_services_available_with_manifests(self) -> None:
        """Test that all expected services are available when manifests are enabled."""
        try:
            from app import create_app
        except ImportError:
            pytest.skip("Reference app not available")

        app = create_app(enable_manifests=True)

        # Get all available services
        services = app.list_services()

        # Verify memory_store services
        memory_services = [s for s in services if s.startswith("memory_store.")]
        assert len(memory_services) >= 7  # Expected number of memory store services

        expected_memory_services = [
            "memory_store.create_conversation",
            "memory_store.store_message",
            "memory_store.get_conversation_history",
            "memory_store.list_conversations",
            "memory_store.get_conversation_info",
            "memory_store.delete_conversation",
            "memory_store.get_storage_stats"
        ]

        for expected_service in expected_memory_services:
            assert expected_service in services, f"Missing service: {expected_service}"

        # Verify chat_ai services
        chat_services = [s for s in services if s.startswith("chat_ai.")]
        assert len(chat_services) >= 3  # Expected number of chat AI services

        expected_chat_services = [
            "chat_ai.generate_response",
            "chat_ai.get_model_info",
            "chat_ai.set_default_model"
        ]

        for expected_service in expected_chat_services:
            assert expected_service in services, f"Missing service: {expected_service}"

    @pytest.mark.asyncio
    async def test_app_startup_with_manifests(self) -> None:
        """Test that the app can start successfully with manifests enabled."""
        try:
            from app import create_app, start_app, stop_app
        except ImportError:
            pytest.skip("Reference app not available")

        app = create_app(enable_manifests=True)

        try:
            # Start the app (without web server to avoid port conflicts)
            await start_app(app, start_web_server=False)

            # Verify app is running (check if runtime facade is available)
            assert app.get_runtime_facade() is not None

            # Test a basic service call
            conversation_id = await app.call_service(
                "memory_store.create_conversation",
                title="Test Conversation"
            )
            assert conversation_id is not None
            assert isinstance(conversation_id, str)

            # Test storing a message
            await app.call_service(
                "memory_store.store_message",
                conversation_id=conversation_id,
                role="user",
                content="Hello, this is a test message"
            )

            # Test retrieving conversation history
            history = await app.call_service(
                "memory_store.get_conversation_history",
                conversation_id=conversation_id
            )
            assert isinstance(history, list)
            assert len(history) == 1
            assert history[0]["content"] == "Hello, this is a test message"

        finally:
            # Clean shutdown
            await stop_app(app)

    def test_manifest_validation_success(self) -> None:
        """Test that all reference app manifests pass validation."""
        try:
            from app import create_app
        except ImportError:
            pytest.skip("Reference app not available")

        # This should not raise any validation errors
        app = create_app(enable_manifests=True)

        # Verify that manifests were loaded successfully
        # (If there were validation errors, the app creation would have failed)
        assert app is not None

        # Verify that services from manifests are available
        services = app.list_services()
        assert len(services) > 0

    def test_manifest_vs_code_consistency(self) -> None:
        """Test that manifest-defined services match actual plugin code."""
        try:
            from app import create_app
        except ImportError:
            pytest.skip("Reference app not available")

        # Create app with manifests enabled
        app_with_manifests = create_app(enable_manifests=True)
        services_with_manifests = set(app_with_manifests.list_services())

        # Create app without manifests
        app_without_manifests = create_app(enable_manifests=False)
        services_without_manifests = set(app_without_manifests.list_services())

        # Services should be the same regardless of manifest loading
        # (Manifests should describe what's actually in the code)
        assert services_with_manifests == services_without_manifests

    def test_dependency_resolution_with_manifests(self) -> None:
        """Test that plugin dependencies are resolved correctly with manifests."""
        try:
            from app import create_app
        except ImportError:
            pytest.skip("Reference app not available")

        app = create_app(enable_manifests=True)

        # Verify that chat_ai plugin has access to memory_store services
        # (This tests that dependency resolution worked correctly)
        services = app.list_services()

        # Both plugins should have their services available
        memory_services = [s for s in services if s.startswith("memory_store.")]
        chat_services = [s for s in services if s.startswith("chat_ai.")]

        assert len(memory_services) > 0, "Memory store services not available"
        assert len(chat_services) > 0, "Chat AI services not available"
