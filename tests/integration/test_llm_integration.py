"""
Integration tests for the LiteLLMProvider using real API calls.

These tests verify that the unified provider can successfully connect to various
LLM services and handle responses correctly. They require environment variables
for API keys to be set.
"""

import os
import json
import pytest
from typing import Dict, Any

# Import the class we are testing
from plugginger.plugins.core.llm_provider.services.litellm_provider import Lite<PERSON><PERSON>rovider

# --- Test Configuration ---

# Define providers and their required environment variables and models
# This makes it easy to add more providers to the test suite
PROVIDERS_TO_TEST = [
    pytest.param(
        {
            "provider_name": "openai",
            "api_key_env": "OPENAI_API_KEY",
            "model": "gpt-4.1",
        },
        id="openai",
        marks=pytest.mark.skipif(not os.getenv("OPENAI_API_KEY"), reason="OPENAI_API_KEY not set")
    ),
    pytest.param(
        {
            "provider_name": "gemini",
            "api_key_env": "GOOGLE_API_KEY",
            "model": "gemini-1.5-flash",
        },
        id="gemini",
        marks=pytest.mark.skipif(not os.getenv("GOOGLE_API_KEY"), reason="GOOGLE_API_KEY not set")
    ),
    pytest.param(
        {
            "provider_name": "groq",
            "api_key_env": "GROQ_API_KEY",
            "model": "llama3-8b-8192",
        },
        id="groq",
        marks=pytest.mark.skipif(not os.getenv("GROQ_API_KEY"), reason="GROQ_API_KEY not set")
    ),
]

# --- Test Class ---

class TestLLMProviderIntegration:
    """
    Integration tests for the LiteLLMProvider making real API calls.
    """

    @pytest.mark.parametrize("provider_config", PROVIDERS_TO_TEST)
    @pytest.mark.asyncio
    async def test_successful_text_generation(self, litellm_provider_session: LiteLLMProvider, provider_config: Dict[str, str]) -> None:
        """
        GIVEN a configured provider with a valid API key.
        WHEN generate_text is called.
        THEN it should return a successful response with text content.
        """
        # Reconfigure the session-scoped provider for the current test's provider_config
        litellm_provider_session.provider = provider_config["provider_name"]
        litellm_provider_session.model = provider_config["model"]
        litellm_provider_session.api_key = os.getenv(provider_config["api_key_env"])
        litellm_provider_session._configure_litellm() # Re-apply LiteLLM global configs if needed

        result = await litellm_provider_session.generate_text(
            prompt="Say 'Hello, Plugginger!' in exactly 3 words.",
            max_tokens=15
        )

        assert result["success"] is True, f"API call for {litellm_provider_session.provider} failed: {result.get('error')}"
        assert isinstance(result.get("content"), str) and len(result["content"]) > 5
        assert result.get("tokens_used", 0) > 0
        assert result["provider"] == provider_config["provider_name"]

    @pytest.mark.parametrize("provider_config", PROVIDERS_TO_TEST)
    @pytest.mark.asyncio
    async def test_successful_structured_generation(self, litellm_provider_session: LiteLLMProvider, provider_config: Dict[str, str]) -> None:
        """
        GIVEN a configured provider with a valid API key.
        WHEN generate_structured is called with JSON mode.
        THEN it should return a valid JSON string.
        """
        # Reconfigure the session-scoped provider for the current test's provider_config
        litellm_provider_session.provider = provider_config["provider_name"]
        litellm_provider_session.model = provider_config["model"]
        litellm_provider_session.api_key = os.getenv(provider_config["api_key_env"])
        litellm_provider_session._configure_litellm() # Re-apply LiteLLM global configs if needed

        result = await litellm_provider_session.generate_structured(
            system_message="You are a helpful assistant that only responds in valid JSON.",
            user_message="Create a JSON object with a 'plugin' key set to 'plugginger' and a 'status' key set to 'ok'."
        )

        assert result["success"] is True, f"API call for {litellm_provider_session.provider} failed: {result.get('error')}"
        assert result["validated"] is True
        
        try:
            data = json.loads(result["content"])
            assert isinstance(data, dict)
            assert data.get("plugin") == "plugginger"
            assert data.get("status") == "ok"
        except (json.JSONDecodeError, AssertionError) as e:
            pytest.fail(f"Failed to validate structured JSON from {litellm_provider_session.provider}: {e}\nContent: {result['content']}")

    @pytest.mark.asyncio
    async def test_ollama_generation_if_available(self, litellm_provider_session: LiteLLMProvider) -> None:
        """
        GIVEN an Ollama provider.
        WHEN a request is made.
        THEN it should succeed if Ollama is running, otherwise fail gracefully.
        """
        try:
            import socket
            with socket.create_connection(("localhost", 11434), timeout=1):
                pass
        except (socket.timeout, ConnectionRefusedError, OSError):
            pytest.skip("Ollama server not available on http://localhost:11434")

        # Reconfigure the session-scoped provider for Ollama
        litellm_provider_session.provider = "ollama"
        litellm_provider_session.model = "qwen2.5-coder:7b"
        litellm_provider_session.api_key = None # Ollama typically doesn't use an API key
        litellm_provider_session._configure_litellm()

        result = await litellm_provider_session.generate_text(prompt="Hello!")
        
        assert result["success"] is True, f"Ollama call failed unexpectedly: {result.get('error')}"
        assert len(result["content"]) > 0

    @pytest.mark.asyncio
    async def test_provider_fails_with_invalid_key(self, litellm_provider_session: LiteLLMProvider) -> None:
        """
        GIVEN a provider with a deliberately invalid API key.
        WHEN a request is made.
        THEN the call should fail with a clear authentication-related error.
        """
        # Reconfigure the session-scoped provider for an invalid key test
        litellm_provider_session.provider = "openai"
        litellm_provider_session.model = "gpt-4o-mini"
        litellm_provider_session.api_key = "sk-invalid-key-for-testing-12345"
        litellm_provider_session._configure_litellm()

        result = await litellm_provider_session.generate_text("This should fail")

        assert result["success"] is False
        assert "error" in result
        error_message = result["error"].lower()
        assert "incorrect api key" in error_message or "authenticationerror" in error_message
