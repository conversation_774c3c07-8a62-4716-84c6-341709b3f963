"""
Integration tests for LiteLLM structured generation with Outlines backend.
"""
import json
import pytest
from typing import Any, Dict, List
from enum import Enum

from pydantic import BaseModel

from plugginger.plugins.core.llm_provider.services.litellm_provider import Lite<PERSON><PERSON>rovider
from plugginger.plugins.core.structured_generation.services.outlines_provider import OutlinesProvider
from plugginger.plugins.core.structured_generation.services.schema_service import SchemaService
from plugginger.plugins.core.structured_generation.services.grammar_service import GrammarService


class UserProfile(BaseModel):
    """Test user profile model."""
    name: str
    age: int
    email: str
    bio: str = "No bio provided"
    active: bool = True


class TaskStatus(str, Enum):
    """Test task status enum."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class TestLiteLLMStructuredGeneration:
    """Integration tests for LiteLLM structured generation with Outlines backend."""

    @pytest.fixture
    def openai_provider(self, litellm_provider_session: Lite<PERSON><PERSON>rovider, outlines_provider_session: OutlinesProvider, skip_if_no_openai: None, openai_api_key: str) -> LiteLLMProvider:
        """Create LiteLLM provider for OpenAI, using session-scoped fixtures."""
        litellm_provider_session.provider = "openai"
        litellm_provider_session.model = "gpt-4o-mini"
        litellm_provider_session.api_key = openai_api_key
        litellm_provider_session._configure_litellm()
        litellm_provider_session._outlines_provider = outlines_provider_session # Inject the session-scoped outlines provider
        return litellm_provider_session

    @pytest.fixture
    def groq_provider(self, litellm_provider_session: LiteLLMProvider, outlines_provider_session: OutlinesProvider, skip_if_no_groq: None, groq_api_key: str) -> LiteLLMProvider:
        """Create LiteLLM provider for Groq, using session-scoped fixtures."""
        litellm_provider_session.provider = "groq"
        litellm_provider_session.model = "llama3-8b-8192"
        litellm_provider_session.api_key = groq_api_key
        litellm_provider_session._configure_litellm()
        litellm_provider_session._outlines_provider = outlines_provider_session # Inject the session-scoped outlines provider
        return litellm_provider_session

    @pytest.mark.asyncio
    async def test_json_generation_with_pydantic(self, openai_provider: LiteLLMProvider) -> None:
        """Test JSON generation with Pydantic model."""
        result = await openai_provider.generate_with_schema(
            prompt="Create a user profile for a 25-year-old software developer named Alice",
            schema=UserProfile,
            max_tokens=200,
            temperature=0.1
        )
        
        assert result["success"] is True

        # Validate content structure
        content = result["content"]
        if isinstance(content, str):
            content = json.loads(content)

        assert "name" in content
        assert "age" in content
        assert "email" in content
        assert isinstance(content["age"], int)
        assert content["age"] == 25
        assert "alice" in content["name"].lower()

    @pytest.mark.asyncio
    async def test_json_generation_with_schema(self, openai_provider: LiteLLMProvider) -> None:
        """Test JSON generation with JSON schema."""
        schema = {
            "type": "object",
            "properties": {
                "task": {"type": "string"},
                "priority": {"type": "integer", "minimum": 1, "maximum": 5},
                "due_date": {"type": "string"},
                "completed": {"type": "boolean"}
            },
            "required": ["task", "priority"]
        }

        result = await openai_provider.generate_with_schema(
            prompt="Create a high-priority task for code review",
            schema=schema,
            max_tokens=150,
            temperature=0.2
        )
        
        assert result["success"] is True

        content = result["content"]
        if isinstance(content, str):
            content = json.loads(content)

        assert "task" in content
        assert "priority" in content
        assert isinstance(content["priority"], int)
        assert 1 <= content["priority"] <= 5

    @pytest.mark.asyncio
    async def test_choice_generation_with_enum(self, openai_provider: LiteLLMProvider) -> None:
        """Test choice generation with enum - using JSON schema approach."""
        # Since LiteLLM doesn't have direct choice generation, use schema with enum
        schema = {
            "type": "object",
            "properties": {
                "status": {
                    "type": "string",
                    "enum": [status.value for status in TaskStatus]
                }
            },
            "required": ["status"]
        }

        result = await openai_provider.generate_with_schema(
            prompt="What is the status of a task that has been finished? Respond with just the status.",
            schema=schema,
            max_tokens=50,
            temperature=0.1
        )

        assert result["success"] is True
        content = result["content"]
        if isinstance(content, str):
            content = json.loads(content)

        assert "status" in content
        # OpenAI may return capitalized values, so check case-insensitively
        status_values = [status.value for status in TaskStatus]
        assert content["status"].lower() in [v.lower() for v in status_values]
        # Should likely be "completed" for a finished task
        assert content["status"].lower() == TaskStatus.COMPLETED.value.lower()

    @pytest.mark.asyncio
    async def test_choice_generation_with_list(self, openai_provider: LiteLLMProvider) -> None:
        """Test choice generation with list of choices - using JSON schema approach."""
        # Use schema with enum for list choices
        schema = {
            "type": "object",
            "properties": {
                "language": {
                    "type": "string",
                    "enum": ["Python", "JavaScript", "Java", "C++", "Go"]
                }
            },
            "required": ["language"]
        }

        result = await openai_provider.generate_with_schema(
            prompt="Which programming language is best for machine learning? Respond with just the language name.",
            schema=schema,
            max_tokens=50,
            temperature=0.1
        )

        assert result["success"] is True
        content = result["content"]
        if isinstance(content, str):
            content = json.loads(content)

        assert "language" in content
        assert content["language"] in ["Python", "JavaScript", "Java", "C++", "Go"]
        # Should likely be "Python" for ML
        assert content["language"] == "Python"

    # NOTE: Regex and Grammar generation are not supported by OpenAI API
    # These would only work with local models through Outlines
    # For OpenAI, we use JSON Schema which is more powerful and reliable

    @pytest.mark.asyncio
    async def test_llm_provider_schema_integration(self, openai_provider: LiteLLMProvider) -> None:
        """Test LiteLLM provider with schema generation."""
        # Test schema generation
        schema = {
            "type": "object",
            "properties": {
                "summary": {"type": "string"},
                "sentiment": {"type": "string", "enum": ["positive", "negative", "neutral"]},
                "confidence": {"type": "number", "minimum": 0, "maximum": 1}
            },
            "required": ["summary", "sentiment"]
        }

        result = await openai_provider.generate_with_schema(
            prompt="Analyze this text: 'I love this new feature! It works perfectly.'",
            schema=schema,
            max_tokens=150,
            temperature=0.1
        )

        assert result["success"] is True

        # Parse and validate JSON
        if isinstance(result["content"], str):
            content = json.loads(result["content"])
        else:
            content = result["content"]

        assert "summary" in content
        assert "sentiment" in content
        # OpenAI may return capitalized values, so check case-insensitively
        sentiment_values = ["positive", "negative", "neutral"]
        assert content["sentiment"].lower() in sentiment_values
        # Should be positive for the given text
        assert content["sentiment"].lower() == "positive"

    # NOTE: Regex and Grammar generation are OpenAI API limitations
    # These features would only work with local models through Outlines
    # For production use with OpenAI, JSON Schema is the recommended approach

    @pytest.mark.asyncio
    async def test_fallback_when_outlines_unavailable(self, skip_if_no_openai: None, openai_api_key: str) -> None:
        """Test fallback to regular generation when Outlines is unavailable."""
        # Create provider without Outlines
        with pytest.MonkeyPatch().context() as m:
            m.setattr(
                "plugginger.plugins.core.llm_provider.services.litellm_provider.OUTLINES_INTEGRATION_AVAILABLE",
                False
            )

            provider = LiteLLMProvider("openai", "gpt-4o-mini", api_key=openai_api_key)

            result = await provider.generate_with_schema(
                prompt="Create a simple JSON object with name and value fields",
                schema={"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "number"}}},
                max_tokens=100
            )

            # Should fallback to regular structured generation
            assert result["success"] is True
            # Without Outlines, should use LiteLLM's built-in structured generation
            assert result.get("outlines_enabled") is not True


class TestSchemaServiceIntegration:
    """Integration tests for Schema Service."""

    @pytest.fixture
    def schema_service(self) -> SchemaService:
        """Create schema service with common schemas."""
        service = SchemaService()
        service.create_common_schemas()
        return service

    def test_user_profile_schema_validation(self, schema_service: SchemaService) -> None:
        """Test user profile schema validation."""
        schema = schema_service.get_schema("user_profile")
        assert schema is not None
        
        # Valid data
        valid_data = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "age": 30,
            "bio": "Software developer",
            "active": True
        }
        
        result = schema_service.validate_with_schema(valid_data, schema)
        assert result.is_valid is True
        
        # Invalid data (missing required field)
        invalid_data = {
            "name": "John Doe",
            "age": 30
        }
        
        result = schema_service.validate_with_schema(invalid_data, schema)
        assert result.is_valid is False

    def test_api_response_schema_validation(self, schema_service: SchemaService) -> None:
        """Test API response schema validation."""
        schema = schema_service.get_schema("api_response")
        assert schema is not None
        
        # Valid response
        valid_response = {
            "success": True,
            "data": {"user_id": 123},
            "message": "User created successfully"
        }
        
        result = schema_service.validate_with_schema(valid_response, schema)
        assert result.is_valid is True

    def test_code_generation_schema_validation(self, schema_service: SchemaService) -> None:
        """Test code generation schema validation."""
        schema = schema_service.get_schema("code_generation")
        assert schema is not None
        
        # Valid code
        valid_code = {
            "language": "python",
            "code": "def hello():\n    print('Hello, World!')",
            "description": "Simple hello world function",
            "dependencies": [],
            "test_cases": [
                {
                    "input": "",
                    "expected_output": "Hello, World!"
                }
            ]
        }
        
        result = schema_service.validate_with_schema(valid_code, schema)
        assert result.is_valid is True


class TestGrammarServiceIntegration:
    """Integration tests for Grammar Service."""

    @pytest.fixture
    def grammar_service(self) -> GrammarService:
        """Create grammar service."""
        return GrammarService()

    def test_arithmetic_grammar_validation(self, grammar_service: GrammarService) -> None:
        """Test arithmetic grammar validation."""
        grammar = grammar_service.get_grammar("arithmetic")
        assert grammar is not None
        
        result = grammar_service.validate_grammar_syntax(grammar.grammar)
        assert result.is_valid is True

    def test_json_grammar_validation(self, grammar_service: GrammarService) -> None:
        """Test JSON grammar validation."""
        grammar = grammar_service.get_grammar("json")
        assert grammar is not None
        
        result = grammar_service.validate_grammar_syntax(grammar.grammar)
        assert result.is_valid is True

    def test_python_function_grammar_validation(self, grammar_service: GrammarService) -> None:
        """Test Python function grammar validation."""
        grammar = grammar_service.get_grammar("python_function")
        assert grammar is not None
        
        result = grammar_service.validate_grammar_syntax(grammar.grammar)
        # May have warnings due to complex grammar, but should be valid
        assert len(result.errors) == 0

    def test_custom_grammar_registration(self, grammar_service: GrammarService) -> None:
        """Test custom grammar registration and validation."""
        from plugginger.plugins.core.structured_generation.services.grammar_service import EBNFGrammar
        
        custom_grammar = EBNFGrammar(
            name="simple_math",
            grammar="""
                start: expression
                expression: NUMBER ("+" NUMBER)*
                %import common.NUMBER
                %import common.WS
                %ignore WS
            """,
            description="Simple addition expressions"
        )
        
        grammar_service.register_grammar(custom_grammar)
        
        retrieved = grammar_service.get_grammar("simple_math")
        assert retrieved is not None
        assert retrieved.name == "simple_math"
        
        result = grammar_service.validate_grammar_syntax(retrieved.grammar)
        assert result.is_valid is True
