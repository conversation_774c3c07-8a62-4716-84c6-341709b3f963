"""
Integration tests for Outlines structured generation.
"""
import json
import pytest
from typing import Any, Dict, List
from enum import Enum

from pydantic import BaseModel

from plugginger.plugins.core.llm_provider.services.litellm_provider import LiteLLMProvider
from plugginger.plugins.core.structured_generation.services.outlines_provider import (
    OutlinesProvider,
    OutlinesProviderFactory,
)
from plugginger.plugins.core.structured_generation.services.schema_service import SchemaService
from plugginger.plugins.core.structured_generation.services.grammar_service import GrammarService


class UserProfile(BaseModel):
    """Test user profile model."""
    name: str
    age: int
    email: str
    bio: str = "No bio provided"
    active: bool = True


class TaskStatus(str, Enum):
    """Test task status enum."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class TestOutlinesIntegration:
    """Integration tests for Outlines with real models."""

    @pytest.fixture
    def openai_provider(self, skip_if_no_openai: None) -> OutlinesProvider:
        """Create OpenAI Outlines provider."""
        return OutlinesProviderFactory.create_openai_provider(
            model_name="gpt-4o-mini"
        )

    @pytest.fixture
    def llm_provider_with_outlines(self, skip_if_no_openai: None, openai_api_key: str) -> LiteLLMProvider:
        """Create LiteLLM provider with Outlines integration."""
        return LiteLLMProvider("openai", "gpt-4o-mini", api_key=openai_api_key)

    @pytest.mark.asyncio
    async def test_json_generation_with_pydantic(self, openai_provider: OutlinesProvider) -> None:
        """Test JSON generation with Pydantic model."""
        result = await openai_provider.generate_json(
            prompt="Create a user profile for a 25-year-old software developer named Alice",
            schema=UserProfile,
            max_tokens=200,
            temperature=0.1
        )
        
        assert result["success"] is True
        assert result["schema_validated"] is True
        
        # Validate content structure
        content = result["content"]
        assert "name" in content
        assert "age" in content
        assert "email" in content
        assert isinstance(content["age"], int)
        assert content["age"] == 25
        assert "alice" in content["name"].lower()

    @pytest.mark.asyncio
    async def test_json_generation_with_schema(self, openai_provider: OutlinesProvider) -> None:
        """Test JSON generation with JSON schema."""
        schema = {
            "type": "object",
            "properties": {
                "task": {"type": "string"},
                "priority": {"type": "integer", "minimum": 1, "maximum": 5},
                "due_date": {"type": "string"},
                "completed": {"type": "boolean"}
            },
            "required": ["task", "priority"]
        }
        
        result = await openai_provider.generate_json(
            prompt="Create a high-priority task for code review",
            schema=schema,
            max_tokens=150,
            temperature=0.2
        )
        
        assert result["success"] is True
        assert result["schema_validated"] is True
        
        content = result["content"]
        assert "task" in content
        assert "priority" in content
        assert isinstance(content["priority"], int)
        assert 1 <= content["priority"] <= 5

    @pytest.mark.asyncio
    async def test_choice_generation_with_enum(self, openai_provider: OutlinesProvider) -> None:
        """Test choice generation with enum."""
        result = await openai_provider.generate_choice(
            prompt="What is the status of a task that has been finished?",
            choices=TaskStatus,
            max_tokens=50,
            temperature=0.1
        )
        
        assert result["success"] is True
        assert result["choice_validated"] is True
        assert result["content"] in [status.value for status in TaskStatus]
        # Should likely be "completed" for a finished task
        assert result["content"] == TaskStatus.COMPLETED.value

    @pytest.mark.asyncio
    async def test_choice_generation_with_list(self, openai_provider: OutlinesProvider) -> None:
        """Test choice generation with list of choices."""
        programming_languages = ["Python", "JavaScript", "Java", "C++", "Go"]
        
        result = await openai_provider.generate_choice(
            prompt="Which programming language is best for machine learning?",
            choices=programming_languages,
            max_tokens=50,
            temperature=0.1
        )
        
        assert result["success"] is True
        assert result["choice_validated"] is True
        assert result["content"] in programming_languages
        # Should likely be "Python" for ML
        assert result["content"] == "Python"

    @pytest.mark.asyncio
    async def test_regex_generation(self, openai_provider: OutlinesProvider) -> None:
        """Test regex-constrained generation."""
        # Email pattern
        email_pattern = r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
        
        result = await openai_provider.generate_regex(
            prompt="Generate a professional email address for John Smith at TechCorp",
            pattern=email_pattern,
            max_tokens=100,
            temperature=0.1
        )
        
        assert result["success"] is True
        assert result["pattern_validated"] is True
        
        # Validate email format
        import re
        assert re.match(email_pattern, result["content"])
        assert "john" in result["content"].lower()

    @pytest.mark.asyncio
    async def test_grammar_generation_arithmetic(self, openai_provider: OutlinesProvider) -> None:
        """Test EBNF grammar generation for arithmetic."""
        arithmetic_grammar = """
            ?start: expression
            ?expression: term (("+" | "-") term)*
            ?term: factor (("*" | "/") factor)*
            ?factor: NUMBER
                   | "-" factor
                   | "(" expression ")"
            %import common.NUMBER
        """
        
        result = await openai_provider.generate_grammar(
            prompt="Generate an arithmetic expression that calculates 2 plus 3 times 4",
            grammar=arithmetic_grammar,
            max_tokens=100,
            temperature=0.1
        )
        
        assert result["success"] is True
        assert result["grammar_validated"] is True
        
        # Should contain numbers and operators
        content = result["content"]
        assert any(char.isdigit() for char in content)
        assert any(op in content for op in ["+", "-", "*", "/"])

    @pytest.mark.asyncio
    async def test_llm_provider_outlines_integration(self, llm_provider_with_outlines: LiteLLMProvider) -> None:
        """Test LiteLLM provider with Outlines integration."""
        # Test schema generation
        schema = {
            "type": "object",
            "properties": {
                "summary": {"type": "string"},
                "sentiment": {"type": "string", "enum": ["positive", "negative", "neutral"]},
                "confidence": {"type": "number", "minimum": 0, "maximum": 1}
            },
            "required": ["summary", "sentiment"]
        }
        
        result = await llm_provider_with_outlines.generate_with_schema(
            prompt="Analyze this text: 'I love this new feature! It works perfectly.'",
            schema=schema,
            max_tokens=150,
            temperature=0.1
        )
        
        assert result["success"] is True
        assert result.get("outlines_enabled") is True
        
        # Parse and validate JSON
        if isinstance(result["content"], str):
            content = json.loads(result["content"])
        else:
            content = result["content"]
            
        assert "summary" in content
        assert "sentiment" in content
        assert content["sentiment"] in ["positive", "negative", "neutral"]
        # Should be positive for the given text
        assert content["sentiment"] == "positive"

    @pytest.mark.asyncio
    async def test_llm_provider_regex_integration(self, llm_provider_with_outlines: LiteLLMProvider) -> None:
        """Test LiteLLM provider regex generation."""
        # Phone number pattern
        phone_pattern = r"\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}"
        
        result = await llm_provider_with_outlines.generate_with_regex(
            prompt="Generate a US phone number",
            pattern=phone_pattern,
            max_tokens=50,
            temperature=0.1
        )
        
        assert result["success"] is True
        assert result.get("outlines_enabled") is True
        
        # Validate phone number format
        import re
        assert re.match(phone_pattern, result["content"])

    @pytest.mark.asyncio
    async def test_llm_provider_grammar_integration(self, llm_provider_with_outlines: LiteLLMProvider) -> None:
        """Test LiteLLM provider grammar generation."""
        # Simple JSON grammar
        json_grammar = """
            ?start: value
            ?value: object | array | string | number | "true" | "false" | "null"
            object: "{" [pair ("," pair)*] "}"
            pair: string ":" value
            array: "[" [value ("," value)*] "]"
            string: ESCAPED_STRING
            number: SIGNED_NUMBER
            
            %import common.ESCAPED_STRING
            %import common.SIGNED_NUMBER
            %import common.WS
            %ignore WS
        """
        
        result = await llm_provider_with_outlines.generate_with_grammar(
            prompt="Generate a JSON object with name and age",
            grammar=json_grammar,
            max_tokens=100,
            temperature=0.1
        )
        
        assert result["success"] is True
        assert result.get("outlines_enabled") is True
        
        # Should be valid JSON
        try:
            parsed = json.loads(result["content"])
            assert isinstance(parsed, (dict, list))
        except json.JSONDecodeError:
            pytest.fail("Generated content is not valid JSON")

    @pytest.mark.asyncio
    async def test_fallback_when_outlines_unavailable(self, skip_if_no_openai: None, openai_api_key: str) -> None:
        """Test fallback to regular generation when Outlines is unavailable."""
        # Create provider without Outlines
        with pytest.MonkeyPatch().context() as m:
            m.setattr(
                "plugginger.plugins.core.llm_provider.services.litellm_provider.OUTLINES_INTEGRATION_AVAILABLE",
                False
            )

            provider = LiteLLMProvider("openai", "gpt-4o-mini", api_key=openai_api_key)

            result = await provider.generate_with_schema(
                prompt="Create a simple JSON object",
                schema={"type": "object"},
                max_tokens=100
            )

            # Should fallback to regular structured generation
            assert result["success"] is True
            assert result.get("outlines_enabled") is not True


class TestSchemaServiceIntegration:
    """Integration tests for Schema Service."""

    @pytest.fixture
    def schema_service(self) -> SchemaService:
        """Create schema service with common schemas."""
        service = SchemaService()
        service.create_common_schemas()
        return service

    def test_user_profile_schema_validation(self, schema_service: SchemaService) -> None:
        """Test user profile schema validation."""
        schema = schema_service.get_schema("user_profile")
        assert schema is not None
        
        # Valid data
        valid_data = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "age": 30,
            "bio": "Software developer",
            "active": True
        }
        
        result = schema_service.validate_with_schema(valid_data, schema)
        assert result.is_valid is True
        
        # Invalid data (missing required field)
        invalid_data = {
            "name": "John Doe",
            "age": 30
        }
        
        result = schema_service.validate_with_schema(invalid_data, schema)
        assert result.is_valid is False

    def test_api_response_schema_validation(self, schema_service: SchemaService) -> None:
        """Test API response schema validation."""
        schema = schema_service.get_schema("api_response")
        assert schema is not None
        
        # Valid response
        valid_response = {
            "success": True,
            "data": {"user_id": 123},
            "message": "User created successfully"
        }
        
        result = schema_service.validate_with_schema(valid_response, schema)
        assert result.is_valid is True

    def test_code_generation_schema_validation(self, schema_service: SchemaService) -> None:
        """Test code generation schema validation."""
        schema = schema_service.get_schema("code_generation")
        assert schema is not None
        
        # Valid code
        valid_code = {
            "language": "python",
            "code": "def hello():\n    print('Hello, World!')",
            "description": "Simple hello world function",
            "dependencies": [],
            "test_cases": [
                {
                    "input": "",
                    "expected_output": "Hello, World!"
                }
            ]
        }
        
        result = schema_service.validate_with_schema(valid_code, schema)
        assert result.is_valid is True


class TestGrammarServiceIntegration:
    """Integration tests for Grammar Service."""

    @pytest.fixture
    def grammar_service(self) -> GrammarService:
        """Create grammar service."""
        return GrammarService()

    def test_arithmetic_grammar_validation(self, grammar_service: GrammarService) -> None:
        """Test arithmetic grammar validation."""
        grammar = grammar_service.get_grammar("arithmetic")
        assert grammar is not None
        
        result = grammar_service.validate_grammar_syntax(grammar.grammar)
        assert result.is_valid is True

    def test_json_grammar_validation(self, grammar_service: GrammarService) -> None:
        """Test JSON grammar validation."""
        grammar = grammar_service.get_grammar("json")
        assert grammar is not None
        
        result = grammar_service.validate_grammar_syntax(grammar.grammar)
        assert result.is_valid is True

    def test_python_function_grammar_validation(self, grammar_service: GrammarService) -> None:
        """Test Python function grammar validation."""
        grammar = grammar_service.get_grammar("python_function")
        assert grammar is not None
        
        result = grammar_service.validate_grammar_syntax(grammar.grammar)
        # May have warnings due to complex grammar, but should be valid
        assert len(result.errors) == 0

    def test_custom_grammar_registration(self, grammar_service: GrammarService) -> None:
        """Test custom grammar registration and validation."""
        from plugginger.plugins.core.structured_generation.services.grammar_service import EBNFGrammar
        
        custom_grammar = EBNFGrammar(
            name="simple_math",
            grammar="""
                start: expression
                expression: NUMBER ("+" NUMBER)*
                %import common.NUMBER
                %import common.WS
                %ignore WS
            """,
            description="Simple addition expressions"
        )
        
        grammar_service.register_grammar(custom_grammar)
        
        retrieved = grammar_service.get_grammar("simple_math")
        assert retrieved is not None
        assert retrieved.name == "simple_math"
        
        result = grammar_service.validate_grammar_syntax(retrieved.grammar)
        assert result.is_valid is True
