# LLM Provider API Keys
# Copy this file to .env and fill in your actual API keys

# OpenAI API Key
# Get from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-your-openai-api-key-here

# Google Gemini API Key  
# Get from: https://aistudio.google.com/app/apikey
GOOGLE_API_KEY=your-google-api-key-here

# Groq API Key
# Get from: https://console.groq.com/keys
GROQ_API_KEY=gsk_your-groq-api-key-here

# Ollama Configuration (for local models)
OLLAMA_BASE_URL=http://localhost:11434

# Test Configuration
# Set to 'true' to run integration tests with real API calls
RUN_INTEGRATION_TESTS=false
RUN_E2E_TESTS=false

# Logging Configuration
LOG_LEVEL=INFO
ENABLE_DEBUG_LOGGING=false
