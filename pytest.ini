[pytest]
minversion = 7.0
addopts = -ra -q --tb=short --cov=src/plugginger --cov-report=term-missing --cov-report=html --cov-report=xml --cov-fail-under=25 --cov-config=.coveragerc
testpaths = tests
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
norecursedirs = .venv build dist .git __pycache__ *.egg-info .pytest_cache htmlcov
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::FutureWarning
    ignore::UserWarning
    ignore::RuntimeWarning
    ignore::ImportWarning
