# Plugginger Framework - Development Roadmap

> **🤖 AI Agent Instructions**: This document outlines the strategic development plan. Always start with the [Current Sprint](#-current-sprint) and follow the [Agent Workflow](#-agent-workflow) for all development tasks. The CI/CD pipeline on GitHub is the ultimate source of truth for code quality.

## 🎯 Framework Vision

**Plugginger** is the first truly **AI-friendly plugin framework** for Python. Its purpose is to enable developers and AI agents to build, test, and deploy modular applications with maximum speed and architectural integrity.

Our core philosophy is to provide an **AI Co-Pilot** experience: the framework automates boilerplate and architectural scaffolding, allowing developers to focus on core business logic.

## 🚀 Current Sprint

### **S5.3 - Architecture Refinement**
**Status**: `ready` | **Priority**: `P2` | **Assignee**: `Available`

| Task | Description | Status | Issue |
|------|-------------|--------|-------|
| Error Handling Audit | Review and standardize error handling across the AI subsystem to ensure consistency and provide actionable feedback. | `todo` | - |
| Performance Optimization | Profile and optimize the intelligent plugin generation workflow to reduce latency and resource consumption. | `todo` | - |
| AppPlugin Configuration | Define and implement a clear, standardized method for passing configuration to nested sub-applications within `AppPluginBase`. | `todo` | - |
| Circular Import Review | Analyze the internal module structure and refactor where necessary to eliminate potential circular import issues. | `todo` | - |

**Definition of Done**: The AI subsystem has consistent error handling, generation performance is measurably improved, and sub-application configuration is standardized and documented.

## 📋 Next Sprints (Prioritized)

### **S5.4 - Developer Experience Enhancement**
**Status**: `blocked` (depends on S5.3) | **Priority**: `P3`

| Task | Description | Status |
|------|-------------|--------|
| Enhanced CLI Help | Improve CLI help messages and add practical examples for all intelligent generation commands. | `todo` |
| Plugin Templates | Refine the built-in scaffolding templates to reflect the latest best practices. | `todo` |
| Developer Tutorials | Create comprehensive tutorials for core concepts like `AppPluginBase` and the `flowinger` service. | `todo` |
| IDE Integration | Research and develop initial tooling for better IDE support (e.g., VS Code snippets). | `todo` |

**Definition of Done**: Developers have access to clear, comprehensive documentation and improved tooling, significantly lowering the barrier to entry.

### **S5.5 - Advanced Features & Marketplace**
**Status**: `blocked` (depends on S5.4) | **Priority**: `P4`

| Task | Description | Status |
|------|-------------|--------|
| Plugin Marketplace | Implement the experimental `PluginRegistry` to discover and fetch plugins from a remote source. | `todo` |
| Advanced Dependency Mgmt | Enhance dependency declarations to support version constraints and compatibility checks. | `todo` |
| Distributed Deployment | Design and implement initial support for deploying plugins as separate, containerized processes. | `todo` |

**Definition of Done**: The framework has a functional proof-of-concept for a plugin ecosystem and advanced deployment patterns.

## 🤖 Agent Workflow

This section defines the mandatory, CI/CD-centric process for completing tasks.

### **Phase 1: Local Development**

1.  **Task Selection:**
    *   Select the first `todo` task from the `Current Sprint`.
    *   Update its status to `doing` in this file and create a corresponding GitHub Issue.
2.  **Branching:**
    *   Create a new branch from an up-to-date `main`: `git checkout -b s<sprint>/<task-name>`.
3.  **Implementation & Local Quality Gates:**
    *   Implement the required changes for the task.
    *   Continuously run local quality checks: `pytest`, `mypy --strict .`, `ruff check .`. **Passing these locally is a prerequisite, not a guarantee of success.**

### **Phase 2: Integration and CI/CD Validation**

4.  **Pull Request Creation:**
    *   Push your branch to the remote repository.
    *   Create a Pull Request (PR) targeting the `main` branch. **Do not merge it yet.**
5.  **CI/CD Pipeline as the Gatekeeper (CRITICAL STEP):**
    *   The creation of the PR will automatically trigger the CI/CD pipeline on GitHub Actions.
    *   **You must monitor the pipeline's result.** Your task is not complete until the pipeline for your PR passes successfully (shows a green checkmark).
6.  **Handling CI/CD Failures:**
    *   **If the pipeline fails (red X):**
        *   Analyze the logs from the failed CI/CD job on GitHub.
        *   Identify the root cause (e.g., a dependency issue, a test that only fails in the CI environment).
        *   Return to your local branch, fix the issue, and push the changes. This will automatically re-run the pipeline.
        *   **Repeat this cycle until the CI/CD pipeline passes.** Do not proceed to the next step otherwise.

### **Phase 3: Finalization**

7.  **Merge and Cleanup:**
    *   **Only after the CI/CD pipeline for your PR is green**, merge the Pull Request into `main`.
    *   Clean up your branch (local and remote).
8.  **Task Completion:**
    *   Update this `ROADMAP.md`: Move the completed task to a `## ✅ Completed Sprints` section and mark it as `done` with the completion date.
    *   Update `CHANGELOG.md` with a summary of the changes if they are user-facing.
    *   Close the corresponding GitHub Issue.

### 🚨 Critical Rules
-   **CI/CD is the Source of Truth:** A task is only truly "done" when its corresponding PR has a successful (green) CI/CD run.
-   **No direct pushes to `main`**. All changes must go through a PR with a passing CI/CD pipeline.
-   **Do not disable or comment out tests** to force a pass.
-   **Follow the "Protocol for Temporary Deactivations"** if a blocker requires disabling code.

#### Protocol for Temporary Deactivations
If a feature or test must be temporarily disabled to resolve a blocker:
1.  **Add a Code Comment:** `# TODO-AI-BLOCKED: Disabled on YYYY-MM-DD due to [reason]. Re-enable in task #[new_issue_number].`
2.  **Create a New Task:** Add a `P1` priority task to the *next* sprint in this roadmap to re-enable the code and fix the blocker.