"""
JSON validation with retry logic for LLM outputs.

Provides robust validation and retry mechanisms for ensuring LLM-generated
JSON conforms to expected schemas with graceful error recovery.
"""

import json
import logging
from typing import Any, TypeVar

from pydantic import BaseModel, ValidationError

from plugginger.ai.grammars import GrammarValidator
from plugginger.ai.types import StructuredPrompt
from plugginger.ai.types import ValidationError as AIValidationError
from plugginger.core.exceptions import PluggingerValidationError

logger = logging.getLogger(__name__)

T = TypeVar('T', bound=BaseModel)


class JSONValidationResult:
    """Result of JSON validation with detailed error information."""

    def __init__(
        self,
        valid: bool,
        parsed_data: dict[str, Any] | None = None,
        validation_errors: list[AIValidationError] | None = None,
        raw_content: str | None = None
    ) -> None:
        """Initialize validation result.

        Args:
            valid: Whether validation was successful
            parsed_data: Successfully parsed JSON data
            validation_errors: List of validation errors
            raw_content: Original raw content that was validated
        """
        self.valid = valid
        self.parsed_data = parsed_data
        self.validation_errors = validation_errors or []
        self.raw_content = raw_content


class JSONValidator:
    """Validator for LLM-generated JSON with retry logic."""

    def __init__(self, max_retries: int = 3) -> None:
        """Initialize JSON validator.

        Args:
            max_retries: Maximum number of retry attempts
        """
        self.max_retries = max_retries

    def validate_and_parse(
        self,
        json_content: str,
        target_model: type[T],
        grammar_validator: str | None = None
    ) -> JSONValidationResult:
        """Validate and parse JSON content against target model.

        Args:
            json_content: Raw JSON string from LLM
            target_model: Pydantic model to validate against
            grammar_validator: Optional grammar validation method name

        Returns:
            JSONValidationResult with validation outcome
        """
        # Step 1: Basic JSON parsing
        try:
            parsed_data = json.loads(json_content)
        except json.JSONDecodeError as e:
            logger.warning(f"JSON parsing failed: {e}")
            return JSONValidationResult(
                valid=False,
                validation_errors=[
                    AIValidationError(
                        error_type="json_parse_error",
                        message=f"Invalid JSON syntax: {str(e)}",
                        location=f"character {e.pos}" if hasattr(e, 'pos') else None,
                        suggestion="Ensure proper JSON formatting with balanced brackets and quotes"
                    )
                ],
                raw_content=json_content
            )

        # Step 2: Grammar validation (if specified)
        if grammar_validator:
            grammar_valid = self._validate_with_grammar(json_content, grammar_validator)
            if not grammar_valid:
                return JSONValidationResult(
                    valid=False,
                    validation_errors=[
                        AIValidationError(
                            error_type="grammar_validation_error",
                            message="JSON structure does not match expected grammar",
                            suggestion="Review the required JSON schema and ensure all required fields are present"
                        )
                    ],
                    raw_content=json_content
                )

        # Step 3: Pydantic model validation
        try:
            validated_model = target_model(**parsed_data)
            logger.info(f"Successfully validated JSON against {target_model.__name__}")
            return JSONValidationResult(
                valid=True,
                parsed_data=validated_model.model_dump(),
                raw_content=json_content
            )

        except ValidationError as e:
            logger.warning(f"Pydantic validation failed: {e}")
            validation_errors = []

            for error in e.errors():
                error_dict = {
                    'type': error['type'],
                    'loc': error['loc'],
                    'msg': error['msg']
                }
                validation_errors.append(
                    AIValidationError(
                        error_type="schema_validation_error",
                        message=error['msg'],
                        location='.'.join(str(loc) for loc in error['loc']),
                        suggestion=self._generate_fix_suggestion(error_dict)
                    )
                )

            return JSONValidationResult(
                valid=False,
                validation_errors=validation_errors,
                raw_content=json_content
            )

    def _validate_with_grammar(self, json_content: str, grammar_method: str) -> bool:
        """Validate JSON content using specified grammar method.

        Args:
            json_content: JSON string to validate
            grammar_method: Name of grammar validation method

        Returns:
            True if grammar validation passes
        """
        try:
            validator_method = getattr(GrammarValidator, grammar_method)
            result = validator_method(json_content)
            return bool(result)  # Ensure we return a bool
        except AttributeError:
            logger.warning(f"Grammar validation method '{grammar_method}' not found")
            return True  # Skip grammar validation if method doesn't exist
        except Exception as e:
            logger.warning(f"Grammar validation failed: {e}")
            return False

    def _generate_fix_suggestion(self, error: dict[str, Any]) -> str:
        """Generate helpful suggestion for fixing validation error.

        Args:
            error: Pydantic validation error

        Returns:
            Human-readable suggestion for fixing the error
        """
        error_type = error.get('type', '')
        field_name = error.get('loc', ['unknown'])[-1]

        suggestions = {
            'missing': f"Add required field '{field_name}' to the JSON object",
            'type_error': f"Ensure field '{field_name}' has the correct data type",
            'value_error': f"Provide a valid value for field '{field_name}'",
            'extra_forbidden': f"Remove unexpected field '{field_name}' from the JSON object",
        }

        for error_pattern, suggestion in suggestions.items():
            if error_pattern in error_type:
                return suggestion

        return f"Fix validation error for field '{field_name}': {error.get('msg', 'Unknown error')}"


class RetryableJSONProcessor:
    """Processor that handles LLM JSON generation with retry logic."""

    def __init__(self, validator: JSONValidator) -> None:
        """Initialize retryable processor.

        Args:
            validator: JSON validator instance
        """
        self.validator = validator

    async def process_with_retry(
        self,
        llm_provider: Any,  # LLMProvider type (avoiding circular import)
        prompt: StructuredPrompt,
        target_model: type[T],
        grammar_validator: str | None = None,
        max_retries: int = 3
    ) -> T | None:
        """Process LLM response with retry logic for invalid JSON.

        Args:
            llm_provider: LLM provider instance
            prompt: Structured prompt for LLM
            target_model: Target Pydantic model
            grammar_validator: Optional grammar validation method
            max_retries: Maximum retry attempts

        Returns:
            Validated model instance or None if all retries failed

        Raises:
            PluggingerValidationError: If validation fails after all retries
        """
        last_errors: list[AIValidationError] = []

        for attempt in range(max_retries + 1):
            try:
                # Generate response from LLM
                response = await llm_provider.generate_structured(prompt)

                if not response.success:
                    logger.warning(f"LLM generation failed (attempt {attempt + 1}): {response.error_message}")
                    continue

                # Validate the response
                validation_result = self.validator.validate_and_parse(
                    response.content,
                    target_model,
                    grammar_validator
                )

                if validation_result.valid and validation_result.parsed_data is not None:
                    logger.info(f"Successfully validated response on attempt {attempt + 1}")
                    return target_model(**validation_result.parsed_data)

                # Log validation errors for retry
                last_errors = validation_result.validation_errors
                logger.warning(f"Validation failed (attempt {attempt + 1}): {len(last_errors)} errors")

                # Enhance prompt with error feedback for retry
                if attempt < max_retries:
                    prompt = self._enhance_prompt_with_errors(prompt, last_errors)

            except Exception as e:
                logger.error(f"Unexpected error during processing (attempt {attempt + 1}): {e}")
                last_errors = [
                    AIValidationError(
                        error_type="processing_error",
                        message=str(e),
                        suggestion="Check LLM provider configuration and network connectivity"
                    )
                ]

        # All retries failed
        error_messages = [f"{err.error_type}: {err.message}" for err in last_errors]
        raise PluggingerValidationError(
            f"JSON validation failed after {max_retries + 1} attempts. "
            f"Errors: {'; '.join(error_messages)}"
        )

    def _enhance_prompt_with_errors(
        self,
        original_prompt: StructuredPrompt,
        errors: list[AIValidationError]
    ) -> StructuredPrompt:
        """Enhance prompt with error feedback for retry.

        Args:
            original_prompt: Original prompt that failed
            errors: Validation errors from previous attempt

        Returns:
            Enhanced prompt with error feedback
        """
        error_feedback = "\n".join([
            f"- {error.error_type}: {error.message}"
            + (f" (Suggestion: {error.suggestion})" if error.suggestion else "")
            for error in errors
        ])

        enhanced_user_message = (
            f"{original_prompt.user_message}\n\n"
            f"IMPORTANT: The previous response had validation errors:\n{error_feedback}\n"
            f"Please fix these issues and generate valid JSON that matches the required schema exactly."
        )

        return StructuredPrompt(
            system_message=original_prompt.system_message,
            user_message=enhanced_user_message,
            ebnf_grammar=original_prompt.ebnf_grammar,
            context=original_prompt.context
        )
