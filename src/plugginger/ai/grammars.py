"""
EBNF grammars for structured LLM outputs in Plugginger AI plugin generation.

Provides GBNF (Grammar-Based Neural Format) grammars for ensuring LLMs generate
valid JSON structures that conform to Plugginger plugin specifications.
"""



class PluginGrammar:
    """EBNF grammars for plugin generation."""

    @staticmethod
    def plugin_spec_grammar() -> str:
        """GBNF grammar for complete plugin specification.

        Ensures LLM generates valid JSON matching PluginSpec structure.

        Returns:
            GBNF grammar string for plugin specification
        """
        return '''
root ::= plugin_spec

plugin_spec ::= "{" ws
  "\"name\":" ws string "," ws
  "\"version\":" ws string "," ws
  "\"description\":" ws string "," ws
  "\"class_name\":" ws string "," ws
  "\"services\":" ws service_list "," ws
  "\"event_listeners\":" ws event_listener_list "," ws
  "\"dependencies\":" ws dependency_list ws
"}"

service_list ::= "[" ws (service (ws "," ws service)*)? ws "]"

service ::= "{" ws
  "\"name\":" ws string "," ws
  "\"description\":" ws string "," ws
  "\"parameters\":" ws parameter_list "," ws
  "\"return_type\":" ws string "," ws
  "\"async_method\":" ws boolean "," ws
  "\"timeout_seconds\":" ws (number | "null") ws
"}"

parameter_list ::= "[" ws (parameter (ws "," ws parameter)*)? ws "]"

parameter ::= "{" ws
  "\"name\":" ws string "," ws
  "\"type\":" ws string "," ws
  "\"description\":" ws string "," ws
  "\"required\":" ws boolean ws
"}"

event_listener_list ::= "[" ws (event_listener (ws "," ws event_listener)*)? ws "]"

event_listener ::= "{" ws
  "\"event_pattern\":" ws string "," ws
  "\"handler_name\":" ws string "," ws
  "\"description\":" ws string ws
"}"

dependency_list ::= "[" ws (dependency (ws "," ws dependency)*)? ws "]"

dependency ::= "{" ws
  "\"name\":" ws string "," ws
  "\"version\":" ws (string | "null") "," ws
  "\"optional\":" ws boolean ws
"}"

string ::= "\\"" char* "\\""
char ::= [^"\\\\] | "\\\\" ["\\\\/bfnrt] | "\\\\u" [0-9a-fA-F] [0-9a-fA-F] [0-9a-fA-F] [0-9a-fA-F]

number ::= "-"? ("0" | [1-9] [0-9]*) ("." [0-9]+)? ([eE] [+-]? [0-9]+)?

boolean ::= "true" | "false"

ws ::= [ \\t\\n\\r]*
'''

    @staticmethod
    def service_spec_grammar() -> str:
        """GBNF grammar for individual service specification.

        Returns:
            GBNF grammar string for service specification
        """
        return '''
root ::= service_spec

service_spec ::= "{" ws
  "\"name\":" ws string "," ws
  "\"description\":" ws string "," ws
  "\"parameters\":" ws parameter_list "," ws
  "\"return_type\":" ws string "," ws
  "\"async_method\":" ws boolean "," ws
  "\"timeout_seconds\":" ws (number | "null") ws
"}"

parameter_list ::= "[" ws (parameter (ws "," ws parameter)*)? ws "]"

parameter ::= "{" ws
  "\"name\":" ws string "," ws
  "\"type\":" ws string "," ws
  "\"description\":" ws string "," ws
  "\"required\":" ws boolean ws
"}"

string ::= "\\"" char* "\\""
char ::= [^"\\\\] | "\\\\" ["\\\\/bfnrt] | "\\\\u" [0-9a-fA-F] [0-9a-fA-F] [0-9a-fA-F] [0-9a-fA-F]

number ::= "-"? ("0" | [1-9] [0-9]*) ("." [0-9]+)? ([eE] [+-]? [0-9]+)?

boolean ::= "true" | "false"

ws ::= [ \\t\\n\\r]*
'''

    @staticmethod
    def wiring_analysis_grammar() -> str:
        """GBNF grammar for wiring analysis results.

        Returns:
            GBNF grammar string for wiring analysis
        """
        return '''
root ::= wiring_analysis

wiring_analysis ::= "{" ws
  "\"compatible_services\":" ws service_match_list "," ws
  "\"relevant_events\":" ws string_list "," ws
  "\"suggested_dependencies\":" ws dependency_list "," ws
  "\"integration_opportunities\":" ws integration_list ws
"}"

service_match_list ::= "[" ws (service_match (ws "," ws service_match)*)? ws "]"

service_match ::= "{" ws
  "\"service_name\":" ws string "," ws
  "\"plugin_name\":" ws string "," ws
  "\"confidence\":" ws number "," ws
  "\"signature\":" ws string "," ws
  "\"description\":" ws string ws
"}"

string_list ::= "[" ws (string (ws "," ws string)*)? ws "]"

dependency_list ::= "[" ws (dependency (ws "," ws dependency)*)? ws "]"

dependency ::= "{" ws
  "\"name\":" ws string "," ws
  "\"version\":" ws (string | "null") "," ws
  "\"optional\":" ws boolean ws
"}"

integration_list ::= "[" ws (integration (ws "," ws integration)*)? ws "]"

integration ::= "{" ws
  "\"event_pattern\":" ws string "," ws
  "\"integration_type\":" ws string "," ws
  "\"description\":" ws string "," ws
  "\"confidence\":" ws number ws
"}"

string ::= "\\"" char* "\\""
char ::= [^"\\\\] | "\\\\" ["\\\\/bfnrt] | "\\\\u" [0-9a-fA-F] [0-9a-fA-F] [0-9a-fA-F] [0-9a-fA-F]

number ::= "-"? ("0" | [1-9] [0-9]*) ("." [0-9]+)? ([eE] [+-]? [0-9]+)?

boolean ::= "true" | "false"

ws ::= [ \\t\\n\\r]*
'''


class GrammarValidator:
    """Validator for EBNF grammar-constrained outputs."""

    @staticmethod
    def validate_plugin_spec_json(json_str: str) -> bool:
        """Validate JSON string against plugin specification schema.

        Args:
            json_str: JSON string to validate

        Returns:
            True if valid, False otherwise
        """
        try:
            import json

            from plugginger.ai.types import PluginSpec

            # Parse JSON
            data = json.loads(json_str)

            # Validate against Pydantic model
            PluginSpec(**data)
            return True

        except (json.JSONDecodeError, ValueError, TypeError):
            return False

    @staticmethod
    def validate_service_spec_json(json_str: str) -> bool:
        """Validate JSON string against service specification schema.

        Args:
            json_str: JSON string to validate

        Returns:
            True if valid, False otherwise
        """
        try:
            import json

            from plugginger.ai.types import ServiceSpec

            # Parse JSON
            data = json.loads(json_str)

            # Validate against Pydantic model
            ServiceSpec(**data)
            return True

        except (json.JSONDecodeError, ValueError, TypeError):
            return False


class PromptTemplates:
    """Templates for LLM prompts with EBNF constraints."""

    @staticmethod
    def plugin_generation_prompt(
        user_request: str,
        context: str = "",
        available_services: str = "",
        available_events: str = ""
    ) -> str:
        """Generate system prompt for plugin generation.

        Args:
            user_request: User's natural language request
            context: Context about existing app (optional)
            available_services: Available services for wiring (optional)
            available_events: Available events for integration (optional)

        Returns:
            Formatted system prompt
        """
        context_section = f"\n\nExisting App Context:\n{context}" if context else ""
        services_section = f"\n\nAvailable Services:\n{available_services}" if available_services else ""
        events_section = f"\n\nAvailable Events:\n{available_events}" if available_events else ""

        return f"""You are an expert Plugginger plugin developer. Generate a plugin specification based on the user's request.

User Request: {user_request}{context_section}{services_section}{events_section}

Requirements:
1. Follow Plugginger plugin conventions
2. Use proper type annotations
3. Include comprehensive docstrings with AI_METADATA
4. Suggest appropriate dependencies for wiring
5. Consider event integration opportunities
6. Ensure service names are descriptive and follow snake_case
7. Make services async by default
8. Include proper error handling considerations

Generate a complete plugin specification as valid JSON matching the required schema.
Focus on practical, working code that integrates well with existing plugins."""

    @staticmethod
    def wiring_analysis_prompt(
        plugin_spec: str,
        app_context: str
    ) -> str:
        """Generate prompt for wiring analysis.

        Args:
            plugin_spec: Generated plugin specification
            app_context: Existing app context

        Returns:
            Formatted prompt for wiring analysis
        """
        return f"""Analyze the wiring compatibility of this plugin with the existing app.

Plugin Specification:
{plugin_spec}

Existing App Context:
{app_context}

Analyze:
1. Which existing services this plugin could use
2. Which events this plugin should listen to or emit
3. What dependencies should be declared
4. Integration opportunities for better functionality

Provide analysis as valid JSON with service matches, event suggestions, and integration opportunities."""
