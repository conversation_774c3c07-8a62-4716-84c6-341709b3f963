"""
Event pattern matching for intelligent plugin integration.

Provides intelligent event integration suggestions and validates event
patterns against available events in the application.
"""

import fnmatch
import logging
from typing import Any

from plugginger.ai.types import AppWiringContext, EventIntegration
from plugginger.core.types import ValidationResult

logger = logging.getLogger(__name__)


class EventPatternMatcher:
    """Matches event patterns for intelligent plugin integration."""

    def __init__(self) -> None:
        """Initialize event pattern matcher."""
        self.logger = logger

    def validate_event_patterns(
        self,
        new_listeners: list[str],
        available_events: list[str]
    ) -> ValidationResult:
        """Validate event patterns against available events.

        Args:
            new_listeners: List of event patterns the plugin wants to listen to
            available_events: List of available event patterns in the app

        Returns:
            ValidationResult with pattern validation outcome
        """
        self.logger.debug(f"Validating {len(new_listeners)} event patterns against {len(available_events)} available events")

        errors: list[str] = []
        warnings: list[str] = []
        valid_patterns = []

        for pattern in new_listeners:
            validation_result = self._validate_single_pattern(pattern, available_events)

            if validation_result["valid"]:
                valid_patterns.append(pattern)
            elif validation_result["severity"] == "error":
                errors.append(validation_result["message"])
            else:
                warnings.append(validation_result["message"])

        return ValidationResult(
            valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            metadata={
                "valid_patterns": valid_patterns,
                "total_patterns": len(new_listeners)
            }
        )

    def _validate_single_pattern(
        self,
        pattern: str,
        available_events: list[str]
    ) -> dict[str, Any]:
        """Validate a single event pattern.

        Args:
            pattern: Event pattern to validate
            available_events: Available events in the app

        Returns:
            Dictionary with validation result
        """
        # Check for empty or invalid patterns
        if not pattern or not pattern.strip():
            return {
                "valid": False,
                "severity": "error",
                "message": "Empty event pattern is not allowed"
            }

        # Check for invalid characters
        if any(char in pattern for char in [" ", "\t", "\n"]):
            return {
                "valid": False,
                "severity": "error",
                "message": f"Event pattern '{pattern}' contains invalid whitespace characters"
            }

        # Check if pattern matches any available events
        matching_events = self._find_matching_events(pattern, available_events)

        if not matching_events:
            # Check if it's a wildcard pattern that might match future events
            if "*" in pattern or "?" in pattern:
                return {
                    "valid": True,
                    "severity": "warning",
                    "message": f"Wildcard pattern '{pattern}' doesn't match current events but may match future ones"
                }
            else:
                return {
                    "valid": False,
                    "severity": "warning",
                    "message": f"Event pattern '{pattern}' doesn't match any available events: {', '.join(available_events[:5])}"
                }

        return {
            "valid": True,
            "severity": "info",
            "message": f"Pattern '{pattern}' matches {len(matching_events)} events"
        }

    def _find_matching_events(self, pattern: str, available_events: list[str]) -> list[str]:
        """Find events that match the given pattern.

        Args:
            pattern: Event pattern (supports wildcards)
            available_events: List of available events

        Returns:
            List of matching event names
        """
        matching = []

        for event in available_events:
            if fnmatch.fnmatchcase(event, pattern):
                matching.append(event)

        return matching

    def suggest_event_integrations(
        self,
        plugin_intent: str,
        app_events: list[str]
    ) -> list[EventIntegration]:
        """Suggest event integrations based on plugin intent.

        Args:
            plugin_intent: Natural language description of plugin purpose
            app_events: Available events in the app

        Returns:
            List of suggested event integrations with confidence scores
        """
        self.logger.debug(f"Suggesting event integrations for intent: {plugin_intent}")

        suggestions = []
        intent_lower = plugin_intent.lower()

        for event in app_events:
            # Calculate relevance score
            confidence = self._calculate_event_relevance(intent_lower, event)

            if confidence > 0.3:  # Only suggest reasonably relevant events
                integration_type = self._determine_integration_type(intent_lower, event)

                suggestions.append(EventIntegration(
                    event_pattern=event,
                    integration_type=integration_type,
                    description=self._generate_integration_description(event, integration_type),
                    code_example=self._generate_code_example(event, integration_type),
                    confidence=confidence
                ))

        # Sort by confidence (highest first)
        suggestions.sort(key=lambda x: x.confidence, reverse=True)

        return suggestions[:10]  # Return top 10 suggestions

    def _calculate_event_relevance(self, intent: str, event: str) -> float:
        """Calculate relevance score between intent and event.

        Args:
            intent: Plugin intent (lowercase)
            event: Event name

        Returns:
            Relevance score between 0.0 and 1.0
        """
        confidence = 0.0
        event_lower = event.lower()

        # Direct keyword matching
        intent_words = set(intent.split())
        event_words = set(event_lower.replace(".", " ").replace("_", " ").split())

        # Calculate word overlap
        common_words = intent_words.intersection(event_words)
        if common_words:
            confidence += 0.5 * (len(common_words) / max(len(intent_words), len(event_words)))

        # Domain-specific matching
        confidence += self._domain_specific_matching(intent, event_lower)

        # Pattern-based matching
        confidence += self._pattern_based_matching(intent, event_lower)

        return min(confidence, 1.0)

    def _domain_specific_matching(self, intent: str, event: str) -> float:
        """Calculate domain-specific matching score.

        Args:
            intent: Plugin intent
            event: Event name

        Returns:
            Domain matching score
        """
        score = 0.0

        # Define domain mappings
        domain_mappings = {
            "user": ["user", "auth", "login", "logout", "register", "profile"],
            "auth": ["auth", "login", "logout", "token", "session", "security"],
            "email": ["email", "mail", "message", "notification", "send"],
            "database": ["data", "save", "load", "create", "update", "delete"],
            "file": ["file", "upload", "download", "storage", "document"],
            "api": ["request", "response", "http", "api", "endpoint"],
            "error": ["error", "exception", "fail", "warning", "critical"],
            "system": ["system", "startup", "shutdown", "health", "status"]
        }

        for _domain, keywords in domain_mappings.items():
            intent_has_domain = any(keyword in intent for keyword in keywords)
            event_has_domain = any(keyword in event for keyword in keywords)

            if intent_has_domain and event_has_domain:
                score += 0.4

        return score

    def _pattern_based_matching(self, intent: str, event: str) -> float:
        """Calculate pattern-based matching score.

        Args:
            intent: Plugin intent
            event: Event name

        Returns:
            Pattern matching score
        """
        score = 0.0

        # Common event patterns and their relevance to intents
        patterns = {
            "created": ["create", "new", "add", "generate"],
            "updated": ["update", "modify", "change", "edit"],
            "deleted": ["delete", "remove", "destroy"],
            "started": ["start", "begin", "init", "launch"],
            "completed": ["complete", "finish", "done", "success"],
            "failed": ["fail", "error", "exception", "problem"]
        }

        for pattern, intent_keywords in patterns.items():
            if pattern in event and any(keyword in intent for keyword in intent_keywords):
                score += 0.3

        return score

    def _determine_integration_type(self, intent: str, event: str) -> str:
        """Determine the type of event integration.

        Args:
            intent: Plugin intent
            event: Event name

        Returns:
            Integration type (listener, emitter, or both)
        """
        # Simple heuristic based on intent keywords
        if any(word in intent for word in ["listen", "react", "respond", "handle"]):
            return "listener"
        elif any(word in intent for word in ["send", "emit", "trigger", "notify"]):
            return "emitter"
        else:
            return "listener"  # Default to listener

    def _generate_integration_description(self, event: str, integration_type: str) -> str:
        """Generate description for event integration.

        Args:
            event: Event name
            integration_type: Type of integration

        Returns:
            Human-readable description
        """
        if integration_type == "listener":
            return f"Listen to '{event}' events to react when they occur"
        elif integration_type == "emitter":
            return f"Emit '{event}' events to notify other plugins"
        else:
            return f"Integrate with '{event}' events for bidirectional communication"

    def _generate_code_example(self, event: str, integration_type: str) -> str:
        """Generate code example for event integration.

        Args:
            event: Event name
            integration_type: Type of integration

        Returns:
            Code example string
        """
        if integration_type == "listener":
            method_name = f"on_{event.replace('.', '_').replace('-', '_')}"
            return f"""@onevent('{event}')
async def {method_name}(self, event_data: dict) -> None:
    \"\"\"Handle {event} event.\"\"\"
    # Process event data
    self.logger.info(f"Received {event} event: {{event_data}}")"""

        elif integration_type == "emitter":
            return f"""# Emit {event} event
await self.app.emit_event('{event}', {{
    'timestamp': time.time(),
    'source': self.plugin_name,
    # Add relevant event data
}})"""

        else:
            method_name = f"on_{event.replace('.', '_').replace('-', '_')}"
            return f"""@onevent('{event}')
async def {method_name}(self, event_data: dict) -> None:
    \"\"\"Handle {event} event and potentially emit response.\"\"\"
    # Process event
    result = self.process_event(event_data)

    # Emit response if needed
    await self.app.emit_event('{event}.response', result)"""

    def analyze_event_patterns(self, app_context: AppWiringContext) -> dict[str, Any]:
        """Analyze event patterns in the application.

        Args:
            app_context: Application wiring context

        Returns:
            Dictionary with event pattern analysis
        """
        events = app_context.events

        # Categorize events by domain
        domains: dict[str, list[str]] = {}
        for event in events:
            parts = event.split(".")
            domain = parts[0] if parts else "unknown"

            if domain not in domains:
                domains[domain] = []
            domains[domain].append(event)

        # Find common patterns
        patterns = self._extract_common_patterns(events)

        # Calculate event complexity
        complexity_score = self._calculate_event_complexity(events)

        return {
            "total_events": len(events),
            "domains": domains,
            "common_patterns": patterns,
            "complexity_score": complexity_score,
            "recommendations": self._generate_event_recommendations(domains, patterns)
        }

    def _extract_common_patterns(self, events: list[str]) -> dict[str, int]:
        """Extract common patterns from event names.

        Args:
            events: List of event names

        Returns:
            Dictionary mapping patterns to their frequency
        """
        patterns: dict[str, int] = {}

        for event in events:
            # Extract suffixes (action patterns)
            parts = event.split(".")
            if len(parts) > 1:
                suffix = parts[-1]
                patterns[suffix] = patterns.get(suffix, 0) + 1

        # Sort by frequency
        return dict(sorted(patterns.items(), key=lambda x: x[1], reverse=True))

    def _calculate_event_complexity(self, events: list[str]) -> float:
        """Calculate complexity score for event system.

        Args:
            events: List of event names

        Returns:
            Complexity score between 0.0 and 1.0
        """
        if not events:
            return 0.0

        # Factors that increase complexity
        avg_depth = sum(len(event.split(".")) for event in events) / len(events)
        unique_domains = len({event.split(".")[0] for event in events})

        # Normalize to 0-1 scale
        depth_score = min(avg_depth / 5.0, 1.0)  # Assume max depth of 5
        domain_score = min(unique_domains / 10.0, 1.0)  # Assume max 10 domains

        return (depth_score + domain_score) / 2

    def _generate_event_recommendations(
        self,
        domains: dict[str, list[str]],
        patterns: dict[str, int]
    ) -> list[str]:
        """Generate recommendations for event system improvement.

        Args:
            domains: Event domains
            patterns: Common patterns

        Returns:
            List of recommendations
        """
        recommendations = []

        # Check for missing common patterns
        common_actions = ["created", "updated", "deleted", "started", "completed", "failed"]
        missing_actions = [action for action in common_actions if action not in patterns]

        if missing_actions:
            recommendations.append(f"Consider adding events for: {', '.join(missing_actions)}")

        # Check for domain coverage
        if len(domains) < 3:
            recommendations.append("Consider organizing events into more specific domains")

        # Check for pattern consistency
        if len(patterns) > 10:
            recommendations.append("Consider standardizing event naming patterns")

        return recommendations
