"""
Type definitions for Plugginger AI plugin generation.

Defines data structures for LLM communication, plugin specifications,
and validation results.
"""

from dataclasses import dataclass
from typing import Any

from pydantic import BaseModel, Field


@dataclass
class StructuredPrompt:
    """Structured prompt for LLM with EBNF grammar constraints."""

    system_message: str
    user_message: str
    ebnf_grammar: str
    context: dict[str, Any] | None = None


@dataclass
class LLMResponse:
    """Response from LLM provider."""

    content: str
    model: str
    provider: str
    tokens_used: int
    success: bool
    error_message: str | None = None


class ServiceSpec(BaseModel):
    """Specification for a plugin service."""

    name: str = Field(..., description="Service name")
    description: str = Field(..., description="Service description")
    parameters: list[dict[str, Any]] = Field(default_factory=list, description="Service parameters")
    return_type: str = Field(..., description="Return type annotation")
    async_method: bool = Field(default=True, description="Whether service is async")
    timeout_seconds: float | None = Field(default=None, description="Service timeout")


class EventListenerSpec(BaseModel):
    """Specification for an event listener."""

    event_pattern: str = Field(..., description="Event pattern to listen for")
    handler_name: str = Field(..., description="Handler method name")
    description: str = Field(..., description="Handler description")


class DependencySpec(BaseModel):
    """Specification for a plugin dependency."""

    name: str = Field(..., description="Dependency name")
    version: str | None = Field(default=None, description="Version constraint")
    optional: bool = Field(default=False, description="Whether dependency is optional")


class PluginSpec(BaseModel):
    """Complete specification for a generated plugin."""

    name: str = Field(..., description="Plugin name")
    version: str = Field(default="0.1.0", description="Plugin version")
    description: str = Field(..., description="Plugin description")
    class_name: str = Field(..., description="Plugin class name")
    services: list[ServiceSpec] = Field(default_factory=list, description="Plugin services")
    event_listeners: list[EventListenerSpec] = Field(default_factory=list, description="Event listeners")
    dependencies: list[DependencySpec] = Field(default_factory=list, description="Plugin dependencies")
    config_schema: dict[str, Any] | None = Field(default=None, description="Configuration schema")


class AppWiringContext(BaseModel):
    """Context information about existing app for wiring analysis."""

    app_name: str = Field(..., description="Application name")
    plugins: list[dict[str, Any]] = Field(default_factory=list, description="Existing plugins")
    services: list[dict[str, Any]] = Field(default_factory=list, description="Available services")
    events: list[str] = Field(default_factory=list, description="Available events")
    dependency_graph: dict[str, list[str]] = Field(default_factory=dict, description="Dependency relationships")


class WiringValidationResult(BaseModel):
    """Result of wiring validation."""

    valid: bool = Field(..., description="Whether wiring is valid")
    errors: list[str] = Field(default_factory=list, description="Validation errors")
    warnings: list[str] = Field(default_factory=list, description="Validation warnings")
    suggestions: list[str] = Field(default_factory=list, description="Improvement suggestions")


class PluginGenerationRequest(BaseModel):
    """Request for plugin generation."""

    prompt: str = Field(..., description="Natural language description of desired plugin")
    plugin_name: str | None = Field(default=None, description="Suggested plugin name")
    context_path: str | None = Field(default=None, description="Path to existing app for context")
    validate_wiring: bool = Field(default=True, description="Whether to validate wiring")
    suggest_integrations: bool = Field(default=True, description="Whether to suggest integrations")
    template_type: str | None = Field(default=None, description="Base template type")


class PluginGenerationResult(BaseModel):
    """Result of plugin generation."""

    plugin_spec: PluginSpec = Field(..., description="Generated plugin specification")
    generated_code: str = Field(..., description="Generated plugin code")
    test_code: str = Field(..., description="Generated test code")
    manifest_content: str = Field(..., description="Generated manifest.yaml")
    wiring_validation: WiringValidationResult | None = Field(default=None, description="Wiring validation result")
    generation_metadata: dict[str, Any] = Field(default_factory=dict, description="Generation metadata")


class ValidationError(BaseModel):
    """Validation error details."""

    error_type: str = Field(..., description="Type of validation error")
    message: str = Field(..., description="Error message")
    location: str | None = Field(default=None, description="Location of error")
    suggestion: str | None = Field(default=None, description="Suggested fix")


class WiringSuggestion(BaseModel):
    """Suggestion for improving plugin wiring."""

    suggestion_type: str = Field(..., description="Type of suggestion")
    description: str = Field(..., description="Suggestion description")
    code_example: str | None = Field(default=None, description="Example code")
    confidence: float = Field(..., description="Confidence score (0.0-1.0)")


class ServiceMatch(BaseModel):
    """Match between intent and available service."""

    service_name: str = Field(..., description="Matched service name")
    plugin_name: str = Field(..., description="Plugin providing the service")
    confidence: float = Field(..., description="Match confidence (0.0-1.0)")
    signature: str = Field(..., description="Service signature")
    description: str = Field(..., description="Service description")


class EventIntegration(BaseModel):
    """Suggested event integration."""

    event_pattern: str = Field(..., description="Event pattern to integrate")
    integration_type: str = Field(..., description="Type of integration (listener/emitter)")
    description: str = Field(..., description="Integration description")
    code_example: str = Field(..., description="Example integration code")
    confidence: float = Field(..., description="Integration confidence (0.0-1.0)")
