"""
Wiring analyzer for intelligent plugin integration.

Analyzes existing app structure to provide context-aware plugin generation
with proper service wiring, event integration, and dependency management.
"""

import logging
from pathlib import Path
from typing import Any

from plugginger.ai.types import (
    AppWiringContext,
    PluginSpec,
    WiringSuggestion,
    WiringValidationResult,
)
from plugginger.cli.cmd_inspect import AppInspector
from plugginger.cli.utils import resolve_app_factory
from plugginger.core.exceptions import PluggingerValidationError

logger = logging.getLogger(__name__)


class WiringAnalyzer:
    """Analyzes app wiring context for intelligent plugin generation."""

    def __init__(self) -> None:
        """Initialize wiring analyzer."""
        self.logger = logger

    async def analyze_app_context(self, app_path: str) -> AppWiringContext:
        """Analyze existing app and extract wiring context.

        Args:
            app_path: Path to app factory function (module:function) or manifest.yaml

        Returns:
            AppWiringContext with analyzed app structure

        Raises:
            PluggingerValidationError: If app analysis fails
        """
        self.logger.info(f"Analyzing app context from: {app_path}")

        try:
            # Check if it's a manifest file or app factory
            if app_path.endswith('.yaml') or app_path.endswith('.yml'):
                return await self._analyze_from_manifest(app_path)
            else:
                return await self._analyze_from_factory(app_path)

        except Exception as e:
            raise PluggingerValidationError(f"Failed to analyze app context: {e}") from e

    async def _analyze_from_factory(self, factory_path: str) -> AppWiringContext:
        """Analyze app from factory function using plugginger inspect.

        Args:
            factory_path: Path to app factory function (module:function)

        Returns:
            AppWiringContext with analyzed structure
        """
        try:
            # Load the app builder using existing infrastructure
            app_builder = resolve_app_factory(factory_path)

            # Use AppInspector to analyze the application
            inspector = AppInspector(app_builder)
            analysis_result = inspector.analyze()

            # Extract wiring context from analysis
            return self._extract_wiring_context(analysis_result)

        except Exception as e:
            self.logger.error(f"Failed to analyze app from factory {factory_path}: {e}")
            raise

    async def _analyze_from_manifest(self, manifest_path: str) -> AppWiringContext:
        """Analyze app from manifest.yaml file.

        Args:
            manifest_path: Path to manifest.yaml file

        Returns:
            AppWiringContext with analyzed structure
        """
        try:
            manifest_file = Path(manifest_path)
            if not manifest_file.exists():
                raise FileNotFoundError(f"Manifest file not found: {manifest_path}")

            # For now, return basic context from manifest
            # TODO: Implement full manifest parsing
            return AppWiringContext(
                app_name=manifest_file.stem,
                plugins=[],
                services=[],
                events=[],
                dependency_graph={}
            )

        except Exception as e:
            self.logger.error(f"Failed to analyze app from manifest {manifest_path}: {e}")
            raise

    def _extract_wiring_context(self, analysis_result: dict[str, Any]) -> AppWiringContext:
        """Extract wiring context from AppInspector analysis result.

        Args:
            analysis_result: Result from AppInspector.analyze()

        Returns:
            AppWiringContext with extracted information
        """
        app_info = analysis_result.get("app_info", {})
        plugins = analysis_result.get("plugins", [])
        dependency_graph = analysis_result.get("dependency_graph", {})

        # Extract available services
        services = []
        for plugin in plugins:
            plugin_services = plugin.get("services", [])
            for service in plugin_services:
                services.append({
                    "name": service.get("name", ""),
                    "plugin": plugin.get("registration_name", ""),
                    "signature": service.get("signature", ""),
                    "description": service.get("docstring", ""),
                    "parameters": service.get("parameters", []),
                    "return_type": service.get("return_annotation", "Any")
                })

        # Extract available events
        events = []
        for plugin in plugins:
            event_listeners = plugin.get("event_listeners", [])
            for listener in event_listeners:
                event_pattern = listener.get("event_pattern", "")
                if event_pattern and event_pattern not in events:
                    events.append(event_pattern)

        # Extract dependency relationships
        dependency_relationships: dict[str, list[str]] = {}
        nodes = dependency_graph.get("nodes", [])
        edges = dependency_graph.get("edges", [])

        for node in nodes:
            node_id = node.get("id", "")
            if node_id:
                dependency_relationships[node_id] = []

        for edge in edges:
            source = edge.get("source", "")
            target = edge.get("target", "")
            if source and target:
                if source not in dependency_relationships:
                    dependency_relationships[source] = []
                dependency_relationships[source].append(target)

        return AppWiringContext(
            app_name=app_info.get("name", "unknown"),
            plugins=plugins,
            services=services,
            events=events,
            dependency_graph=dependency_relationships
        )

    async def validate_plugin_compatibility(
        self,
        new_plugin_spec: PluginSpec,
        app_context: AppWiringContext
    ) -> WiringValidationResult:
        """Validate if new plugin is compatible with existing app.

        Args:
            new_plugin_spec: Specification of the new plugin
            app_context: Context of the existing app

        Returns:
            WiringValidationResult with validation outcome
        """
        errors = []
        warnings = []
        suggestions = []

        # Validate service dependencies
        service_errors = self._validate_service_dependencies(new_plugin_spec, app_context)
        errors.extend(service_errors)

        # Validate event patterns
        event_warnings = self._validate_event_patterns(new_plugin_spec, app_context)
        warnings.extend(event_warnings)

        # Check for dependency cycles
        cycle_errors = self._check_dependency_cycles(new_plugin_spec, app_context)
        errors.extend(cycle_errors)

        # Generate integration suggestions
        integration_suggestions = self._generate_integration_suggestions(new_plugin_spec, app_context)
        suggestions.extend(integration_suggestions)

        return WiringValidationResult(
            valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            suggestions=suggestions
        )

    def _validate_service_dependencies(
        self,
        plugin_spec: PluginSpec,
        app_context: AppWiringContext
    ) -> list[str]:
        """Validate service dependencies against available services.

        Args:
            plugin_spec: Plugin specification to validate
            app_context: App context with available services

        Returns:
            List of validation error messages
        """
        errors = []
        {svc["name"]: svc for svc in app_context.services}

        for dependency in plugin_spec.dependencies:
            # Check if dependency provides required services
            dep_name = dependency.name
            if dep_name not in app_context.dependency_graph:
                if not dependency.optional:
                    errors.append(f"Required dependency '{dep_name}' not found in app")

        return errors

    def _validate_event_patterns(
        self,
        plugin_spec: PluginSpec,
        app_context: AppWiringContext
    ) -> list[str]:
        """Validate event patterns against available events.

        Args:
            plugin_spec: Plugin specification to validate
            app_context: App context with available events

        Returns:
            List of validation warning messages
        """
        warnings = []

        for listener in plugin_spec.event_listeners:
            pattern = listener.event_pattern
            # Check if pattern matches any existing events
            matching_events = [event for event in app_context.events if pattern in event or event in pattern]
            if not matching_events:
                warnings.append(f"Event pattern '{pattern}' may not match any existing events")

        return warnings

    def _check_dependency_cycles(
        self,
        plugin_spec: PluginSpec,
        app_context: AppWiringContext
    ) -> list[str]:
        """Check for potential dependency cycles.

        Args:
            plugin_spec: Plugin specification to validate
            app_context: App context with dependency graph

        Returns:
            List of cycle error messages
        """
        errors = []

        # Simple cycle detection - check if any dependency depends on this plugin
        plugin_name = plugin_spec.name

        for dependency in plugin_spec.dependencies:
            dep_name = dependency.name
            if dep_name in app_context.dependency_graph:
                # Check if dependency has transitive dependency on this plugin
                if self._has_transitive_dependency(dep_name, plugin_name, app_context.dependency_graph):
                    errors.append(f"Dependency cycle detected: {plugin_name} -> {dep_name} -> ... -> {plugin_name}")

        return errors

    def _has_transitive_dependency(
        self,
        start: str,
        target: str,
        dependency_graph: dict[str, list[str]],
        visited: set[str] | None = None
    ) -> bool:
        """Check if start has transitive dependency on target.

        Args:
            start: Starting node
            target: Target node to find
            dependency_graph: Dependency graph
            visited: Set of visited nodes (for cycle detection)

        Returns:
            True if transitive dependency exists
        """
        if visited is None:
            visited = set()

        # Don't check self-dependency
        if start == target:
            return False

        if start in visited:
            return False  # Cycle detected, but not the target we're looking for

        visited.add(start)

        dependencies = dependency_graph.get(start, [])
        if target in dependencies:
            return True

        for dep in dependencies:
            if self._has_transitive_dependency(dep, target, dependency_graph, visited.copy()):
                return True

        return False

    def _generate_integration_suggestions(
        self,
        plugin_spec: PluginSpec,
        app_context: AppWiringContext
    ) -> list[str]:
        """Generate suggestions for better plugin integration.

        Args:
            plugin_spec: Plugin specification
            app_context: App context

        Returns:
            List of integration suggestions
        """
        suggestions = []

        # Suggest compatible services
        if not plugin_spec.services:
            suggestions.append("Consider adding services to make this plugin more useful")

        # Suggest event integration
        if not plugin_spec.event_listeners and app_context.events:
            suggestions.append(f"Consider listening to events like: {', '.join(app_context.events[:3])}")

        # Suggest dependencies
        if not plugin_spec.dependencies and app_context.services:
            common_services = ["auth", "database", "logging"]
            available_common = [svc["plugin"] for svc in app_context.services
                              if any(common in svc["name"].lower() for common in common_services)]
            if available_common:
                suggestions.append(f"Consider depending on: {', '.join(set(available_common[:2]))}")

        return suggestions

    async def suggest_wiring_improvements(
        self,
        plugin_spec: PluginSpec,
        app_context: AppWiringContext
    ) -> list[WiringSuggestion]:
        """Suggest improvements for plugin wiring.

        Args:
            plugin_spec: Plugin specification
            app_context: App context

        Returns:
            List of wiring suggestions with confidence scores
        """
        suggestions = []

        # Suggest service integrations
        service_suggestions = self._suggest_service_integrations(plugin_spec, app_context)
        suggestions.extend(service_suggestions)

        # Suggest event integrations
        event_suggestions = self._suggest_event_integrations(plugin_spec, app_context)
        suggestions.extend(event_suggestions)

        return suggestions

    def _suggest_service_integrations(
        self,
        plugin_spec: PluginSpec,
        app_context: AppWiringContext
    ) -> list[WiringSuggestion]:
        """Suggest service integrations based on plugin purpose.

        Args:
            plugin_spec: Plugin specification
            app_context: App context

        Returns:
            List of service integration suggestions
        """
        suggestions = []

        # Analyze plugin description for intent
        description_lower = plugin_spec.description.lower()

        for service in app_context.services:
            service_name = service["name"].lower()
            confidence = 0.0

            # Simple keyword matching for service relevance
            if "auth" in description_lower and "auth" in service_name:
                confidence = 0.8
            elif "database" in description_lower and ("db" in service_name or "database" in service_name):
                confidence = 0.8
            elif "email" in description_lower and "email" in service_name:
                confidence = 0.9
            elif "log" in description_lower and "log" in service_name:
                confidence = 0.7

            if confidence > 0.5:
                suggestions.append(WiringSuggestion(
                    suggestion_type="service_integration",
                    description=f"Use {service['plugin']}.{service['name']} for {service['description'][:50]}...",
                    code_example=f"result = await self.{service['plugin']}.{service['name']}()",
                    confidence=confidence
                ))

        return suggestions

    def _suggest_event_integrations(
        self,
        plugin_spec: PluginSpec,
        app_context: AppWiringContext
    ) -> list[WiringSuggestion]:
        """Suggest event integrations based on plugin purpose.

        Args:
            plugin_spec: Plugin specification
            app_context: App context

        Returns:
            List of event integration suggestions
        """
        suggestions = []

        description_lower = plugin_spec.description.lower()

        for event_pattern in app_context.events:
            confidence = 0.0

            # Simple keyword matching for event relevance
            if "user" in description_lower and "user" in event_pattern.lower():
                confidence = 0.7
            elif "auth" in description_lower and "auth" in event_pattern.lower():
                confidence = 0.8
            elif "email" in description_lower and ("email" in event_pattern.lower() or "message" in event_pattern.lower()):
                confidence = 0.8

            if confidence > 0.5:
                suggestions.append(WiringSuggestion(
                    suggestion_type="event_integration",
                    description=f"Listen to '{event_pattern}' events for reactive behavior",
                    code_example=f"@onevent('{event_pattern}')\nasync def on_{event_pattern.replace('.', '_')}(self, event_data):\n    # Handle event",
                    confidence=confidence
                ))

        return suggestions
