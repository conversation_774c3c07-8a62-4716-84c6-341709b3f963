"""
Service signature matching for intelligent plugin wiring.

Provides type-safe validation of service calls and suggests compatible
services based on intent and signature analysis.
"""

import logging
from typing import Any

from plugginger.ai.types import AppWiringContext, ServiceMatch
from plugginger.core.types import ValidationResult

logger = logging.getLogger(__name__)


class ServiceSignatureMatcher:
    """Matches service calls with available services for type-safe wiring."""

    def __init__(self) -> None:
        """Initialize service signature matcher."""
        self.logger = logger

    def validate_service_call(
        self,
        calling_plugin: str,
        target_service: str,
        call_signature: str,
        available_services: dict[str, Any]
    ) -> ValidationResult:
        """Validate service call against available services.

        Args:
            calling_plugin: Name of the plugin making the call
            target_service: Target service name (plugin.service format)
            call_signature: Signature of the service call
            available_services: Dictionary of available services

        Returns:
            ValidationResult with validation outcome
        """
        self.logger.debug(f"Validating service call: {calling_plugin} -> {target_service}")

        # Check if target service exists
        if target_service not in available_services:
            return ValidationResult(
                valid=False,
                errors=[f"Service '{target_service}' not found"],
                warnings=[],
                metadata={"calling_plugin": calling_plugin, "target_service": target_service}
            )

        service_info = available_services[target_service]

        # Validate signature compatibility
        signature_errors = self._validate_signature_compatibility(call_signature, service_info)

        return ValidationResult(
            valid=len(signature_errors) == 0,
            errors=signature_errors,
            warnings=[],
            metadata={
                "calling_plugin": calling_plugin,
                "target_service": target_service,
                "service_info": service_info
            }
        )

    def _validate_signature_compatibility(
        self,
        call_signature: str,
        service_info: dict[str, Any]
    ) -> list[str]:
        """Validate signature compatibility between call and service.

        Args:
            call_signature: Signature of the service call
            service_info: Information about the target service

        Returns:
            List of validation error messages
        """
        errors = []

        try:
            # Parse call signature (simplified - in real implementation would use AST)
            # For now, just basic validation
            service_info.get("signature", "")
            service_parameters = service_info.get("parameters", [])

            # Check required parameters
            [p for p in service_parameters if p.get("required", True)]

            # Simple validation - check if call looks reasonable
            if not call_signature.strip():
                errors.append("Empty service call signature")

            # Check for obvious type mismatches (simplified)
            if "(" not in call_signature or ")" not in call_signature:
                errors.append("Invalid service call syntax")

        except Exception as e:
            errors.append(f"Failed to validate signature: {e}")

        return errors

    def suggest_compatible_services(
        self,
        intent: str,
        available_services: dict[str, Any]
    ) -> list[ServiceMatch]:
        """Suggest compatible services based on intent.

        Args:
            intent: Natural language description of what the plugin wants to do
            available_services: Dictionary of available services

        Returns:
            List of service matches with confidence scores
        """
        self.logger.debug(f"Finding compatible services for intent: {intent}")

        matches = []
        intent_lower = intent.lower()

        for service_name, service_info in available_services.items():
            confidence = self._calculate_service_match_confidence(intent_lower, service_info)

            if confidence > 0.3:  # Only include reasonably confident matches
                matches.append(ServiceMatch(
                    service_name=service_name,
                    plugin_name=service_info.get("plugin", "unknown"),
                    confidence=confidence,
                    signature=service_info.get("signature", ""),
                    description=service_info.get("description", "")
                ))

        # Sort by confidence (highest first)
        matches.sort(key=lambda x: x.confidence, reverse=True)

        return matches[:10]  # Return top 10 matches

    def _calculate_service_match_confidence(
        self,
        intent: str,
        service_info: dict[str, Any]
    ) -> float:
        """Calculate confidence score for service match.

        Args:
            intent: Lowercase intent string
            service_info: Service information dictionary

        Returns:
            Confidence score between 0.0 and 1.0
        """
        confidence = 0.0

        service_name = service_info.get("name", "").lower()
        service_description = service_info.get("description", "").lower()
        plugin_name = service_info.get("plugin", "").lower()

        # Exact name match
        if intent in service_name or service_name in intent:
            confidence += 0.8

        # Description match
        if intent in service_description or any(word in service_description for word in intent.split()):
            confidence += 0.6

        # Plugin name match
        if intent in plugin_name or plugin_name in intent:
            confidence += 0.4

        # Keyword-based matching
        confidence += self._keyword_match_score(intent, service_name, service_description)

        # Normalize to 0.0-1.0 range
        return min(confidence, 1.0)

    def _keyword_match_score(
        self,
        intent: str,
        service_name: str,
        service_description: str
    ) -> float:
        """Calculate keyword-based match score.

        Args:
            intent: Intent string
            service_name: Service name
            service_description: Service description

        Returns:
            Keyword match score
        """
        score = 0.0

        # Define keyword categories and their weights
        keyword_categories = {
            "auth": ["authenticate", "login", "logout", "token", "user", "session"],
            "database": ["save", "load", "store", "retrieve", "query", "data"],
            "email": ["send", "mail", "message", "notify", "communication"],
            "file": ["upload", "download", "file", "storage", "document"],
            "api": ["request", "response", "http", "rest", "endpoint"],
            "logging": ["log", "debug", "error", "info", "trace"],
            "cache": ["cache", "redis", "memory", "temporary", "fast"],
            "queue": ["queue", "job", "task", "background", "async"]
        }

        set(intent.split())
        service_words = set((service_name + " " + service_description).split())

        for _category, keywords in keyword_categories.items():
            intent_has_category = any(keyword in intent for keyword in keywords)
            service_has_category = any(keyword in service_words for keyword in keywords)

            if intent_has_category and service_has_category:
                score += 0.3

        return score

    def analyze_service_dependencies(
        self,
        plugin_services: list[dict[str, Any]],
        app_context: AppWiringContext
    ) -> dict[str, list[str]]:
        """Analyze service dependencies for a plugin.

        Args:
            plugin_services: List of services provided by the plugin
            app_context: App context with available services

        Returns:
            Dictionary mapping service names to their dependencies
        """
        dependencies = {}

        for service in plugin_services:
            service_name = service.get("name", "")
            service_deps = []

            # Analyze service implementation for dependencies (simplified)
            # In a real implementation, this would parse the service code
            service_description = service.get("description", "").lower()

            # Look for common dependency patterns
            for available_service in app_context.services:
                available_name = available_service["name"].lower()
                available_plugin = available_service["plugin"].lower()

                # Simple heuristic: if service description mentions another service
                if available_name in service_description or available_plugin in service_description:
                    service_deps.append(f"{available_plugin}.{available_name}")

            dependencies[service_name] = service_deps

        return dependencies

    def validate_type_compatibility(
        self,
        source_type: str,
        target_type: str
    ) -> bool:
        """Validate type compatibility between source and target.

        Args:
            source_type: Source type annotation
            target_type: Target type annotation

        Returns:
            True if types are compatible
        """
        # Simplified type compatibility check
        # In a real implementation, this would use proper type analysis

        if source_type == target_type:
            return True

        # Handle common compatible types
        compatible_types = {
            "str": ["str", "Any"],
            "int": ["int", "float", "Any"],
            "float": ["float", "int", "Any"],
            "bool": ["bool", "Any"],
            "dict": ["dict", "Dict", "Any"],
            "list": ["list", "List", "Any"],
            "Any": ["Any"]  # Any is compatible with everything
        }

        source_base = source_type.split("[")[0]  # Remove generic parameters
        target_base = target_type.split("[")[0]

        return target_base in compatible_types.get(source_base, [])

    def suggest_service_improvements(
        self,
        service_spec: dict[str, Any],
        app_context: AppWiringContext
    ) -> list[str]:
        """Suggest improvements for service integration.

        Args:
            service_spec: Service specification
            app_context: App context

        Returns:
            List of improvement suggestions
        """
        suggestions = []

        service_spec.get("name", "")
        service_description = service_spec.get("description", "")

        # Suggest error handling
        if "error" not in service_description.lower():
            suggestions.append("Consider adding error handling documentation")

        # Suggest timeout configuration
        if service_spec.get("timeout_seconds") is None:
            suggestions.append("Consider adding timeout configuration for reliability")

        # Suggest async implementation
        if not service_spec.get("async_method", True):
            suggestions.append("Consider making service async for better performance")

        # Suggest parameter validation
        parameters = service_spec.get("parameters", [])
        if parameters and not any(p.get("required", True) for p in parameters):
            suggestions.append("Consider adding required parameter validation")

        return suggestions
