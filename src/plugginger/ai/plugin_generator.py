"""
AI-powered plugin generator for Plugginger framework.

Provides intelligent plugin generation using LLMs with structured output validation,
wiring analysis, and graceful fallback to static templates.
"""

import logging
from pathlib import Path

from plugginger.ai.grammars import PluginGrammar, PromptTemplates
from plugginger.ai.json_validator import <PERSON><PERSON><PERSON><PERSON><PERSON>da<PERSON>, RetryableJSONProcessor
from plugginger.ai.llm_provider import LLMProvider, LLMProviderFactory
from plugginger.ai.types import (
    PluginGenerationRequest,
    PluginGenerationResult,
    PluginSpec,
    StructuredPrompt,
)
from plugginger.cli.cmd_new import cmd_new_plugin
from plugginger.core.exceptions import PluggingerConfigurationError

logger = logging.getLogger(__name__)


class PluginGenerator:
    """AI-powered plugin generator with fallback to static templates."""

    def __init__(self) -> None:
        """Initialize plugin generator."""
        self.validator = JSONValidator(max_retries=3)
        self.processor = RetryableJSONProcessor(self.validator)
        self._llm_provider: LLMProvider | None = None

    @property
    def llm_provider(self) -> LLMProvider | None:
        """Lazy-loaded LLM provider."""
        if self._llm_provider is None:
            try:
                self._llm_provider = LLMProviderFactory.create_provider()
            except PluggingerConfigurationError as e:
                logger.warning(f"LLM provider not configured: {e}")
                # Keep _llm_provider as None to indicate no provider available
        return self._llm_provider

    async def generate_plugin(self, request: PluginGenerationRequest) -> PluginGenerationResult:
        """Generate plugin using AI or fallback to static template.

        Args:
            request: Plugin generation request

        Returns:
            Plugin generation result with code, tests, and manifest
        """
        logger.info(f"Generating plugin for prompt: {request.prompt}")

        # Try AI generation first
        if self.llm_provider:
            try:
                return await self._generate_with_ai(request)
            except Exception as e:
                logger.warning(f"AI generation failed, falling back to static template: {e}")

        # Fallback to static template
        return await self._generate_with_template(request)

    async def _generate_with_ai(self, request: PluginGenerationRequest) -> PluginGenerationResult:
        """Generate plugin using AI.

        Args:
            request: Plugin generation request

        Returns:
            AI-generated plugin result
        """
        logger.info("Generating plugin with AI")

        # Load app context if provided
        app_context = ""
        if request.context_path:
            app_context = await self._load_app_context(request.context_path)

        # Create structured prompt
        prompt = StructuredPrompt(
            system_message=PromptTemplates.plugin_generation_prompt(
                user_request=request.prompt,
                context=app_context
            ),
            user_message=f"Generate a plugin specification for: {request.prompt}",
            ebnf_grammar=PluginGrammar.plugin_spec_grammar()
        )

        # Generate plugin specification
        plugin_spec = await self.processor.process_with_retry(
            llm_provider=self.llm_provider,
            prompt=prompt,
            target_model=PluginSpec,
            grammar_validator="validate_plugin_spec_json",
            max_retries=3
        )

        if not plugin_spec:
            raise ValueError("Failed to generate valid plugin specification")

        # Generate code from specification
        generated_code = self._generate_code_from_spec(plugin_spec)
        test_code = self._generate_test_code_from_spec(plugin_spec)
        manifest_content = self._generate_manifest_from_spec(plugin_spec)

        return PluginGenerationResult(
            plugin_spec=plugin_spec,
            generated_code=generated_code,
            test_code=test_code,
            manifest_content=manifest_content,
            generation_metadata={
                "method": "ai",
                "llm_provider": self.llm_provider.default_model if self.llm_provider else "unknown",
                "context_used": bool(request.context_path)
            }
        )

    async def _generate_with_template(self, request: PluginGenerationRequest) -> PluginGenerationResult:
        """Generate plugin using static template as fallback.

        Args:
            request: Plugin generation request

        Returns:
            Template-generated plugin result
        """
        logger.info("Generating plugin with static template")

        # Derive plugin name from prompt or use provided name
        plugin_name = request.plugin_name or self._derive_plugin_name(request.prompt)

        # Create temporary directory for generation
        import tempfile
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Use existing template generation
            cmd_new_plugin(plugin_name, temp_path)

            # Read generated files
            plugin_path = temp_path / plugin_name
            generated_code = (plugin_path / f"{plugin_name.replace('-', '_')}.py").read_text()
            test_code = (plugin_path / "tests" / f"test_{plugin_name.replace('-', '_')}.py").read_text()
            manifest_content = (plugin_path / "manifest.yaml").read_text()

        # Create basic plugin spec from template
        plugin_spec = PluginSpec(
            name=plugin_name.replace('-', '_'),
            description=f"Plugin generated from prompt: {request.prompt}",
            class_name=f"{plugin_name.replace('-', '_').title().replace('_', '')}Plugin",
            services=[],  # Template services will be in the code
            event_listeners=[],
            dependencies=[]
        )

        return PluginGenerationResult(
            plugin_spec=plugin_spec,
            generated_code=generated_code,
            test_code=test_code,
            manifest_content=manifest_content,
            generation_metadata={
                "method": "template",
                "template_type": "basic",
                "fallback_reason": "LLM not available or failed"
            }
        )

    async def _load_app_context(self, context_path: str) -> str:
        """Load app context for wiring analysis.

        Args:
            context_path: Path to existing app

        Returns:
            App context string for LLM
        """
        # TODO: Implement app context loading using plugginger inspect
        # For now, return empty context
        logger.info(f"Loading app context from: {context_path}")
        return ""

    def _derive_plugin_name(self, prompt: str) -> str:
        """Derive plugin name from user prompt.

        Args:
            prompt: User's natural language prompt

        Returns:
            Derived plugin name
        """
        # Simple name derivation - can be enhanced with NLP
        words = prompt.lower().split()
        # Take first few meaningful words
        meaningful_words = [w for w in words[:3] if len(w) > 2 and w.isalpha()]
        if not meaningful_words:
            return "generated-plugin"

        return "-".join(meaningful_words)

    def _generate_code_from_spec(self, spec: PluginSpec) -> str:
        """Generate Python code from plugin specification.

        Args:
            spec: Plugin specification

        Returns:
            Generated Python code
        """
        # TODO: Implement code generation from spec
        # For now, return basic template
        return f'''from plugginger.api import plugin, service, PluginBase

@plugin(name="{spec.name}", version="{spec.version}")
class {spec.class_name}(PluginBase):
    """
    {spec.description}
    """

    @service()
    async def hello(self) -> str:
        """Hello service

        Returns a greeting message from the plugin.

        Returns:
            str: Greeting message from the plugin
        """
        return "Hello from {spec.name}!"
'''

    def _generate_test_code_from_spec(self, spec: PluginSpec) -> str:
        """Generate test code from plugin specification.

        Args:
            spec: Plugin specification

        Returns:
            Generated test code
        """
        # TODO: Implement test generation from spec
        # For now, return basic template
        return f'''import pytest
from plugginger.api import PluggingerAppBuilder

from {spec.name} import {spec.class_name}

@pytest.mark.asyncio
async def test_{spec.name}_hello_service():
    builder = PluggingerAppBuilder(app_name="test_app")
    builder.include({spec.class_name})
    app = builder.build()

    result = await app.call_service("{spec.name}.hello")
    assert result == "Hello from {spec.name}!"
'''

    def _generate_manifest_from_spec(self, spec: PluginSpec) -> str:
        """Generate manifest.yaml from plugin specification.

        Args:
            spec: Plugin specification

        Returns:
            Generated manifest content
        """
        return f'''name: {spec.name}
version: {spec.version}
description: {spec.description}
plugins:
  - module: {spec.name}
    class: {spec.class_name}
'''
