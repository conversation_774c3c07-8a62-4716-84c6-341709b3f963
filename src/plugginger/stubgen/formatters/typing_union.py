"""Formatter for Union and Optional type annotations.

This module extracts the Union/Optional formatting logic from the D-26 complexity
_format_generic_with_origin method, providing focused, single-responsibility formatting.
"""

from __future__ import annotations

import logging
from typing import Any, Union, get_args, get_origin

from plugginger.stubgen.formatters.base import BaseTypeFormatter

logger = logging.getLogger(__name__)


class TypingUnionFormatter(BaseTypeFormatter):
    """Formatter for Union and Optional type annotations.

    Handles:
    - Union[X, Y, Z] -> Union[X, Y, Z]
    - Union[X, None] -> Optional[X]
    - X | Y (Python 3.10+) -> Union[X, Y]
    - X | None (Python 3.10+) -> Optional[X]
    """

    def can_format(self, annotation: Any) -> bool:
        """Check if this formatter can handle Union or Optional types.

        Args:
            annotation: The type annotation to check

        Returns:
            True if this is a Union type or Python 3.10+ union syntax
        """
        origin = get_origin(annotation)

        # Handle typing.Union
        if origin is Union:
            return True

        # Handle Python 3.10+ union syntax (X | Y)
        if hasattr(annotation, "__class__") and hasattr(annotation.__class__, "__name__"):
            if annotation.__class__.__name__ == "UnionType":
                return True

        return False

    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """Format Union and Optional type annotations.

        Args:
            annotation: The Union type annotation to format
            recursion_depth: Current recursion depth

        Returns:
            Formatted string (e.g., "Union[X, Y]" or "Optional[X]")
        """
        self._check_recursion_depth(recursion_depth)

        args = get_args(annotation)

        # Handle empty Union (shouldn't happen but be safe)
        if not args:
            return "Union"

        # Check for Optional pattern: Union[X, None] or Union[None, X]
        if len(args) == 2 and type(None) in args:
            # Find the non-None type
            non_none_type = args[0] if args[1] is type(None) else args[1]
            formatted_type = self._format_recursively(non_none_type, recursion_depth)
            return f"Optional[{formatted_type}]"

        # Handle regular Union with multiple types
        formatted_args = []
        for arg in args:
            formatted_arg = self._format_recursively(arg, recursion_depth)
            formatted_args.append(formatted_arg)

        return f"Union[{', '.join(formatted_args)}]"


def is_union_type(annotation: Any) -> bool:
    """Check if an annotation is a Union type.

    Args:
        annotation: The annotation to check

    Returns:
        True if this is a Union type (including Python 3.10+ syntax)
    """
    origin = get_origin(annotation)

    # Handle typing.Union
    if origin is Union:
        return True

    # Handle Python 3.10+ union syntax (X | Y)
    if hasattr(annotation, "__class__") and hasattr(annotation.__class__, "__name__"):
        if annotation.__class__.__name__ == "UnionType":
            return True

    return False


def is_optional_type(annotation: Any) -> bool:
    """Check if an annotation represents an Optional type.

    Args:
        annotation: The annotation to check

    Returns:
        True if this is Optional[X] (i.e., Union[X, None])
    """
    if not is_union_type(annotation):
        return False

    args = get_args(annotation)
    return len(args) == 2 and type(None) in args


def extract_optional_inner_type(annotation: Any) -> Any:
    """Extract the inner type from an Optional annotation.

    Args:
        annotation: The Optional[X] annotation

    Returns:
        The inner type X

    Raises:
        ValueError: If annotation is not Optional
    """
    if not is_optional_type(annotation):
        raise ValueError(f"Annotation {annotation!r} is not Optional")

    args = get_args(annotation)
    return args[0] if args[1] is type(None) else args[1]
