"""Formatter for TypeVar annotations."""

from __future__ import annotations

from typing import Any, TypeVar

from plugginger.stubgen.formatters.base import BaseTypeFormatter


class TypeVarFormatter(BaseTypeFormatter):
    """Formatter for TypeVar annotations."""

    def can_format(self, annotation: Any) -> bool:
        """Check if this is a TypeVar."""
        return isinstance(annotation, TypeVar)

    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """Format TypeVar annotations."""
        if isinstance(annotation, TypeVar):
            return annotation.__name__
        return str(annotation)
