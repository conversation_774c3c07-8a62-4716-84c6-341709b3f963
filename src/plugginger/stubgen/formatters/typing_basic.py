"""Formatter for basic typing generics like <PERSON>, Dict, Tuple."""

from __future__ import annotations

from typing import Any, get_args, get_origin

from plugginger.stubgen.formatters.base import BaseTypeFormatter


class TypingBasicFormatter(BaseTypeFormatter):
    """Formatter for basic typing generics."""

    # Mapping of origins to display names
    ORIGIN_NAME_MAP = {
        list: "List",
        dict: "Dict",
        tuple: "Tuple",
        set: "Set",
    }

    def can_format(self, annotation: Any) -> bool:
        """Check if this is a basic typing generic."""
        origin = get_origin(annotation)
        if origin in self.ORIGIN_NAME_MAP:
            return True

        # Check for typing module generics
        if hasattr(annotation, "_name") and annotation._name:
            return annotation._name in ("List", "Dict", "Tuple", "Set")

        return False

    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """Format basic typing generics."""
        self._check_recursion_depth(recursion_depth)

        origin = get_origin(annotation)
        args = get_args(annotation)

        # Get display name
        if origin in self.ORIGIN_NAME_MAP:
            display_name = self.ORIGIN_NAME_MAP[origin]
        elif hasattr(annotation, "_name") and annotation._name:
            display_name = annotation._name
        else:
            display_name = str(origin)

        # Handle no args
        if not args:
            return display_name

        # Handle Tuple with ellipsis
        if display_name == "Tuple" and len(args) == 2 and args[1] is Ellipsis:
            formatted_type = self._format_recursively(args[0], recursion_depth)
            return f"Tuple[{formatted_type}, ...]"

        # Format all args
        formatted_args = []
        for arg in args:
            formatted_arg = self._format_recursively(arg, recursion_depth)
            formatted_args.append(formatted_arg)

        return f"{display_name}[{', '.join(formatted_args)}]"
