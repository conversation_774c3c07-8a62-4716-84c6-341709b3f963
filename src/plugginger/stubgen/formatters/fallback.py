"""Fallback formatter for any types not handled by other formatters."""

from __future__ import annotations

import logging
from typing import Any

from plugginger.stubgen.formatters.base import BaseTypeFormatter

logger = logging.getLogger(__name__)


class FallbackFormatter(BaseTypeFormatter):
    """Fallback formatter for any types not handled by more specific formatters."""

    def can_format(self, annotation: Any) -> bool:
        """Always returns True as this is the ultimate fallback."""
        return True

    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """Provide fallback string representation."""
        logger.warning(
            f"FallbackFormatter: Using fallback for type: {annotation!r}. "
            "Consider adding a specific formatter if this type appears often."
        )

        try:
            s = str(annotation)
            # Heuristics for unhelpful str() representations
            if len(s) > 70 or s.startswith("<") or (" at 0x" in s):
                return "Any"
            # Clean up common typing prefixes
            if s.startswith("typing."):
                s = s[len("typing."):]
            if s.startswith("typing_extensions."):
                s = s[len("typing_extensions."):]
            return s
        except Exception:
            return "Any"
