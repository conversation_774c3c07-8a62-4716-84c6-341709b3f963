"""Formatter for ForwardRef annotations."""

from __future__ import annotations

from typing import Any, ForwardRef

from plugginger.stubgen.formatters.base import BaseTypeFormatter


class ForwardRefFormatter(BaseTypeFormatter):
    """Formatter for ForwardRef annotations."""

    def can_format(self, annotation: Any) -> bool:
        """Check if this is a ForwardRef."""
        return isinstance(annotation, ForwardRef)

    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """Format ForwardRef annotations."""
        if isinstance(annotation, ForwardRef):
            return annotation.__forward_arg__
        return str(annotation)
