"""Type annotation formatters for stub generation.

This package provides modular, single-responsibility formatters that replace
the monolithic D-26 complexity TypingModuleFormatter.
"""

from plugginger.stubgen.formatters.base import (
    BaseTypeFormatter,
    FormatterError,
    TypeFormatterProtocol,
)
from plugginger.stubgen.formatters.manager import (
    TypeFormatterManager,
    create_default_formatter_manager,
    format_type_annotation,
    get_default_manager,
)

__all__ = [
    "BaseTypeFormatter",
    "FormatterError",
    "TypeFormatterProtocol",
    "TypeFormatterManager",
    "create_default_formatter_manager",
    "format_type_annotation",
    "get_default_manager",
]
