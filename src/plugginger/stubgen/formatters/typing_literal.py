"""Formatter for Literal type annotations.

This module extracts the Literal formatting logic from the D-26 complexity
_format_generic_with_origin method, providing focused, single-responsibility formatting.
"""

from __future__ import annotations

import logging
from typing import Any, get_args, get_origin

try:
    from typing import Literal
except ImportError:
    from typing import Literal

from plugginger.stubgen.formatters.base import BaseTypeFormatter

logger = logging.getLogger(__name__)


class TypingLiteralFormatter(BaseTypeFormatter):
    """Formatter for Literal type annotations.

    Handles:
    - Literal["value"] -> Literal["value"]
    - Literal[1, 2, 3] -> Literal[1, 2, 3]
    - Literal[True, False] -> Literal[True, False]
    """

    def can_format(self, annotation: Any) -> bool:
        """Check if this formatter can handle Literal types.

        Args:
            annotation: The type annotation to check

        Returns:
            True if this is a Literal type
        """
        origin = get_origin(annotation)

        # Handle typing.Literal or typing_extensions.Literal
        if origin is Literal:
            return True

        # Handle direct Literal reference
        if annotation is Literal:
            return True

        # Handle string representation check for edge cases
        annotation_str = str(annotation)
        if annotation_str.startswith("Literal"):
            return True

        return False

    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """Format Literal type annotations.

        Args:
            annotation: The Literal type annotation to format
            recursion_depth: Current recursion depth

        Returns:
            Formatted string (e.g., "Literal['value1', 'value2']")
        """
        self._check_recursion_depth(recursion_depth)

        # Handle bare Literal without values
        if annotation is Literal:
            return "Literal"

        args = get_args(annotation)

        # Handle Literal without arguments
        if not args:
            return "Literal"

        # Format each literal value
        formatted_values = []
        for value in args:
            formatted_value = self._format_literal_value(value)
            formatted_values.append(formatted_value)

        return f"Literal[{', '.join(formatted_values)}]"

    def _format_literal_value(self, value: Any) -> str:
        """Format a single literal value for inclusion in Literal type.

        Args:
            value: The literal value to format

        Returns:
            String representation of the value suitable for Literal
        """
        # Use repr() to get proper string representation with quotes
        # This handles strings, numbers, booleans, None, etc.
        try:
            return repr(value)
        except Exception as e:
            logger.warning(f"Failed to format literal value {value!r}: {e}")
            return str(value)


def is_literal_type(annotation: Any) -> bool:
    """Check if an annotation is a Literal type.

    Args:
        annotation: The annotation to check

    Returns:
        True if this is a Literal type
    """
    origin = get_origin(annotation)

    # Handle typing.Literal or typing_extensions.Literal
    if origin is Literal:
        return True

    # Handle direct Literal reference
    if annotation is Literal:
        return True

    return False


def extract_literal_values(annotation: Any) -> tuple[Any, ...]:
    """Extract the literal values from a Literal annotation.

    Args:
        annotation: The Literal annotation

    Returns:
        Tuple of literal values

    Raises:
        ValueError: If annotation is not a Literal
    """
    if not is_literal_type(annotation):
        raise ValueError(f"Annotation {annotation!r} is not a Literal")

    if annotation is Literal:
        return ()

    return get_args(annotation)


def create_literal_annotation(*values: Any) -> Any:
    """Create a Literal annotation from the given values.

    Args:
        *values: The literal values to include

    Returns:
        Literal annotation
    """
    if not values:
        return Literal

    # Use subscript notation to create Literal[value1, value2, ...]
    if len(values) == 1:
        return Literal[values[0]]
    else:
        return Literal[values]
