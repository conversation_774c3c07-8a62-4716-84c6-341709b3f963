"""Formatter for user-defined classes and types."""

from __future__ import annotations

import inspect
import logging
from typing import Any, get_origin

from plugginger.stubgen.formatters.base import (
    BaseTypeFormatter,
    get_annotation_module,
    get_annotation_name,
)

logger = logging.getLogger(__name__)


class UserDefinedTypeFormatter(BaseTypeFormatter):
    """Formatter for user-defined classes and types."""

    def can_format(self, annotation: Any) -> bool:
        """Check if this is a user-defined class/type."""
        if not inspect.isclass(annotation):
            return False

        module_name = get_annotation_module(annotation)

        # Skip built-in and typing modules
        if module_name in ("builtins", "typing", "collections.abc", "typing_extensions"):
            # But allow if it has a generic origin (handled elsewhere)
            if get_origin(annotation) is not None:
                return False
            # Skip common built-in types
            if annotation in (int, str, float, bool, bytes, list, dict, tuple, set, type(None)):
                return False
            # Skip Any
            from typing import Any as TypingAny
            if annotation is TypingAny:
                return False
            # Allow other typing classes not caught by other formatters
            return True

        return True  # User-defined type from another module

    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """Format user-defined types."""
        module_name = get_annotation_module(annotation)
        qual_name = get_annotation_name(annotation)

        if not qual_name:
            logger.warning(f"UserDefinedTypeFormatter: Could not get name for {annotation!r}")
            return "Any"

        if module_name and module_name not in ("builtins", "__main__"):
            return f"{module_name}.{qual_name}"
        return qual_name
