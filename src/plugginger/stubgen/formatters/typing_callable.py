"""Formatter for Callable type annotations.

This module extracts the Callable formatting logic from the D-26 complexity
_format_generic_with_origin method, providing focused, single-responsibility formatting.
"""

from __future__ import annotations

import logging
from collections.abc import Callable
from typing import Any, get_args, get_origin

from plugginger.stubgen.formatters.base import BaseTypeFormatter

logger = logging.getLogger(__name__)


class TypingCallableFormatter(BaseTypeFormatter):
    """Formatter for Callable type annotations.

    Handles:
    - Callable[[int, str], bool] -> Callable[[int, str], bool]
    - Callable[..., Any] -> Callable[..., Any]
    - collections.abc.Callable -> Callable[..., Any]
    """

    def can_format(self, annotation: Any) -> bool:
        """Check if this formatter can handle Callable types.

        Args:
            annotation: The type annotation to check

        Returns:
            True if this is a Callable type
        """
        origin = get_origin(annotation)

        # Handle typing.Callable and collections.abc.Callable
        if origin is not None:
            origin_name = getattr(origin, "__name__", "")
            if "Callable" in origin_name:
                return True

        # Handle direct Callable reference
        if annotation is Callable:
            return True

        # Handle string representation check for edge cases
        annotation_str = str(annotation)
        if "Callable" in annotation_str:
            return True

        return False

    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """Format Callable type annotations.

        Args:
            annotation: The Callable type annotation to format
            recursion_depth: Current recursion depth

        Returns:
            Formatted string (e.g., "Callable[[int, str], bool]")
        """
        self._check_recursion_depth(recursion_depth)

        args = get_args(annotation)

        # Handle bare Callable without parameters
        if not args:
            return "Callable[..., Any]"

        # Handle Callable with single argument (return type only)
        if len(args) == 1:
            return_type = self._format_recursively(args[0], recursion_depth)
            return f"Callable[..., {return_type}]"

        # Handle Callable with parameter types and return type
        if len(args) >= 2:
            param_types = args[0]
            return_type = args[-1]

            # Format return type
            formatted_return = self._format_recursively(return_type, recursion_depth)

            # Format parameter types
            if param_types is Ellipsis:
                # Callable[..., ReturnType]
                return f"Callable[..., {formatted_return}]"
            elif isinstance(param_types, list):
                # Callable[[ParamType1, ParamType2], ReturnType]
                formatted_params = []
                for param_type in param_types:
                    formatted_param = self._format_recursively(param_type, recursion_depth)
                    formatted_params.append(formatted_param)
                params_str = f"[{', '.join(formatted_params)}]"
                return f"Callable[{params_str}, {formatted_return}]"
            else:
                # Fallback for unexpected parameter format
                logger.warning(
                    f"Unexpected Callable parameter format: {param_types!r}. "
                    "Using ellipsis."
                )
                return f"Callable[..., {formatted_return}]"

        # Fallback for unexpected Callable format
        logger.warning(f"Unexpected Callable format: {annotation!r}")
        return "Callable[..., Any]"


def is_callable_type(annotation: Any) -> bool:
    """Check if an annotation is a Callable type.

    Args:
        annotation: The annotation to check

    Returns:
        True if this is a Callable type
    """
    origin = get_origin(annotation)

    # Handle typing.Callable and collections.abc.Callable
    if origin is not None:
        origin_name = getattr(origin, "__name__", "")
        if "Callable" in origin_name:
            return True

    # Handle direct Callable reference
    if annotation is Callable:
        return True

    return False


def extract_callable_signature(annotation: Any) -> tuple[list[Any] | None, Any]:
    """Extract parameter types and return type from a Callable annotation.

    Args:
        annotation: The Callable annotation

    Returns:
        Tuple of (parameter_types, return_type)
        parameter_types is None if Callable[..., ReturnType]

    Raises:
        ValueError: If annotation is not a Callable
    """
    if not is_callable_type(annotation):
        raise ValueError(f"Annotation {annotation!r} is not a Callable")

    args = get_args(annotation)

    if not args:
        return None, Any

    if len(args) == 1:
        return None, args[0]

    if len(args) >= 2:
        param_types = args[0]
        return_type = args[-1]

        if param_types is Ellipsis:
            return None, return_type
        elif isinstance(param_types, list):
            return param_types, return_type

    return None, Any
