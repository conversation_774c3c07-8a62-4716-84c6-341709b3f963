# src/plugginger/services/manifest_service.py

"""
Manifest generation service.

This service eliminates circular dependencies between api and schemas modules
by providing a centralized service for manifest generation operations.
"""

from __future__ import annotations

from pathlib import Path
from typing import TYPE_CHECKING

# Import both api and schemas modules - this is safe because this service
# sits above both in the dependency hierarchy
from plugginger.schemas import (
    generate_app_manifest,
    generate_plugin_manifest,
    manifest_to_yaml,
)

if TYPE_CHECKING:
    from plugginger.api.app import PluggingerAppInstance
    from plugginger.api.builder import PluggingerAppBuilder


class ManifestService:
    """
    Service for generating and exporting plugin and application manifests.

    This service eliminates circular dependencies by providing a centralized
    location for manifest operations that can import both api and schemas modules.
    """

    @staticmethod
    def export_manifests_from_builder(
        builder: PluggingerAppBuilder,
        output_dir: str
    ) -> dict[str, str]:
        """
        Export manifests for all registered plugins from a builder.

        Args:
            builder: The PluggingerAppBuilder instance
            output_dir: Directory to export manifests to

        Returns:
            Dictionary mapping plugin names to exported file paths

        Raises:
            ValueError: If no plugins are registered or output directory is invalid
        """
        if not builder._registered_item_classes:
            raise ValueError("No plugins registered - cannot export manifests")

        if not output_dir or not isinstance(output_dir, str):
            raise ValueError("output_dir must be a non-empty string")

        # Create output directory if it doesn't exist
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        exported_files: dict[str, str] = {}

        # Export individual plugin manifests
        for registration_name, plugin_class in builder._registered_item_classes.items():
            if hasattr(plugin_class, '__plugginger_metadata__'):
                manifest = generate_plugin_manifest(plugin_class)
                yaml_content = manifest_to_yaml(manifest)

                manifest_file = output_path / f"{registration_name}_manifest.yaml"
                manifest_file.write_text(yaml_content, encoding="utf-8")
                exported_files[registration_name] = str(manifest_file)

        # Export application manifest
        app_manifest = generate_app_manifest(
            app_name=builder._app_name,
            app_version="1.0.0",  # Default version
            plugin_classes=list(builder._registered_item_classes.values()),
            global_config={}  # No global config available from builder
        )
        app_yaml_content = manifest_to_yaml(app_manifest)

        app_manifest_file = output_path / "app_manifest.yaml"
        app_manifest_file.write_text(app_yaml_content, encoding="utf-8")
        exported_files["app"] = str(app_manifest_file)

        return exported_files

    @staticmethod
    def export_manifests_from_app(
        app: PluggingerAppInstance,
        output_dir: str
    ) -> dict[str, str]:
        """
        Export manifests for all registered plugins from an app instance.

        Args:
            app: The PluggingerAppInstance
            output_dir: Directory to export manifests to

        Returns:
            Dictionary mapping plugin names to exported file paths

        Raises:
            ValueError: If no plugins are registered or output directory is invalid
        """
        if not app._registered_plugin_classes:
            raise ValueError("No plugins registered - cannot export manifests")

        if not output_dir or not isinstance(output_dir, str):
            raise ValueError("output_dir must be a non-empty string")

        # Create output directory if it doesn't exist
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        exported_files: dict[str, str] = {}

        # Export individual plugin manifests
        for registration_name, plugin_class in app._registered_plugin_classes.items():
            if hasattr(plugin_class, '__plugginger_metadata__'):
                manifest = generate_plugin_manifest(plugin_class)
                yaml_content = manifest_to_yaml(manifest)

                manifest_file = output_path / f"{registration_name}_manifest.yaml"
                manifest_file.write_text(yaml_content, encoding="utf-8")
                exported_files[registration_name] = str(manifest_file)

        # Export application manifest
        app_manifest = generate_app_manifest(
            app_name=app._app_name,
            app_version="1.0.0",  # Default version
            plugin_classes=list(app._registered_plugin_classes.values()),
            global_config=app._global_config.model_dump() if hasattr(app._global_config, 'model_dump') else {}
        )
        app_yaml_content = manifest_to_yaml(app_manifest)

        app_manifest_file = output_path / "app_manifest.yaml"
        app_manifest_file.write_text(app_yaml_content, encoding="utf-8")
        exported_files["app"] = str(app_manifest_file)

        return exported_files
