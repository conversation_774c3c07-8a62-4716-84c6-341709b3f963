# src/plugginger/services/type_safety_service.py

"""
Type safety analysis and improvement service.

This service provides comprehensive type safety analysis and automatic
improvements for the Plugginger framework, addressing the R4 requirements.
"""

from __future__ import annotations

import ast
import logging
from pathlib import Path
from typing import TYPE_CHECKING, Any

if TYPE_CHECKING:
    pass


class TypeSafetyService:
    """
    Service for analyzing and improving type safety across the framework.

    This service addresses R4 requirements by providing:
    - Type annotation analysis and validation
    - Any type usage detection and replacement suggestions
    - API consistency checking across modules
    - Protocol definition validation
    """

    def __init__(self, logger: logging.Logger) -> None:
        """Initialize the TypeSafetyService with a logger."""
        self._logger = logger

    def analyze_module_type_safety(self, module_path: Path) -> dict[str, Any]:
        """
        Analyze type safety issues in a Python module.

        Args:
            module_path: Path to the Python module to analyze

        Returns:
            Dictionary containing type safety analysis results
        """
        self._logger.info(f"Analyzing type safety for module: {module_path}")

        try:
            with open(module_path, encoding='utf-8') as f:
                source_code = f.read()

            tree = ast.parse(source_code)
            analyzer = TypeSafetyAnalyzer()
            analyzer.visit(tree)

            results = {
                "module_path": str(module_path),
                "missing_annotations": analyzer.missing_annotations,
                "any_usage": analyzer.any_usage,
                "inconsistent_patterns": analyzer.inconsistent_patterns,
                "protocol_violations": analyzer.protocol_violations,
                "improvement_suggestions": self._generate_improvement_suggestions(analyzer)
            }

            self._logger.info(f"Type safety analysis complete for {module_path}")
            return results

        except Exception as e:
            self._logger.error(f"Failed to analyze type safety for {module_path}: {e}")
            return {
                "module_path": str(module_path),
                "error": str(e),
                "missing_annotations": [],
                "any_usage": [],
                "inconsistent_patterns": [],
                "protocol_violations": [],
                "improvement_suggestions": []
            }

    def analyze_api_consistency(self, api_modules: list[Path]) -> dict[str, Any]:
        """
        Analyze API consistency across multiple modules.

        Args:
            api_modules: List of paths to API modules to analyze

        Returns:
            Dictionary containing API consistency analysis results
        """
        self._logger.info(f"Analyzing API consistency across {len(api_modules)} modules")

        api_patterns = {}
        inconsistencies = []

        for module_path in api_modules:
            try:
                module_analysis = self.analyze_module_type_safety(module_path)
                api_patterns[str(module_path)] = module_analysis

                # Check for common inconsistency patterns
                inconsistencies.extend(self._detect_api_inconsistencies(module_analysis))

            except Exception as e:
                self._logger.error(f"Failed to analyze API consistency for {module_path}: {e}")
                inconsistencies.append({
                    "module": str(module_path),
                    "type": "analysis_error",
                    "description": f"Failed to analyze module: {e}"
                })

        return {
            "analyzed_modules": len(api_modules),
            "api_patterns": api_patterns,
            "inconsistencies": inconsistencies,
            "consistency_score": self._calculate_consistency_score(inconsistencies, len(api_modules))
        }

    def suggest_type_improvements(self, analysis_results: dict[str, Any]) -> list[dict[str, Any]]:
        """
        Generate specific type improvement suggestions based on analysis.

        Args:
            analysis_results: Results from type safety analysis

        Returns:
            List of improvement suggestions with specific actions
        """
        suggestions = []

        # Suggest specific type annotations for missing ones
        for missing in analysis_results.get("missing_annotations", []):
            suggestions.append({
                "type": "add_type_annotation",
                "location": missing,
                "priority": "high",
                "description": f"Add type annotation for {missing['name']}",
                "suggested_fix": self._suggest_type_annotation(missing)
            })

        # Suggest replacements for Any usage
        for any_usage in analysis_results.get("any_usage", []):
            suggestions.append({
                "type": "replace_any_type",
                "location": any_usage,
                "priority": "medium",
                "description": f"Replace Any with more specific type in {any_usage['context']}",
                "suggested_fix": self._suggest_any_replacement(any_usage)
            })

        # Suggest fixes for inconsistent patterns
        for inconsistency in analysis_results.get("inconsistent_patterns", []):
            suggestions.append({
                "type": "fix_inconsistency",
                "location": inconsistency,
                "priority": "medium",
                "description": f"Fix inconsistent pattern: {inconsistency['description']}",
                "suggested_fix": inconsistency.get("suggested_fix", "Manual review required")
            })

        return suggestions

    def _generate_improvement_suggestions(self, analyzer: TypeSafetyAnalyzer) -> list[str]:
        """Generate high-level improvement suggestions based on analysis."""
        suggestions = []

        if analyzer.missing_annotations:
            suggestions.append(f"Add type annotations to {len(analyzer.missing_annotations)} functions/methods")

        if analyzer.any_usage:
            suggestions.append(f"Replace {len(analyzer.any_usage)} Any types with more specific types")

        if analyzer.inconsistent_patterns:
            suggestions.append(f"Fix {len(analyzer.inconsistent_patterns)} inconsistent API patterns")

        if analyzer.protocol_violations:
            suggestions.append(f"Address {len(analyzer.protocol_violations)} protocol violations")

        return suggestions

    def _detect_api_inconsistencies(self, module_analysis: dict[str, Any]) -> list[dict[str, Any]]:
        """Detect API inconsistencies in a module analysis."""
        inconsistencies = []

        # Check for inconsistent parameter naming patterns
        # Check for inconsistent return type patterns
        # Check for inconsistent error handling patterns

        # This is a simplified implementation - can be expanded
        if len(module_analysis.get("any_usage", [])) > 5:
            inconsistencies.append({
                "type": "excessive_any_usage",
                "description": f"Module has {len(module_analysis['any_usage'])} Any types - consider more specific typing",
                "severity": "medium"
            })

        return inconsistencies

    def _calculate_consistency_score(self, inconsistencies: list[dict[str, Any]], module_count: int) -> float:
        """Calculate a consistency score based on inconsistencies found."""
        if module_count == 0:
            return 0.0

        # Simple scoring: reduce score based on inconsistencies
        base_score = 100.0
        penalty_per_inconsistency = 5.0

        total_penalty = len(inconsistencies) * penalty_per_inconsistency
        score = max(0.0, base_score - total_penalty)

        return round(score, 2)

    def _suggest_type_annotation(self, missing_annotation: dict[str, Any]) -> str:
        """Suggest a specific type annotation for a missing one."""
        name = missing_annotation.get("name", "unknown")
        context = missing_annotation.get("context", "")

        # Simple heuristics for common patterns
        if "config" in name.lower():
            return "dict[str, Any]"
        elif "logger" in name.lower():
            return "logging.Logger"
        elif name.endswith("_id"):
            return "str"
        elif name.endswith("_count"):
            return "int"
        elif "async" in context:
            return "Awaitable[Any]"
        else:
            return "Any  # TODO: Specify more precise type"

    def _suggest_any_replacement(self, any_usage: dict[str, Any]) -> str:
        """Suggest a replacement for Any type usage."""
        context = any_usage.get("context", "")

        # Simple heuristics for common Any replacements
        if "dict" in context.lower():
            return "dict[str, Any]  # Consider more specific value type"
        elif "list" in context.lower():
            return "list[Any]  # Consider more specific element type"
        elif "config" in context.lower():
            return "BaseModel  # Use Pydantic model for type safety"
        elif "return" in context.lower():
            return "Specific return type based on method purpose"
        else:
            return "Consider Protocol or specific type based on usage"


class TypeSafetyAnalyzer(ast.NodeVisitor):
    """AST visitor for analyzing type safety issues in Python code."""

    def __init__(self) -> None:
        """Initialize the analyzer."""
        self.missing_annotations: list[dict[str, Any]] = []
        self.any_usage: list[dict[str, Any]] = []
        self.inconsistent_patterns: list[dict[str, Any]] = []
        self.protocol_violations: list[dict[str, Any]] = []
        self._current_class: str | None = None
        self._current_function: str | None = None

    def visit_ClassDef(self, node: ast.ClassDef) -> None:
        """Visit class definitions."""
        self._current_class = node.name
        self.generic_visit(node)
        self._current_class = None

    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        """Visit function definitions."""
        self._current_function = node.name

        # Check for missing return type annotation
        if node.returns is None and not node.name.startswith("_"):
            self.missing_annotations.append({
                "name": node.name,
                "type": "return_annotation",
                "line": node.lineno,
                "context": f"Function {node.name} in class {self._current_class}" if self._current_class else f"Function {node.name}"
            })

        # Check for missing parameter annotations
        for arg in node.args.args:
            if arg.annotation is None and arg.arg != "self":
                self.missing_annotations.append({
                    "name": arg.arg,
                    "type": "parameter_annotation",
                    "line": node.lineno,
                    "context": f"Parameter {arg.arg} in {node.name}"
                })

        self.generic_visit(node)
        self._current_function = None

    def visit_Name(self, node: ast.Name) -> None:
        """Visit name references to detect Any usage."""
        if node.id == "Any":
            self.any_usage.append({
                "line": node.lineno,
                "context": f"Any usage in {self._current_function or 'module'}"
            })

        self.generic_visit(node)
