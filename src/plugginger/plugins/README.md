# Plugginger Plugin System

This directory contains the plugin ecosystem for Plugginger with three distinct categories:

## 📁 Directory Structure

```
plugins/
├── core/           # Reusable plugins for end users
├── internal/       # Framework-internal plugins  
└── (user plugins)  # Project-specific plugins
```

## 🔧 Core Plugins (`core/`)

**Purpose**: Reusable, user-facing plugins shipped with Plugginger

**Characteristics**:
- ✅ Useful for end users in their applications
- ✅ Stable, documented APIs
- ✅ Shipped with framework distribution
- ✅ Semantic versioning
- ✅ Comprehensive documentation

**Examples**:
- `json-validator`: JSON schema validation with retry logic
- `llm-provider`: LLM abstraction (OpenAI, Anthropic, local)
- `wiring-analyzer`: App structure analysis

**Usage**:
```python
# Include in your application
builder.include_core_plugins(["json-validator", "llm-provider"])

# Use services
result = await app.call_service("json_validator.validate", data)
```

## 🔒 Internal Plugins (`internal/`)

**Purpose**: Framework-specific functionality, hidden from users

**Characteristics**:
- ⚙️ Framework use only
- ⚙️ APIs can change without user impact
- ⚙️ Powers CLI and framework tools
- ⚙️ No user documentation
- ⚙️ Private implementation details

**Examples**:
- `ai-orchestrator`: Coordinates AI workflow for CLI
- `plugin-generator`: Powers `plugginger new plugin`
- `cli-integration`: CLI-specific utilities

**Usage**:
```bash
# Used transparently by CLI
plugginger new plugin --prompt "email service"
# → Uses plugin-generator internal plugin
```

## 👤 User Plugins (root level)

**Purpose**: Project-specific plugins created by users

**Characteristics**:
- 🏠 Project-specific implementations
- 🏠 Not shipped with framework
- 🏠 User-defined APIs and functionality
- 🏠 Custom business logic

**Examples**:
```
plugins/
├── email-service/
├── payment-processor/
└── custom-auth/
```

## 🚀 Plugin Loading

### Automatic Loading
```python
# Core plugins loaded on demand
builder.include_core_plugins(["json-validator"])

# Internal plugins loaded automatically by framework
# (transparent to users)
```

### Manual Loading
```python
# User plugins loaded explicitly
builder.include_plugin_from_path("./plugins/email-service")
```

## 📋 Development Guidelines

### Core Plugin Development
1. **User-focused**: Must solve real user problems
2. **Stable APIs**: Follow semantic versioning
3. **Documentation**: Comprehensive user docs
4. **Testing**: >90% coverage required
5. **Examples**: Include usage examples

### Internal Plugin Development
1. **Framework-focused**: Solve framework needs
2. **Flexible APIs**: Can change as needed
3. **No user docs**: Internal implementation
4. **Testing**: Focus on framework integration
5. **Performance**: Optimize for CLI/framework use

### User Plugin Development
1. **Project-specific**: Solve specific business needs
2. **Custom APIs**: Define as needed
3. **Local docs**: Document for team use
4. **Testing**: As required by project
5. **Integration**: Use core plugins as dependencies
