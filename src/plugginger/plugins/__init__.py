"""
Plugginger Plugin System.

This package contains the plugin ecosystem for Plugginger:

- core/: Reusable plugins for end users (shipped with framework)
- internal/: Framework-internal plugins (hidden from users)
- (root): User-created plugins (project-specific)

Plugin Categories:
- Core plugins provide reusable services for end users
- Internal plugins provide framework functionality (CLI, etc.)
- User plugins are project-specific implementations

Example:
    # Core plugin usage (public API)
    result = await app.call_service("llm_provider.generate", prompt)

    # Internal plugin usage (framework only)
    result = await app.call_service("_internal.generate_plugin", prompt)
"""

__all__: list[str] = []
