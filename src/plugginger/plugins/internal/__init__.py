"""
Plugginger Internal Plugins.

Internal plugins provide framework-specific functionality and are not intended
for direct use by end users. These plugins power CLI commands, framework tools,
and internal operations.

Available Internal Plugins:
- ai-orchestrator: Coordinates AI workflow for CLI operations
- plugin-generator: Powers `plugginger new plugin` command
- cli-integration: CLI-specific services and utilities

Usage (Framework Internal Only):
    # Internal plugins are loaded automatically by framework
    # Users should not directly interact with these plugins

    # CLI commands use internal plugins transparently:
    # $ plugginger new plugin --prompt "email service"
    # → Uses plugin-generator internal plugin

Internal Plugin Guidelines:
- Framework use only (not user-facing)
- APIs can change without breaking user code
- No user documentation required
- Focused on framework functionality
- May use private/unstable APIs
"""

__all__: list[str] = []
