name: plugin_generator
version: 1.0.0
description: Framework-internal plugin generation and scaffolding services
author: Plugginger Framework
license: MIT
plugin_type: internal
execution_mode: thread
dependencies:
  - name: ai_orchestrator
    version: ">=1.0.0"
    optional: false
  - name: llm_provider
    version: ">=1.0.0"
    optional: false
  - name: json_validator
    version: ">=1.0.0"
    optional: false
  - name: wiring_analyzer
    version: ">=1.0.0"
    optional: false
services:
  - name: generate_plugin
    description: Generate complete plugin from specification
    timeout_seconds: 180.0
  - name: scaffold_plugin_structure
    description: Create plugin directory structure and files
    timeout_seconds: 30.0
  - name: generate_plugin_code
    description: Generate plugin implementation code
    timeout_seconds: 120.0
  - name: validate_plugin_structure
    description: Validate generated plugin structure and code
    timeout_seconds: 60.0
  - name: create_plugin_manifest
    description: Generate plugin manifest.yaml file
    timeout_seconds: 15.0
  # S4.4 - Intelligent Plugin Generation Services
  - name: process_user_prompt
    description: Process natural language prompt for AI-powered plugin generation
    timeout_seconds: 30.0
  - name: generate_plugin_spec
    description: Generate structured plugin specification from LLM output
    timeout_seconds: 60.0
  - name: generate_plugin_code_advanced
    description: Advanced plugin code generation with quality enforcement
    timeout_seconds: 180.0
  - name: analyze_integration_opportunities
    description: Analyze integration opportunities for generated plugins
    timeout_seconds: 90.0
  - name: assess_plugin_quality
    description: Assess quality of generated plugins with grading
    timeout_seconds: 60.0
config_schema:
  type: object
  properties:
    default_template:
      type: string
      enum: ["basic", "service", "event", "full"]
      default: "basic"
      description: Default plugin template to use
    output_directory:
      type: string
      default: "./plugins"
      description: Default output directory for generated plugins
    enable_ai_generation:
      type: boolean
      default: true
      description: Enable AI-powered code generation
    enable_validation:
      type: boolean
      default: true
      description: Enable validation of generated plugins
    include_tests:
      type: boolean
      default: true
      description: Generate test files for plugins
    include_documentation:
      type: boolean
      default: true
      description: Generate documentation for plugins
    code_style:
      type: string
      enum: ["pep8", "black", "custom"]
      default: "pep8"
      description: Code style to use for generated code
    max_generation_time:
      type: number
      minimum: 30.0
      maximum: 600.0
      default: 300.0
      description: Maximum time for plugin generation
  additionalProperties: false
events:
  emitted:
    - plugin.generation.started
    - plugin.generation.completed
    - plugin.generation.failed
    - plugin.scaffold.created
    - plugin.code.generated
    - plugin.validation.performed
    - plugin.manifest.created
    # S4.4 - Intelligent Plugin Generation Events
    - plugin.prompt.processed
    - plugin.spec.generated
    - plugin.code.advanced.generated
    - plugin.integration.analyzed
    - plugin.quality.assessed
  listened:
    - cli.plugin.new.requested
    - ai.plugin.generation.requested
    # S4.5 - CLI Integration Events
    - cli.intelligent.generation.requested
    - cli.wiring.validation.requested
    - cli.integration.suggestions.requested
metadata:
  tags:
    - plugin-generation
    - scaffolding
    - cli
    - internal
    - intelligent-generation
    - ai-powered
    - quality-assessment
    - wiring-analysis
  category: framework-internal
  stability: stable
  documentation_url: https://plugginger.dev/plugins/internal/plugin-generator
  repository_url: https://github.com/jkehrhahn/plugginger
  created_at: "2025-01-27T16:00:00Z"
  updated_at: "2024-12-05T21:00:00Z"
