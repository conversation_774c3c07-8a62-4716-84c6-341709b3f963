"""
Plugin Generation Service.

Provides comprehensive plugin generation and scaffolding functionality.
"""

import logging
from pathlib import Path
from typing import Any

logger = logging.getLogger(__name__)


class PluginGenerationService:
    """Service for generating complete plugins from specifications."""

    def __init__(self, app: Any) -> None:
        """Initialize generation service.

        Args:
            app: Application instance for service calls
        """
        self.app = app
        self.logger = logger

    async def generate_plugin(
        self,
        plugin_spec: dict[str, Any],
        output_directory: str = "./plugins",
        template: str = "basic",
        enable_ai_generation: bool = True,
        include_tests: bool = True,
        include_documentation: bool = True
    ) -> dict[str, Any]:
        """Generate complete plugin from specification.

        Args:
            plugin_spec: Plugin specification
            output_directory: Output directory for generated plugin
            template: Template to use for generation
            enable_ai_generation: Enable AI-powered code generation
            include_tests: Generate test files
            include_documentation: Generate documentation

        Returns:
            Plugin generation result
        """
        plugin_name = plugin_spec.get("name", "unknown_plugin")
        self.logger.info(f"Generating plugin: {plugin_name}")

        generation_id = f"plugin_gen_{plugin_name}"

        try:
            await self.app.emit_event("plugin.generation.started", {
                "generation_id": generation_id,
                "plugin_name": plugin_name,
                "template": template
            })

            # Step 1: Create plugin directory structure
            scaffold_result = await self.scaffold_plugin_structure(
                plugin_name=plugin_name,
                output_directory=output_directory,
                template=template
            )

            if not scaffold_result.get("success", False):
                return self._create_error_result("Plugin scaffolding failed", generation_id)

            plugin_path = scaffold_result["plugin_path"]

            # Step 2: Generate plugin manifest
            manifest_result = await self.create_plugin_manifest(
                plugin_spec=plugin_spec,
                plugin_path=plugin_path
            )

            if not manifest_result.get("success", False):
                return self._create_error_result("Manifest creation failed", generation_id)

            # Step 3: Generate plugin code
            if enable_ai_generation:
                code_result = await self._generate_ai_plugin_code(
                    plugin_spec=plugin_spec,
                    plugin_path=plugin_path,
                    template=template
                )
            else:
                code_result = await self._generate_template_plugin_code(
                    plugin_spec=plugin_spec,
                    plugin_path=plugin_path,
                    template=template
                )

            if not code_result.get("success", False):
                return self._create_error_result("Code generation failed", generation_id)

            # Step 4: Generate tests if requested
            if include_tests:
                test_result = await self._generate_plugin_tests(
                    plugin_spec=plugin_spec,
                    plugin_path=plugin_path
                )
            else:
                test_result = {"success": True, "tests_generated": False}

            # Step 5: Generate documentation if requested
            if include_documentation:
                docs_result = await self._generate_plugin_documentation(
                    plugin_spec=plugin_spec,
                    plugin_path=plugin_path
                )
            else:
                docs_result = {"success": True, "docs_generated": False}

            # Step 6: Validate generated plugin
            validation_result = await self.validate_plugin_structure(
                plugin_path=plugin_path
            )

            # Compile final result
            final_result = {
                "success": True,
                "generation_id": generation_id,
                "plugin_name": plugin_name,
                "plugin_path": plugin_path,
                "scaffold": scaffold_result,
                "manifest": manifest_result,
                "code": code_result,
                "tests": test_result,
                "documentation": docs_result,
                "validation": validation_result,
                "metadata": {
                    "generated_at": "2025-01-27T16:00:00Z",
                    "template": template,
                    "ai_generation": enable_ai_generation,
                    "includes_tests": include_tests,
                    "includes_documentation": include_documentation
                }
            }

            await self.app.emit_event("plugin.generation.completed", {
                "generation_id": generation_id,
                "plugin_name": plugin_name,
                "success": True,
                "plugin_path": plugin_path
            })

            self.logger.info(f"Plugin generation completed: {plugin_name}")
            return final_result

        except Exception as e:
            self.logger.error(f"Plugin generation failed: {e}")
            await self.app.emit_event("plugin.generation.failed", {
                "generation_id": generation_id,
                "plugin_name": plugin_name,
                "error": str(e)
            })
            return self._create_error_result(f"Plugin generation failed: {e}", generation_id)

    async def scaffold_plugin_structure(
        self,
        plugin_name: str,
        output_directory: str = "./plugins",
        template: str = "basic"
    ) -> dict[str, Any]:
        """Create plugin directory structure and basic files.

        Args:
            plugin_name: Name of the plugin
            output_directory: Output directory
            template: Template to use

        Returns:
            Scaffolding result
        """
        self.logger.info(f"Scaffolding plugin structure: {plugin_name}")

        try:
            # Create plugin directory
            plugin_path = Path(output_directory) / plugin_name
            plugin_path.mkdir(parents=True, exist_ok=True)

            # Create subdirectories based on template
            directories = self._get_template_directories(template)
            for directory in directories:
                (plugin_path / directory).mkdir(parents=True, exist_ok=True)

            # Create basic files
            files_created = []

            # Create __init__.py
            init_file = plugin_path / "__init__.py"
            init_content = self._generate_init_file_content(plugin_name)
            init_file.write_text(init_content, encoding="utf-8")
            files_created.append(str(init_file))

            # Create services __init__.py if services directory exists
            services_dir = plugin_path / "services"
            if services_dir.exists():
                services_init = services_dir / "__init__.py"
                services_init_content = self._generate_services_init_content()
                services_init.write_text(services_init_content, encoding="utf-8")
                files_created.append(str(services_init))

            await self.app.emit_event("plugin.scaffold.created", {
                "plugin_name": plugin_name,
                "plugin_path": str(plugin_path),
                "template": template,
                "files_created": len(files_created)
            })

            return {
                "success": True,
                "plugin_name": plugin_name,
                "plugin_path": str(plugin_path),
                "template": template,
                "directories_created": directories,
                "files_created": files_created
            }

        except Exception as e:
            self.logger.error(f"Plugin scaffolding failed: {e}")
            return {
                "success": False,
                "plugin_name": plugin_name,
                "error": str(e)
            }

    async def create_plugin_manifest(
        self,
        plugin_spec: dict[str, Any],
        plugin_path: str
    ) -> dict[str, Any]:
        """Generate plugin manifest.yaml file.

        Args:
            plugin_spec: Plugin specification
            plugin_path: Path to plugin directory

        Returns:
            Manifest creation result
        """
        plugin_name = plugin_spec.get("name", "unknown_plugin")
        self.logger.info(f"Creating manifest for plugin: {plugin_name}")

        try:
            manifest_content = self._generate_manifest_content(plugin_spec)
            manifest_path = Path(plugin_path) / "manifest.yaml"
            manifest_path.write_text(manifest_content, encoding="utf-8")

            await self.app.emit_event("plugin.manifest.created", {
                "plugin_name": plugin_name,
                "manifest_path": str(manifest_path)
            })

            return {
                "success": True,
                "plugin_name": plugin_name,
                "manifest_path": str(manifest_path),
                "manifest_content": manifest_content
            }

        except Exception as e:
            self.logger.error(f"Manifest creation failed: {e}")
            return {
                "success": False,
                "plugin_name": plugin_name,
                "error": str(e)
            }

    async def validate_plugin_structure(
        self,
        plugin_path: str
    ) -> dict[str, Any]:
        """Validate generated plugin structure and code.

        Args:
            plugin_path: Path to plugin directory

        Returns:
            Validation result
        """
        self.logger.info(f"Validating plugin structure: {plugin_path}")

        try:
            validation_errors: list[str] = []
            validation_warnings: list[str] = []

            plugin_path_obj = Path(plugin_path)

            # Check required files
            required_files = ["__init__.py", "manifest.yaml"]
            for required_file in required_files:
                file_path = plugin_path_obj / required_file
                if not file_path.exists():
                    validation_errors.append(f"Missing required file: {required_file}")

            # Check manifest validity
            manifest_path = plugin_path_obj / "manifest.yaml"
            if manifest_path.exists():
                try:
                    import yaml
                    manifest_content = manifest_path.read_text(encoding="utf-8")
                    yaml.safe_load(manifest_content)
                except Exception as e:
                    validation_errors.append(f"Invalid manifest.yaml: {e}")

            # Check Python syntax if plugin file exists
            plugin_files = list(plugin_path_obj.glob("**/*.py"))
            for py_file in plugin_files:
                try:
                    content = py_file.read_text(encoding="utf-8")
                    compile(content, str(py_file), 'exec')
                except SyntaxError as e:
                    validation_errors.append(f"Syntax error in {py_file.name}: {e}")

            await self.app.emit_event("plugin.validation.performed", {
                "plugin_path": plugin_path,
                "valid": len(validation_errors) == 0,
                "error_count": len(validation_errors),
                "warning_count": len(validation_warnings)
            })

            return {
                "success": True,
                "plugin_path": plugin_path,
                "valid": len(validation_errors) == 0,
                "errors": validation_errors,
                "warnings": validation_warnings,
                "files_checked": len(plugin_files) + 1,  # +1 for manifest
                "validation_score": self._calculate_validation_score(validation_errors, validation_warnings)
            }

        except Exception as e:
            self.logger.error(f"Plugin validation failed: {e}")
            return {
                "success": False,
                "plugin_path": plugin_path,
                "error": str(e)
            }

    def _get_template_directories(self, template: str) -> list[str]:
        """Get directories to create based on template.

        Args:
            template: Template name

        Returns:
            List of directory names
        """
        base_dirs = ["services"]

        if template == "full":
            return base_dirs + ["tests", "docs", "examples"]
        elif template == "service":
            return base_dirs + ["tests"]
        elif template == "event":
            return base_dirs + ["handlers", "tests"]
        else:  # basic
            return base_dirs

    def _generate_init_file_content(self, plugin_name: str) -> str:
        """Generate __init__.py file content.

        Args:
            plugin_name: Plugin name

        Returns:
            File content
        """
        return f'''"""
{plugin_name.replace("_", " ").title()} Plugin.

Generated plugin implementation.
"""

__all__: list[str] = []
'''

    def _generate_services_init_content(self) -> str:
        """Generate services/__init__.py file content.

        Returns:
            File content
        """
        return '''"""Services for this plugin."""

__all__: list[str] = []
'''

    def _generate_manifest_content(self, plugin_spec: dict[str, Any]) -> str:
        """Generate manifest.yaml content.

        Args:
            plugin_spec: Plugin specification

        Returns:
            Manifest content
        """
        name = plugin_spec.get("name", "unknown_plugin")
        version = plugin_spec.get("version", "1.0.0")
        description = plugin_spec.get("description", "Generated plugin")
        author = plugin_spec.get("author", "Plugin Generator")

        services = plugin_spec.get("services", [])
        dependencies = plugin_spec.get("dependencies", [])

        manifest = f"""name: {name}
version: {version}
description: {description}
author: {author}
license: MIT
plugin_type: user
execution_mode: thread
"""

        if dependencies:
            manifest += "dependencies:\n"
            for dep in dependencies:
                dep_name = dep.get("name", "unknown")
                dep_version = dep.get("version", ">=1.0.0")
                optional = dep.get("optional", False)
                manifest += f"  - name: {dep_name}\n"
                manifest += f"    version: \"{dep_version}\"\n"
                manifest += f"    optional: {str(optional).lower()}\n"

        if services:
            manifest += "services:\n"
            for service in services:
                service_name = service.get("name", "unknown_service")
                service_desc = service.get("description", "Generated service")
                timeout = service.get("timeout_seconds", 30.0)
                manifest += f"  - name: {service_name}\n"
                manifest += f"    description: {service_desc}\n"
                manifest += f"    timeout_seconds: {timeout}\n"

        manifest += """metadata:
  tags:
    - generated
    - user-plugin
  category: user
  stability: experimental
  created_at: "2025-01-27T16:00:00Z"
  updated_at: "2025-01-27T16:00:00Z"
"""

        return manifest

    async def _generate_ai_plugin_code(
        self,
        plugin_spec: dict[str, Any],
        plugin_path: str,
        template: str
    ) -> dict[str, Any]:
        """Generate plugin code using AI orchestrator.

        Args:
            plugin_spec: Plugin specification
            plugin_path: Plugin path
            template: Template

        Returns:
            Code generation result
        """
        try:
            # Use AI orchestrator for code generation
            result = await self.app.call_service("ai_orchestrator.orchestrate_plugin_generation", {
                "plugin_request": plugin_spec,
                "app_context": None,  # Could be enhanced to analyze existing app
                "max_retry_attempts": 2,
                "confidence_threshold": 0.7
            })

            if result.get("success", False):
                # Write generated code to files
                generated_code = result.get("generated_code", {})
                code_sections = generated_code.get("code_sections", {})

                files_written = []

                # Write main plugin file
                if "services" in code_sections:
                    plugin_file_path = Path(plugin_path) / f"{plugin_spec.get('name', 'plugin')}_plugin.py"
                    plugin_code = self._generate_plugin_class_code(plugin_spec, code_sections)
                    plugin_file_path.write_text(plugin_code, encoding="utf-8")
                    files_written.append(str(plugin_file_path))

                await self.app.emit_event("plugin.code.generated", {
                    "plugin_name": plugin_spec.get("name", "unknown"),
                    "plugin_path": plugin_path,
                    "ai_generated": True,
                    "files_written": len(files_written)
                })

                return {
                    "success": True,
                    "ai_generated": True,
                    "files_written": files_written,
                    "generation_result": result
                }
            else:
                return {"success": False, "error": "AI generation failed"}

        except Exception as e:
            self.logger.error(f"AI code generation failed: {e}")
            return {"success": False, "error": str(e)}

    async def _generate_template_plugin_code(
        self,
        plugin_spec: dict[str, Any],
        plugin_path: str,
        template: str
    ) -> dict[str, Any]:
        """Generate plugin code using templates.

        Args:
            plugin_spec: Plugin specification
            plugin_path: Plugin path
            template: Template

        Returns:
            Code generation result
        """
        try:
            plugin_name = plugin_spec.get("name", "unknown_plugin")
            plugin_code = self._generate_basic_plugin_code(plugin_spec)

            plugin_file_path = Path(plugin_path) / f"{plugin_name}_plugin.py"
            plugin_file_path.write_text(plugin_code, encoding="utf-8")

            await self.app.emit_event("plugin.code.generated", {
                "plugin_name": plugin_name,
                "plugin_path": plugin_path,
                "ai_generated": False,
                "files_written": 1
            })

            return {
                "success": True,
                "ai_generated": False,
                "files_written": [str(plugin_file_path)],
                "template_used": template
            }

        except Exception as e:
            self.logger.error(f"Template code generation failed: {e}")
            return {"success": False, "error": str(e)}

    def _generate_basic_plugin_code(self, plugin_spec: dict[str, Any]) -> str:
        """Generate basic plugin code.

        Args:
            plugin_spec: Plugin specification

        Returns:
            Plugin code
        """
        plugin_name = plugin_spec.get("name", "unknown_plugin")
        class_name = "".join(word.capitalize() for word in plugin_name.split("_")) + "Plugin"
        description = plugin_spec.get("description", "Generated plugin")

        return f'''"""
{class_name}.

{description}
"""

import logging
from typing import Any

from plugginger.api.plugin import PluginBase
from plugginger.api.service import service

logger = logging.getLogger(__name__)


class {class_name}(PluginBase):
    """{description}"""

    def __init__(self, **injected_dependencies: Any) -> None:
        """Initialize {plugin_name} plugin.

        Args:
            **injected_dependencies: Injected dependencies from DI container
        """
        super().__init__(**injected_dependencies)
        self.logger = logger

    async def setup(self, plugin_config: Any = None) -> None:
        """Setup the {plugin_name} plugin."""
        self.logger.info("{class_name} plugin initializing")

    async def teardown(self) -> None:
        """Cleanup the {plugin_name} plugin."""
        self.logger.info("{class_name} plugin shutting down")

    @service(name="hello")
    async def hello(self, name: str = "World") -> dict[str, Any]:
        """Say hello.

        Args:
            name: Name to greet

        Returns:
            Greeting message
        """
        message = f"Hello, {{name}}!"
        self.logger.info(f"Generated greeting: {{message}}")

        return {{
            "message": message,
            "plugin": "{plugin_name}",
            "timestamp": "2025-01-27T16:00:00Z"
        }}
'''

    def _generate_plugin_class_code(
        self,
        plugin_spec: dict[str, Any],
        code_sections: dict[str, Any]
    ) -> str:
        """Generate plugin class code from AI-generated sections.

        Args:
            plugin_spec: Plugin specification
            code_sections: Generated code sections

        Returns:
            Complete plugin code
        """
        # This would integrate AI-generated code sections
        # For now, fall back to basic template
        return self._generate_basic_plugin_code(plugin_spec)

    async def _generate_plugin_tests(
        self,
        plugin_spec: dict[str, Any],
        plugin_path: str
    ) -> dict[str, Any]:
        """Generate test files for plugin.

        Args:
            plugin_spec: Plugin specification
            plugin_path: Plugin path

        Returns:
            Test generation result
        """
        # Placeholder for test generation
        return {"success": True, "tests_generated": False, "message": "Test generation not implemented"}

    async def _generate_plugin_documentation(
        self,
        plugin_spec: dict[str, Any],
        plugin_path: str
    ) -> dict[str, Any]:
        """Generate documentation for plugin.

        Args:
            plugin_spec: Plugin specification
            plugin_path: Plugin path

        Returns:
            Documentation generation result
        """
        # Placeholder for documentation generation
        return {"success": True, "docs_generated": False, "message": "Documentation generation not implemented"}

    def _calculate_validation_score(self, errors: list[str], warnings: list[str]) -> float:
        """Calculate validation score.

        Args:
            errors: Validation errors
            warnings: Validation warnings

        Returns:
            Validation score (0.0 to 1.0)
        """
        score = 1.0
        score -= len(errors) * 0.2
        score -= len(warnings) * 0.05
        return max(0.0, score)

    def _create_error_result(self, error_message: str, generation_id: str) -> dict[str, Any]:
        """Create standardized error result.

        Args:
            error_message: Error message
            generation_id: Generation ID

        Returns:
            Error result
        """
        return {
            "success": False,
            "generation_id": generation_id,
            "error": error_message,
            "metadata": {
                "failed_at": "2025-01-27T16:00:00Z"
            }
        }
