"""Services for Plugin Generator Internal Plugin."""

from .code_template_engine import CodeTemplateEngine
from .generation_service import PluginGenerationService
from .plugin_spec_generator import PluginSpecGenerator

# S4.4 - Intelligent Plugin Generation Services
from .prompt_processor import PromptProcessor
from .quality_scoring import QualityScoring
from .wiring_integration import WiringIntegration

__all__ = [
    "PluginGenerationService",
    # S4.4 Services
    "PromptProcessor",
    "PluginSpecGenerator",
    "CodeTemplateEngine",
    "WiringIntegration",
    "QualityScoring"
]
