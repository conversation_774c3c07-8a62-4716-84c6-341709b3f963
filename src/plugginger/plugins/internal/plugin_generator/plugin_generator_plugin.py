"""
Plugin Generator Internal Plugin.

Framework-internal plugin generation and scaffolding services.
"""

import logging
from typing import Any

from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service
from plugginger.core.exceptions import PluggingerValidationError
from plugginger.plugins.internal.plugin_generator.services.generation_service import (
    PluginGenerationService,
)

logger = logging.getLogger(__name__)


@plugin(name="plugin_generator", version="1.0.0")
class PluginGeneratorPlugin(PluginBase):
    """Internal plugin for plugin generation and scaffolding services."""

    def __init__(self, **injected_dependencies: Any) -> None:
        """Initialize plugin generator plugin.

        Args:
            **injected_dependencies: Injected dependencies from DI container
        """
        super().__init__(**injected_dependencies)
        self.logger = logger

        # Initialize services
        self.generation_service = PluginGenerationService(self.app)

        # Configuration from manifest
        self.default_template = "basic"
        self.output_directory = "./plugins"
        self.enable_ai_generation = True
        self.enable_validation = True
        self.include_tests = True
        self.include_documentation = True
        self.code_style = "pep8"
        self.max_generation_time = 300.0

    async def setup(self, plugin_config: Any = None) -> None:
        """Setup the plugin generator plugin."""
        self.logger.info("Plugin Generator plugin initializing")

        # Load configuration from plugin_config if provided
        if plugin_config:
            if hasattr(plugin_config, 'default_template'):
                self.default_template = plugin_config.default_template
            if hasattr(plugin_config, 'output_directory'):
                self.output_directory = plugin_config.output_directory
            if hasattr(plugin_config, 'enable_ai_generation'):
                self.enable_ai_generation = plugin_config.enable_ai_generation
            if hasattr(plugin_config, 'enable_validation'):
                self.enable_validation = plugin_config.enable_validation
            if hasattr(plugin_config, 'include_tests'):
                self.include_tests = plugin_config.include_tests
            if hasattr(plugin_config, 'include_documentation'):
                self.include_documentation = plugin_config.include_documentation
            if hasattr(plugin_config, 'code_style'):
                self.code_style = plugin_config.code_style
            if hasattr(plugin_config, 'max_generation_time'):
                self.max_generation_time = plugin_config.max_generation_time

        self.logger.info(f"Plugin Generator configured: template={self.default_template}, "
                        f"ai_generation={self.enable_ai_generation}")

    async def teardown(self) -> None:
        """Cleanup the plugin generator plugin."""
        self.logger.info("Plugin Generator plugin shutting down")

    @service(name="generate_plugin")
    async def generate_plugin(
        self,
        plugin_spec: dict[str, Any],
        output_directory: str | None = None,
        template: str | None = None,
        enable_ai_generation: bool | None = None,
        include_tests: bool | None = None,
        include_documentation: bool | None = None
    ) -> dict[str, Any]:
        """Generate complete plugin from specification.

        Args:
            plugin_spec: Plugin specification
            output_directory: Output directory for generated plugin
            template: Template to use for generation
            enable_ai_generation: Enable AI-powered code generation
            include_tests: Generate test files
            include_documentation: Generate documentation

        Returns:
            Plugin generation result

        Example:
            result = await app.call_service("plugin_generator.generate_plugin", {
                "plugin_spec": {
                    "name": "my_plugin",
                    "version": "1.0.0",
                    "description": "My custom plugin",
                    "services": [{"name": "my_service", "description": "My service"}]
                },
                "template": "service"
            })
        """
        output_directory = output_directory or self.output_directory
        template = template or self.default_template
        enable_ai_generation = enable_ai_generation if enable_ai_generation is not None else self.enable_ai_generation
        include_tests = include_tests if include_tests is not None else self.include_tests
        include_documentation = include_documentation if include_documentation is not None else self.include_documentation

        plugin_name = plugin_spec.get("name", "unknown_plugin")
        self.logger.info(f"Generating plugin: {plugin_name}")

        try:
            result = await self.generation_service.generate_plugin(
                plugin_spec=plugin_spec,
                output_directory=output_directory,
                template=template,
                enable_ai_generation=enable_ai_generation,
                include_tests=include_tests,
                include_documentation=include_documentation
            )

            self.logger.info(f"Plugin generation {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Plugin generation error: {e}")
            raise PluggingerValidationError(f"Plugin generation failed: {e}") from e

    @service(name="scaffold_plugin_structure")
    async def scaffold_plugin_structure(
        self,
        plugin_name: str,
        output_directory: str | None = None,
        template: str | None = None
    ) -> dict[str, Any]:
        """Create plugin directory structure and files.

        Args:
            plugin_name: Name of the plugin
            output_directory: Output directory
            template: Template to use

        Returns:
            Scaffolding result

        Example:
            result = await app.call_service("plugin_generator.scaffold_plugin_structure", {
                "plugin_name": "my_plugin",
                "template": "full"
            })
        """
        output_directory = output_directory or self.output_directory
        template = template or self.default_template

        self.logger.info(f"Scaffolding plugin structure: {plugin_name}")

        try:
            result = await self.generation_service.scaffold_plugin_structure(
                plugin_name=plugin_name,
                output_directory=output_directory,
                template=template
            )

            self.logger.info(f"Plugin scaffolding {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Plugin scaffolding error: {e}")
            raise PluggingerValidationError(f"Plugin scaffolding failed: {e}") from e

    @service(name="generate_plugin_code")
    async def generate_plugin_code(
        self,
        plugin_spec: dict[str, Any],
        plugin_path: str,
        enable_ai_generation: bool | None = None
    ) -> dict[str, Any]:
        """Generate plugin implementation code.

        Args:
            plugin_spec: Plugin specification
            plugin_path: Path to plugin directory
            enable_ai_generation: Enable AI-powered code generation

        Returns:
            Code generation result

        Example:
            result = await app.call_service("plugin_generator.generate_plugin_code", {
                "plugin_spec": {"name": "my_plugin", "services": [...]},
                "plugin_path": "./plugins/my_plugin"
            })
        """
        enable_ai_generation = enable_ai_generation if enable_ai_generation is not None else self.enable_ai_generation

        plugin_name = plugin_spec.get("name", "unknown_plugin")
        self.logger.info(f"Generating code for plugin: {plugin_name}")

        try:
            if enable_ai_generation:
                result = await self.generation_service._generate_ai_plugin_code(
                    plugin_spec=plugin_spec,
                    plugin_path=plugin_path,
                    template=self.default_template
                )
            else:
                result = await self.generation_service._generate_template_plugin_code(
                    plugin_spec=plugin_spec,
                    plugin_path=plugin_path,
                    template=self.default_template
                )

            self.logger.info(f"Plugin code generation {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Plugin code generation error: {e}")
            raise PluggingerValidationError(f"Plugin code generation failed: {e}") from e

    @service(name="validate_plugin_structure")
    async def validate_plugin_structure(
        self,
        plugin_path: str
    ) -> dict[str, Any]:
        """Validate generated plugin structure and code.

        Args:
            plugin_path: Path to plugin directory

        Returns:
            Validation result

        Example:
            result = await app.call_service("plugin_generator.validate_plugin_structure", {
                "plugin_path": "./plugins/my_plugin"
            })
        """
        self.logger.info(f"Validating plugin structure: {plugin_path}")

        try:
            result = await self.generation_service.validate_plugin_structure(
                plugin_path=plugin_path
            )

            self.logger.info(f"Plugin validation {'passed' if result.get('valid') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Plugin validation error: {e}")
            raise PluggingerValidationError(f"Plugin validation failed: {e}") from e

    @service(name="create_plugin_manifest")
    async def create_plugin_manifest(
        self,
        plugin_spec: dict[str, Any],
        plugin_path: str
    ) -> dict[str, Any]:
        """Generate plugin manifest.yaml file.

        Args:
            plugin_spec: Plugin specification
            plugin_path: Path to plugin directory

        Returns:
            Manifest creation result

        Example:
            result = await app.call_service("plugin_generator.create_plugin_manifest", {
                "plugin_spec": {"name": "my_plugin", "version": "1.0.0", ...},
                "plugin_path": "./plugins/my_plugin"
            })
        """
        plugin_name = plugin_spec.get("name", "unknown_plugin")
        self.logger.info(f"Creating manifest for plugin: {plugin_name}")

        try:
            result = await self.generation_service.create_plugin_manifest(
                plugin_spec=plugin_spec,
                plugin_path=plugin_path
            )

            self.logger.info(f"Plugin manifest creation {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Plugin manifest creation error: {e}")
            raise PluggingerValidationError(f"Plugin manifest creation failed: {e}") from e

    async def get_available_templates(self) -> dict[str, Any]:
        """Get list of available plugin templates.

        Returns:
            Available templates information
        """
        templates = {
            "basic": {
                "name": "basic",
                "description": "Basic plugin with minimal structure",
                "directories": ["services"],
                "includes_tests": False,
                "includes_docs": False
            },
            "service": {
                "name": "service",
                "description": "Service-oriented plugin with test structure",
                "directories": ["services", "tests"],
                "includes_tests": True,
                "includes_docs": False
            },
            "event": {
                "name": "event",
                "description": "Event-driven plugin with handlers",
                "directories": ["services", "handlers", "tests"],
                "includes_tests": True,
                "includes_docs": False
            },
            "full": {
                "name": "full",
                "description": "Complete plugin with all features",
                "directories": ["services", "tests", "docs", "examples"],
                "includes_tests": True,
                "includes_docs": True
            }
        }

        return {
            "templates": templates,
            "default_template": self.default_template,
            "total_templates": len(templates)
        }

    # S4.4 - Intelligent Plugin Generation Services

    @service(name="process_user_prompt")
    async def process_user_prompt(self, data: dict[str, Any]) -> dict[str, Any]:
        """Process natural language prompt for AI-powered plugin generation.

        Args:
            data: Request data containing:
                - user_prompt: Natural language prompt
                - app_context: Optional application context
                - generation_options: Optional generation options

        Returns:
            Processed prompt result with analysis and enhanced context
        """
        try:
            user_prompt = data.get("user_prompt", "")
            app_context = data.get("app_context", {})
            generation_options = data.get("generation_options", {})

            if not user_prompt:
                return {"success": False, "error": "User prompt is required"}

            # Use PromptProcessor service
            from plugginger.plugins.internal.plugin_generator.services.prompt_processor import (
                PromptProcessor,
            )
            processor = PromptProcessor(self.app)

            result = await processor.process_user_prompt(
                user_prompt=user_prompt,
                app_context=app_context,
                generation_options=generation_options
            )

            await self.app.emit_event("plugin.prompt.processed", {
                "prompt": user_prompt,
                "success": result.get("success", False),
                "plugin_type": result.get("prompt_analysis", {}).get("plugin_type", "unknown")
            })

            return dict(result)

        except Exception as e:
            self.logger.error(f"Prompt processing failed: {e}")
            return {"success": False, "error": str(e)}

    @service(name="generate_plugin_spec")
    async def generate_plugin_spec(self, data: dict[str, Any]) -> dict[str, Any]:
        """Generate structured plugin specification from LLM output.

        Args:
            data: Request data containing:
                - llm_output: LLM generated output
                - prompt_context: Context from prompt processing
                - validation_options: Optional validation options

        Returns:
            Generated plugin specification
        """
        try:
            llm_output = data.get("llm_output", "")
            prompt_context = data.get("prompt_context", {})
            validation_options = data.get("validation_options", {})

            if not llm_output:
                return {"success": False, "error": "LLM output is required"}

            # Use PluginSpecGenerator service
            from plugginger.plugins.internal.plugin_generator.services.plugin_spec_generator import (
                PluginSpecGenerator,
            )
            generator = PluginSpecGenerator(self.app)

            result = await generator.generate_plugin_spec(
                llm_output=llm_output,
                prompt_context=prompt_context,
                validation_options=validation_options
            )

            await self.app.emit_event("plugin.spec.generated", {
                "success": result.get("success", False),
                "plugin_name": result.get("plugin_spec", {}).get("name", "unknown")
            })

            return dict(result)

        except Exception as e:
            self.logger.error(f"Plugin spec generation failed: {e}")
            return {"success": False, "error": str(e)}

    @service(name="generate_plugin_code_advanced")
    async def generate_plugin_code_advanced(self, data: dict[str, Any]) -> dict[str, Any]:
        """Advanced plugin code generation with quality enforcement.

        Args:
            data: Request data containing:
                - plugin_spec: Plugin specification
                - generation_options: Code generation options

        Returns:
            Generated plugin code with tests and documentation
        """
        try:
            plugin_spec = data.get("plugin_spec", {})
            generation_options = data.get("generation_options", {})

            if not plugin_spec:
                return {"success": False, "error": "Plugin specification is required"}

            # Use CodeTemplateEngine service
            from plugginger.plugins.internal.plugin_generator.services.code_template_engine import (
                CodeTemplateEngine,
            )
            engine = CodeTemplateEngine(self.app)

            result = await engine.generate_plugin_code(
                plugin_spec=plugin_spec,
                generation_options=generation_options
            )

            await self.app.emit_event("plugin.code.advanced.generated", {
                "success": result.get("success", False),
                "plugin_name": plugin_spec.get("name", "unknown"),
                "files_generated": len(result.get("additional_files", {})) + 3  # plugin, test, manifest
            })

            return dict(result)

        except Exception as e:
            self.logger.error(f"Advanced code generation failed: {e}")
            return {"success": False, "error": str(e)}

    @service(name="analyze_integration_opportunities")
    async def analyze_integration_opportunities(self, data: dict[str, Any]) -> dict[str, Any]:
        """Analyze integration opportunities for generated plugins.

        Args:
            data: Request data containing:
                - plugin_spec: Plugin specification
                - app_context: Application context
                - analysis_options: Analysis options

        Returns:
            Integration analysis with suggestions
        """
        try:
            plugin_spec = data.get("plugin_spec", {})
            app_context = data.get("app_context", {})
            analysis_options = data.get("analysis_options", {})

            if not plugin_spec:
                return {"success": False, "error": "Plugin specification is required"}

            # Use WiringIntegration service
            from plugginger.plugins.internal.plugin_generator.services.wiring_integration import (
                WiringIntegration,
            )
            wiring = WiringIntegration(self.app)

            result = await wiring.analyze_integration_opportunities(
                plugin_spec=plugin_spec,
                app_context=app_context,
                analysis_options=analysis_options
            )

            await self.app.emit_event("plugin.integration.analyzed", {
                "success": result.get("success", False),
                "plugin_name": plugin_spec.get("name", "unknown"),
                "suggestions_count": len(result.get("integration_suggestions", []))
            })

            return dict(result)

        except Exception as e:
            self.logger.error(f"Integration analysis failed: {e}")
            return {"success": False, "error": str(e)}

    @service(name="assess_plugin_quality")
    async def assess_plugin_quality(self, data: dict[str, Any]) -> dict[str, Any]:
        """Assess quality of generated plugins with grading.

        Args:
            data: Request data containing:
                - plugin_spec: Plugin specification
                - generated_code: Generated plugin code
                - test_code: Generated test code
                - manifest_code: Generated manifest code
                - assessment_options: Assessment options

        Returns:
            Quality assessment with grade and recommendations
        """
        try:
            plugin_spec = data.get("plugin_spec", {})
            generated_code = data.get("generated_code", "")
            test_code = data.get("test_code", "")
            manifest_code = data.get("manifest_code", "")
            assessment_options = data.get("assessment_options", {})

            if not plugin_spec or not generated_code:
                return {"success": False, "error": "Plugin specification and generated code are required"}

            # Use QualityScoring service
            from plugginger.plugins.internal.plugin_generator.services.quality_scoring import (
                QualityScoring,
            )
            scorer = QualityScoring(self.app)

            result = await scorer.assess_plugin_quality(
                plugin_spec=plugin_spec,
                generated_code=generated_code,
                test_code=test_code,
                manifest_code=manifest_code,
                assessment_options=assessment_options
            )

            await self.app.emit_event("plugin.quality.assessed", {
                "success": result.get("success", False),
                "plugin_name": plugin_spec.get("name", "unknown"),
                "quality_score": result.get("overall_score", 0.0),
                "quality_grade": result.get("quality_grade", "F")
            })

            return dict(result)

        except Exception as e:
            self.logger.error(f"Quality assessment failed: {e}")
            return {"success": False, "error": str(e)}

    async def get_generation_status(self) -> dict[str, Any]:
        """Get current generation status and configuration.

        Returns:
            Generation status information
        """
        return {
            "plugin_name": "plugin_generator",
            "status": "active",
            "configuration": {
                "default_template": self.default_template,
                "output_directory": self.output_directory,
                "enable_ai_generation": self.enable_ai_generation,
                "enable_validation": self.enable_validation,
                "include_tests": self.include_tests,
                "include_documentation": self.include_documentation,
                "code_style": self.code_style,
                "max_generation_time": self.max_generation_time
            },
            "capabilities": [
                "plugin_generation",
                "structure_scaffolding",
                "code_generation",
                "manifest_creation",
                "validation",
                "ai_powered_generation",
                "template_based_generation"
            ],
            "dependencies": [
                "ai_orchestrator",
                "llm_provider",
                "json_validator",
                "wiring_analyzer"
            ]
        }
