"""
AI Output Validation Service.

Provides comprehensive validation of AI-generated content using multiple validators.
"""

import logging
from typing import Any

logger = logging.getLogger(__name__)


class AIValidationService:
    """Service for validating AI-generated content using multiple validators."""

    def __init__(self, app: Any) -> None:
        """Initialize validation service.

        Args:
            app: Application instance for service calls
        """
        self.app = app
        self.logger = logger

    async def validate_ai_output(
        self,
        content: str,
        content_type: str,
        validation_rules: dict[str, Any] | None = None,
        confidence_threshold: float = 0.7
    ) -> dict[str, Any]:
        """Validate AI-generated content using multiple validators.

        Args:
            content: AI-generated content to validate
            content_type: Type of content (json, yaml, python, text)
            validation_rules: Optional validation rules
            confidence_threshold: Confidence threshold for validation

        Returns:
            Comprehensive validation result
        """
        self.logger.info(f"Validating AI output: {content_type}")

        validation_id = f"ai_validation_{content_type}_{hash(content) % 10000}"

        try:
            await self.app.emit_event("ai.validation.performed", {
                "validation_id": validation_id,
                "content_type": content_type,
                "content_length": len(content)
            })

            # Perform multiple validation checks
            validation_results = []

            # 1. Format validation
            format_result = await self._validate_format(content, content_type, validation_id)
            validation_results.append(format_result)

            # 2. Schema validation (if applicable)
            if validation_rules and "schema" in validation_rules:
                schema_result = await self._validate_schema(content, validation_rules["schema"], validation_id)
                validation_results.append(schema_result)

            # 3. Content quality validation
            quality_result = await self._validate_content_quality(content, content_type, validation_id)
            validation_results.append(quality_result)

            # 4. Security validation
            security_result = await self._validate_security(content, content_type, validation_id)
            validation_results.append(security_result)

            # Compile overall validation result
            overall_result = self._compile_validation_result(
                validation_results, confidence_threshold, validation_id
            )

            self.logger.info(f"AI output validation completed: {validation_id}")
            return overall_result

        except Exception as e:
            self.logger.error(f"AI output validation failed: {e}")
            return {
                "valid": False,
                "validation_id": validation_id,
                "error": str(e),
                "content_type": content_type,
                "validation_results": []
            }

    async def validate_plugin_specification(
        self,
        plugin_spec: dict[str, Any],
        strict_mode: bool = True
    ) -> dict[str, Any]:
        """Validate AI-generated plugin specification.

        Args:
            plugin_spec: Plugin specification to validate
            strict_mode: Enable strict validation mode

        Returns:
            Plugin specification validation result
        """
        self.logger.info(f"Validating plugin specification: {plugin_spec.get('name', 'unknown')}")

        validation_errors = []
        validation_warnings = []

        # Required fields validation
        required_fields = ["name", "version", "description", "services"]
        for field in required_fields:
            if field not in plugin_spec:
                validation_errors.append(f"Missing required field: {field}")

        # Name validation
        if "name" in plugin_spec:
            name = plugin_spec["name"]
            if not isinstance(name, str) or not name.strip():
                validation_errors.append("Plugin name must be a non-empty string")
            elif not name.replace("_", "").replace("-", "").isalnum():
                validation_warnings.append("Plugin name should only contain alphanumeric characters, hyphens, and underscores")

        # Version validation
        if "version" in plugin_spec:
            version = plugin_spec["version"]
            if not isinstance(version, str) or not self._is_valid_version(version):
                validation_errors.append("Plugin version must be a valid semantic version (e.g., '1.0.0')")

        # Services validation
        if "services" in plugin_spec:
            services = plugin_spec["services"]
            if not isinstance(services, list):
                validation_errors.append("Services must be a list")
            else:
                for i, service in enumerate(services):
                    service_errors = self._validate_service_spec(service, i)
                    validation_errors.extend(service_errors)

        # Dependencies validation
        if "dependencies" in plugin_spec:
            dependencies = plugin_spec["dependencies"]
            if not isinstance(dependencies, list):
                validation_errors.append("Dependencies must be a list")
            else:
                for i, dep in enumerate(dependencies):
                    dep_errors = self._validate_dependency_spec(dep, i)
                    validation_errors.extend(dep_errors)

        return {
            "valid": len(validation_errors) == 0,
            "plugin_name": plugin_spec.get("name", "unknown"),
            "errors": validation_errors,
            "warnings": validation_warnings,
            "validation_score": self._calculate_validation_score(validation_errors, validation_warnings),
            "strict_mode": strict_mode
        }

    async def validate_generated_code(
        self,
        code: str,
        language: str = "python",
        check_syntax: bool = True,
        check_style: bool = True
    ) -> dict[str, Any]:
        """Validate AI-generated code.

        Args:
            code: Generated code to validate
            language: Programming language
            check_syntax: Enable syntax checking
            check_style: Enable style checking

        Returns:
            Code validation result
        """
        self.logger.info(f"Validating generated {language} code")

        validation_errors = []
        validation_warnings = []

        if language.lower() == "python" and check_syntax:
            syntax_errors = self._validate_python_syntax(code)
            validation_errors.extend(syntax_errors)

        if check_style:
            style_warnings = self._validate_code_style(code, language)
            validation_warnings.extend(style_warnings)

        # Security checks
        security_issues = self._validate_code_security(code, language)
        validation_errors.extend(security_issues)

        return {
            "valid": len(validation_errors) == 0,
            "language": language,
            "errors": validation_errors,
            "warnings": validation_warnings,
            "code_length": len(code),
            "line_count": len(code.split('\n')),
            "validation_score": self._calculate_validation_score(validation_errors, validation_warnings)
        }

    async def _validate_format(
        self,
        content: str,
        content_type: str,
        validation_id: str
    ) -> dict[str, Any]:
        """Validate content format."""
        try:
            if content_type.lower() == "json":
                result = await self.app.call_service("json_validator.validate_json", {
                    "data": content,
                    "schema": {"type": "object"}  # Basic JSON validation
                })
                return {
                    "validator": "format",
                    "valid": result.get("valid", False),
                    "errors": result.get("errors", []),
                    "content_type": content_type
                }
            else:
                # For other formats, perform basic checks
                return {
                    "validator": "format",
                    "valid": len(content.strip()) > 0,
                    "errors": [] if content.strip() else ["Content is empty"],
                    "content_type": content_type
                }

        except Exception as e:
            return {
                "validator": "format",
                "valid": False,
                "errors": [f"Format validation failed: {e}"],
                "content_type": content_type
            }

    async def _validate_schema(
        self,
        content: str,
        schema: dict[str, Any],
        validation_id: str
    ) -> dict[str, Any]:
        """Validate content against schema."""
        try:
            result = await self.app.call_service("json_validator.validate_json", {
                "data": content,
                "schema": schema
            })
            return {
                "validator": "schema",
                "valid": result.get("valid", False),
                "errors": result.get("errors", []),
                "schema": schema
            }

        except Exception as e:
            return {
                "validator": "schema",
                "valid": False,
                "errors": [f"Schema validation failed: {e}"],
                "schema": schema
            }

    async def _validate_content_quality(
        self,
        content: str,
        content_type: str,
        validation_id: str
    ) -> dict[str, Any]:
        """Validate content quality."""
        errors = []
        warnings = []

        # Basic quality checks
        if len(content.strip()) < 10:
            warnings.append("Content is very short")

        if content_type.lower() == "json":
            # Check for common JSON quality issues
            if content.count('{') != content.count('}'):
                errors.append("Mismatched braces in JSON")
            if '"null"' in content:
                warnings.append("String 'null' found - might be intended as null value")

        return {
            "validator": "quality",
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "content_type": content_type
        }

    async def _validate_security(
        self,
        content: str,
        content_type: str,
        validation_id: str
    ) -> dict[str, Any]:
        """Validate content for security issues."""
        security_issues = []

        # Basic security checks
        dangerous_patterns = [
            "eval(", "exec(", "__import__", "subprocess", "os.system",
            "shell=True", "input(", "raw_input("
        ]

        for pattern in dangerous_patterns:
            if pattern in content:
                security_issues.append(f"Potentially dangerous pattern found: {pattern}")

        return {
            "validator": "security",
            "valid": len(security_issues) == 0,
            "errors": security_issues,
            "content_type": content_type
        }

    def _compile_validation_result(
        self,
        validation_results: list[dict[str, Any]],
        confidence_threshold: float,
        validation_id: str
    ) -> dict[str, Any]:
        """Compile overall validation result."""
        all_errors = []
        all_warnings = []
        validator_results = {}

        for result in validation_results:
            validator = result.get("validator", "unknown")
            validator_results[validator] = result
            all_errors.extend(result.get("errors", []))
            all_warnings.extend(result.get("warnings", []))

        # Calculate overall validity
        overall_valid = all(result.get("valid", False) for result in validation_results)

        # Calculate confidence score
        confidence_score = self._calculate_confidence_score(validation_results)

        return {
            "valid": overall_valid,
            "validation_id": validation_id,
            "confidence_score": confidence_score,
            "meets_threshold": confidence_score >= confidence_threshold,
            "errors": all_errors,
            "warnings": all_warnings,
            "validator_results": validator_results,
            "validation_summary": {
                "total_validators": len(validation_results),
                "passed_validators": sum(1 for r in validation_results if r.get("valid", False)),
                "error_count": len(all_errors),
                "warning_count": len(all_warnings)
            }
        }

    def _validate_service_spec(self, service: dict[str, Any], index: int) -> list[str]:
        """Validate service specification."""
        errors = []

        if not isinstance(service, dict):
            errors.append(f"Service {index} must be an object")
            return errors

        if "name" not in service:
            errors.append(f"Service {index} missing required field: name")
        elif not isinstance(service["name"], str) or not service["name"].strip():
            errors.append(f"Service {index} name must be a non-empty string")

        return errors

    def _validate_dependency_spec(self, dependency: dict[str, Any], index: int) -> list[str]:
        """Validate dependency specification."""
        errors = []

        if not isinstance(dependency, dict):
            errors.append(f"Dependency {index} must be an object")
            return errors

        if "name" not in dependency:
            errors.append(f"Dependency {index} missing required field: name")

        return errors

    def _is_valid_version(self, version: str) -> bool:
        """Check if version string is valid semantic version."""
        import re
        pattern = r'^\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?(\+[a-zA-Z0-9.-]+)?$'
        return bool(re.match(pattern, version))

    def _validate_python_syntax(self, code: str) -> list[str]:
        """Validate Python syntax."""
        errors = []
        try:
            compile(code, '<string>', 'exec')
        except SyntaxError as e:
            errors.append(f"Syntax error: {e}")
        except Exception as e:
            errors.append(f"Compilation error: {e}")

        return errors

    def _validate_code_style(self, code: str, language: str) -> list[str]:
        """Validate code style."""
        warnings = []

        if language.lower() == "python":
            # Basic Python style checks
            lines = code.split('\n')
            for i, line in enumerate(lines, 1):
                if len(line) > 120:
                    warnings.append(f"Line {i} exceeds 120 characters")
                if line.endswith(' '):
                    warnings.append(f"Line {i} has trailing whitespace")

        return warnings

    def _validate_code_security(self, code: str, language: str) -> list[str]:
        """Validate code for security issues."""
        errors = []

        if language.lower() == "python":
            dangerous_imports = ["os", "subprocess", "sys", "importlib"]
            for imp in dangerous_imports:
                if f"import {imp}" in code or f"from {imp}" in code:
                    errors.append(f"Potentially dangerous import: {imp}")

        return errors

    def _calculate_validation_score(self, errors: list[str], warnings: list[str]) -> float:
        """Calculate validation score."""
        # Start with perfect score
        score = 1.0

        # Deduct for errors (major issues)
        score -= len(errors) * 0.2

        # Deduct for warnings (minor issues)
        score -= len(warnings) * 0.05

        return max(0.0, score)

    def _calculate_confidence_score(self, validation_results: list[dict[str, Any]]) -> float:
        """Calculate confidence score from validation results."""
        if not validation_results:
            return 0.0

        # Weight different validators
        validator_weights = {
            "format": 0.3,
            "schema": 0.3,
            "quality": 0.2,
            "security": 0.2
        }

        total_score = 0.0
        total_weight = 0.0

        for result in validation_results:
            validator = result.get("validator", "unknown")
            weight = validator_weights.get(validator, 0.1)

            if result.get("valid", False):
                validator_score = 1.0
            else:
                # Partial score based on error count
                error_count = len(result.get("errors", []))
                validator_score = max(0.0, 1.0 - (error_count * 0.2))

            total_score += validator_score * weight
            total_weight += weight

        return total_score / total_weight if total_weight > 0 else 0.0
