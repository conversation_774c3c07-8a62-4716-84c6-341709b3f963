name: ai_orchestrator
version: 1.0.0
description: Framework-internal AI coordination and orchestration services
author: Plugginger Framework
license: MIT
plugin_type: internal
execution_mode: thread
dependencies:
  - name: llm_provider
    version: ">=1.0.0"
    optional: false
  - name: json_validator
    version: ">=1.0.0"
    optional: false
  - name: wiring_analyzer
    version: ">=1.0.0"
    optional: false
services:
  - name: orchestrate_plugin_generation
    description: Coordinate AI-powered plugin generation workflow
    timeout_seconds: 120.0
  - name: analyze_and_suggest
    description: Analyze app context and suggest plugin improvements
    timeout_seconds: 60.0
  - name: validate_ai_output
    description: Validate AI-generated content using multiple validators
    timeout_seconds: 30.0
  - name: coordinate_services
    description: Coordinate multiple AI services for complex workflows
    timeout_seconds: 90.0
config_schema:
  type: object
  properties:
    max_retry_attempts:
      type: integer
      minimum: 1
      maximum: 10
      default: 3
      description: Maximum retry attempts for AI operations
    default_confidence_threshold:
      type: number
      minimum: 0.0
      maximum: 1.0
      default: 0.7
      description: Default confidence threshold for AI suggestions
    enable_parallel_processing:
      type: boolean
      default: true
      description: Enable parallel processing of AI tasks
    workflow_timeout_seconds:
      type: number
      minimum: 30.0
      maximum: 600.0
      default: 180.0
      description: Default timeout for AI workflows
    enable_caching:
      type: boolean
      default: true
      description: Enable caching of AI results
    cache_ttl_seconds:
      type: integer
      minimum: 60
      maximum: 86400
      default: 3600
      description: Cache time-to-live in seconds
  additionalProperties: false
events:
  emitted:
    - ai.orchestration.started
    - ai.orchestration.completed
    - ai.orchestration.failed
    - ai.workflow.step_completed
    - ai.validation.performed
    - ai.cache.hit
    - ai.cache.miss
  listened:
    - plugin.generation.requested
    - app.analysis.requested
    - validation.required
metadata:
  tags:
    - ai
    - orchestration
    - coordination
    - internal
  category: framework-internal
  stability: stable
  documentation_url: https://plugginger.dev/plugins/internal/ai-orchestrator
  repository_url: https://github.com/jkehrhahn/plugginger
  created_at: "2025-01-27T15:00:00Z"
  updated_at: "2025-01-27T15:00:00Z"
