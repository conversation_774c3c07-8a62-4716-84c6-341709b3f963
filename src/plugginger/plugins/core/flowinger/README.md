# Flowinger Plugin

**Version:** 1.0.0  
**Core Plugin**

## Overview

The Flowinger plugin provides a powerful, AI-assisted workflow decomposition service. It implements a recursive algorithm inspired by the "Flow: Modularized Agentic Workflow Automation" paper to break down large, complex tasks or topics into a structured, hierarchical, and executable plan.

This plugin acts as a "system architect" Co-Pilot. It takes a high-level objective and produces a detailed task graph, emphasizing modularity, parallelism, and adherence to software engineering best practices.

### Core Inspiration

This plugin's methodology is heavily inspired by the concepts presented in the following academic paper:

> <PERSON><PERSON>, B<PERSON>, <PERSON>, Y<PERSON>, <PERSON>, K<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, <PERSON>. (2025). *Flow: Modularized Agentic Workflow Automation*. arXiv:2501.07834v2 [cs.AI].

While the paper focuses on multi-agent systems, this plugin adapts its core principles of AOV graphs, dynamic refinement, and modularity for robust, single-agent task decomposition within the Plugginger framework.

## Features

-   **Recursive Decomposition:** Breaks down tasks into multiple levels of sub-tasks until they become "operationally atomic."
-   **Hybrid Intelligence:** Uses LLMs for creative and semantic tasks (e.g., generating workflow candidates) and deterministic Python code for analysis (e.g., calculating graph metrics).
-   **Best-Practice Driven:** Prompts are engineered to encourage modularity, the Single Responsibility Principle (SRP), and low coupling.
-   **Structured Output:** Returns a well-defined Pydantic model representing the entire task hierarchy.

## Services

### `flowinger.decompose_task`

Takes a high-level task description and returns a detailed, hierarchical `DecompositionResult` object.

**Example Usage:**

```python
# In another plugin that depends on 'flowinger'
decomposition = await self.app.call_service(
    "flowinger.decompose_task",
    main_task_description="Build a complete e-commerce backend",
    context="The backend should use Stripe for payments and support user accounts."
)

# You can now traverse the decomposition.root_task tree
print(decomposition.root_task.description)
for child_task in decomposition.root_task.children:
    print(f"- {child_task.description}")