"""
Outlines Provider for structured text generation.
"""
import json
import logging
import warnings
from typing import Any, Dict, List, Optional, Union, Type
from enum import Enum
from pydantic import BaseModel, create_model

from plugginger.core.exceptions import PluggingerConfigurationError

try:
    import outlines
    OUTLINES_AVAILABLE = True
except ImportError:
    outlines = None
    OUTLINES_AVAILABLE = False

# Suppress warnings from outlines/transformers
warnings.filterwarnings("ignore", category=UserWarning, module="outlines")
warnings.filterwarnings("ignore", category=FutureWarning, module="transformers")

logger = logging.getLogger(__name__)


class OutlinesProvider:
    """Provider for structured text generation using Outlines framework."""

    def __init__(
        self,
        model_name: str,
        provider: str = "transformers",
        device: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """Initialize Outlines provider.
        
        Args:
            model_name: Name of the model to use
            provider: Provider type (transformers, openai, etc.)
            device: Device to run model on (cuda, cpu, etc.)
            **kwargs: Additional model configuration
        """
        if not OUTLINES_AVAILABLE:
            raise PluggingerConfigurationError(
                "Outlines is not installed. Install with: pip install outlines"
            )
        
        self.model_name = model_name
        self.provider = provider
        self.device = device
        self.model_kwargs = kwargs
        self._model: Optional[Any] = None
        self._model_counter = 0  # For unique model names

        logger.info(f"Initializing Outlines provider with model: {model_name}")

    @property
    def model(self) -> Any:
        """Get or create the Outlines model."""
        if self._model is None:
            self._model = self._create_model()
        return self._model

    def _create_model(self) -> Any:
        """Create Outlines model based on provider."""
        try:
            if self.provider == "transformers":
                return outlines.models.transformers(
                    self.model_name,
                    device=self.device,
                    **self.model_kwargs
                )
            elif self.provider == "openai":
                return outlines.models.openai(
                    self.model_name,
                    **self.model_kwargs
                )
            elif self.provider == "llamacpp":
                return outlines.models.llamacpp(
                    self.model_name,
                    **self.model_kwargs
                )
            else:
                raise PluggingerConfigurationError(
                    f"Unsupported Outlines provider: {self.provider}"
                )
        except Exception as e:
            logger.error(f"Failed to create Outlines model: {e}")
            raise PluggingerConfigurationError(f"Failed to create Outlines model: {e}")

    def _dict_to_pydantic_model(self, schema_dict: Dict[str, Any]) -> Any:
        """Convert JSON schema dict to Pydantic model.

        Args:
            schema_dict: JSON schema dictionary

        Returns:
            Pydantic model class
        """
        try:
            # Generate unique model name
            self._model_counter += 1
            model_name = f"DynamicModel_{self._model_counter}"

            # Extract properties and required fields
            properties = schema_dict.get("properties", {})
            required_fields = set(schema_dict.get("required", []))

            # Convert JSON schema types to Python types
            type_mapping = {
                "string": str,
                "integer": int,
                "number": float,
                "boolean": bool,
                "array": list,
                "object": dict
            }

            # Build field definitions
            field_definitions: Dict[str, Any] = {}
            for field_name, field_schema in properties.items():
                field_type = field_schema.get("type", "string")
                python_type = type_mapping.get(field_type, str)

                # Handle enum constraints - just use string type
                if "enum" in field_schema:
                    python_type = str  # Use string type for enum values

                # For OpenAI, ALL fields must be required
                if self.provider == "openai":
                    field_definitions[field_name] = python_type
                    # Add to required fields for OpenAI
                    required_fields.add(field_name)
                else:
                    # For other providers, handle optional fields
                    if field_name in required_fields:
                        field_definitions[field_name] = python_type
                    else:
                        default_value = field_schema.get("default")
                        if default_value is None:
                            if python_type == str:
                                default_value = ""
                            elif python_type == int:
                                default_value = 0
                            elif python_type == float:
                                default_value = 0.0
                            elif python_type == bool:
                                default_value = False
                            elif python_type == list:
                                default_value = []
                            elif python_type == dict:
                                default_value = {}
                        field_definitions[field_name] = (python_type, default_value)

            # Create dynamic Pydantic model with BaseModel base
            return create_model(model_name, __base__=BaseModel, **field_definitions)

        except Exception as e:
            logger.error(f"Failed to convert schema to Pydantic model: {e}")
            # Fallback: create simple model with any fields
            return create_model("FallbackModel", __base__=BaseModel, data=(dict, {}), message=(str, ""))

    def _make_all_fields_required(self, model_class: Type[BaseModel]) -> Any:
        """Make all fields in a Pydantic model required for OpenAI compatibility.

        Args:
            model_class: Original Pydantic model class

        Returns:
            New Pydantic model with all fields required
        """
        try:
            # Get model schema
            schema = model_class.model_json_schema()

            # Make all properties required
            properties = schema.get("properties", {})
            required_fields = list(properties.keys())

            # Update schema
            schema["required"] = required_fields

            # Convert back to Pydantic model
            return self._dict_to_pydantic_model(schema)

        except Exception as e:
            logger.error(f"Failed to make fields required: {e}")
            # Fallback to original model
            return model_class

    async def generate_json(
        self,
        prompt: str,
        schema: Union[Dict[str, Any], Type[BaseModel], str],
        max_tokens: int = 1000,
        temperature: float = 0.1,
        seed: Optional[int] = None,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """Generate JSON following a schema.

        Args:
            prompt: Input prompt
            schema: JSON schema, Pydantic model, or schema string
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            seed: Random seed for reproducibility
            **kwargs: Additional generation parameters

        Returns:
            Dictionary with generated content and metadata
        """
        try:
            # Convert dict schema to Pydantic model if needed
            if isinstance(schema, dict):
                schema = self._dict_to_pydantic_model(schema)
            elif isinstance(schema, str):
                # Parse JSON schema string
                schema_dict = json.loads(schema)
                schema = self._dict_to_pydantic_model(schema_dict)
            elif isinstance(schema, type) and issubclass(schema, BaseModel):
                # For OpenAI, we need to modify Pydantic models to make all fields required
                if self.provider == "openai":
                    schema = self._make_all_fields_required(schema)

            # Create JSON generator
            generator = outlines.generate.json(self.model, schema)

            # Prepare generation parameters (remove seed for OpenAI)
            gen_params = {
                "max_tokens": max_tokens,
                "temperature": temperature,
                **kwargs
            }

            # Only add seed for non-OpenAI models
            if self.provider != "openai" and seed is not None:
                gen_params["seed"] = seed

            # Generate structured output (handle event loop issue)
            import asyncio
            try:
                # Try to run in current event loop
                loop = asyncio.get_running_loop()
                # If we're in an event loop, run in thread pool
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(generator, prompt, **gen_params)
                    result = future.result(timeout=60)  # 60 second timeout
            except RuntimeError:
                # No event loop running, safe to call directly
                result = generator(prompt, **gen_params)

            # Convert to dict if it's a Pydantic model
            if isinstance(result, BaseModel):
                content = result.model_dump()
            else:
                content = result
            
            return {
                "success": True,
                "content": content,
                "raw_output": json.dumps(content, indent=2),
                "schema_validated": True,
                "provider": "outlines",
                "model": self.model_name,
                "tokens_used": len(str(content).split()),  # Approximate
            }
            
        except Exception as e:
            logger.error(f"JSON generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "provider": "outlines",
                "model": self.model_name,
            }

    async def generate_choice(
        self,
        prompt: str,
        choices: Union[List[str], Type[Enum]],
        max_tokens: int = 100,
        temperature: float = 0.1,
        seed: Optional[int] = None,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """Generate text with multiple choice constraint.
        
        Args:
            prompt: Input prompt
            choices: List of choices or Enum class
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            seed: Random seed for reproducibility
            **kwargs: Additional generation parameters
            
        Returns:
            Dictionary with generated content and metadata
        """
        try:
            # Create choice generator
            generator = outlines.generate.choice(self.model, choices)
            
            # Generate constrained output
            result = generator(
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                seed=seed,
                **kwargs
            )
            
            return {
                "success": True,
                "content": result,
                "choice_validated": True,
                "available_choices": list(choices) if isinstance(choices, list) else [e.value for e in choices],
                "provider": "outlines",
                "model": self.model_name,
                "tokens_used": len(str(result).split()),
            }
            
        except Exception as e:
            logger.error(f"Choice generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "provider": "outlines",
                "model": self.model_name,
            }

    async def generate_regex(
        self,
        prompt: str,
        pattern: str,
        max_tokens: int = 500,
        temperature: float = 0.1,
        seed: Optional[int] = None,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """Generate text following a regex pattern.

        Args:
            prompt: Input prompt
            pattern: Regex pattern to follow
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            seed: Random seed for reproducibility
            **kwargs: Additional generation parameters

        Returns:
            Dictionary with generated content and metadata
        """
        # OpenAI doesn't support regex constraints
        if self.provider == "openai":
            return {
                "success": False,
                "error": "Cannot use regex-structured generation with an OpenAI modeldue to the limitations of the OpenAI API.",
                "provider": "outlines",
                "model": self.model_name,
            }

        try:
            # Create regex generator
            generator = outlines.generate.regex(self.model, pattern)
            
            # Generate pattern-constrained output
            result = generator(
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                seed=seed,
                **kwargs
            )
            
            return {
                "success": True,
                "content": result,
                "pattern_validated": True,
                "regex_pattern": pattern,
                "provider": "outlines",
                "model": self.model_name,
                "tokens_used": len(str(result).split()),
            }
            
        except Exception as e:
            logger.error(f"Regex generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "provider": "outlines",
                "model": self.model_name,
            }

    async def generate_grammar(
        self,
        prompt: str,
        grammar: str,
        max_tokens: int = 1000,
        temperature: float = 0.1,
        seed: Optional[int] = None,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """Generate text following an EBNF grammar.
        
        Args:
            prompt: Input prompt
            grammar: EBNF grammar string
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            seed: Random seed for reproducibility
            **kwargs: Additional generation parameters
            
        Returns:
            Dictionary with generated content and metadata
        """
        try:
            # Create grammar generator
            generator = outlines.generate.cfg(self.model, grammar)
            
            # Generate grammar-constrained output
            result = generator(
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                seed=seed,
                **kwargs
            )
            
            return {
                "success": True,
                "content": result,
                "grammar_validated": True,
                "ebnf_grammar": grammar,
                "provider": "outlines",
                "model": self.model_name,
                "tokens_used": len(str(result).split()),
            }
            
        except Exception as e:
            logger.error(f"Grammar generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "provider": "outlines",
                "model": self.model_name,
            }

    async def generate_format(
        self,
        prompt: str,
        format_type: type,
        max_tokens: int = 200,
        temperature: float = 0.1,
        seed: Optional[int] = None,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """Generate text with type format constraint.
        
        Args:
            prompt: Input prompt
            format_type: Python type (int, float, bool, etc.)
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            seed: Random seed for reproducibility
            **kwargs: Additional generation parameters
            
        Returns:
            Dictionary with generated content and metadata
        """
        try:
            # Create format generator
            generator = outlines.generate.format(self.model, format_type)
            
            # Generate type-constrained output
            result = generator(
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                seed=seed,
                **kwargs
            )
            
            return {
                "success": True,
                "content": result,
                "type_validated": True,
                "expected_type": format_type.__name__,
                "actual_type": type(result).__name__,
                "provider": "outlines",
                "model": self.model_name,
                "tokens_used": len(str(result).split()),
            }
            
        except Exception as e:
            logger.error(f"Format generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "provider": "outlines",
                "model": self.model_name,
            }


class OutlinesProviderFactory:
    """Factory for creating Outlines providers."""

    @staticmethod
    def create_transformers_provider(
        model_name: str,
        device: Optional[str] = None,
        **kwargs: Any
    ) -> OutlinesProvider:
        """Create Outlines provider for Transformers models."""
        return OutlinesProvider(
            model_name=model_name,
            provider="transformers",
            device=device,
            **kwargs
        )

    @staticmethod
    def create_openai_provider(
        model_name: str = "gpt-4o-mini",
        **kwargs: Any
    ) -> OutlinesProvider:
        """Create Outlines provider for OpenAI models."""
        return OutlinesProvider(
            model_name=model_name,
            provider="openai",
            **kwargs
        )

    @staticmethod
    def create_llamacpp_provider(
        model_path: str,
        **kwargs: Any
    ) -> OutlinesProvider:
        """Create Outlines provider for llama.cpp models."""
        return OutlinesProvider(
            model_name=model_path,
            provider="llamacpp",
            **kwargs
        )
