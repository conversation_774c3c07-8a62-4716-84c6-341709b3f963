"""
Schema Service for JSON Schema management and validation.
"""
import json
import logging
from typing import Any, Dict, List, Optional, Type, Union
from dataclasses import dataclass
from enum import Enum

try:
    from pydantic import BaseModel, ValidationError
    PYDANTIC_AVAILABLE = True
except ImportError:
    BaseModel = object  # type: ignore[misc,assignment]
    ValidationError = Exception  # type: ignore[misc,assignment]
    PYDANTIC_AVAILABLE = False

try:
    import jsonschema
    JSONSCHEMA_AVAILABLE = True
except ImportError:
    jsonschema = None
    JSONSCHEMA_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class SchemaValidationResult:
    """Result of schema validation."""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    confidence_score: float
    validated_data: Optional[Any] = None
    schema_type: Optional[str] = None


class SchemaService:
    """Service for managing and validating JSON schemas."""

    def __init__(self) -> None:
        """Initialize schema service."""
        self.registered_schemas: Dict[str, Dict[str, Any]] = {}
        self.pydantic_models: Dict[str, Type[BaseModel]] = {}

    def register_schema(self, name: str, schema: Dict[str, Any]) -> None:
        """Register a JSON schema.
        
        Args:
            name: Schema name/identifier
            schema: JSON schema dictionary
        """
        self.registered_schemas[name] = schema
        logger.info(f"Registered schema: {name}")

    def register_pydantic_model(self, name: str, model: Type[BaseModel]) -> None:
        """Register a Pydantic model.
        
        Args:
            name: Model name/identifier
            model: Pydantic model class
        """
        if not PYDANTIC_AVAILABLE:
            raise ImportError("Pydantic is required for Pydantic model support")
        
        self.pydantic_models[name] = model
        logger.info(f"Registered Pydantic model: {name}")

    def get_schema(self, name: str) -> Optional[Dict[str, Any]]:
        """Get registered schema by name.
        
        Args:
            name: Schema name
            
        Returns:
            Schema dictionary or None if not found
        """
        return self.registered_schemas.get(name)

    def get_pydantic_model(self, name: str) -> Optional[Type[BaseModel]]:
        """Get registered Pydantic model by name.
        
        Args:
            name: Model name
            
        Returns:
            Pydantic model class or None if not found
        """
        return self.pydantic_models.get(name)

    def pydantic_to_schema(self, model: Type[BaseModel]) -> Dict[str, Any]:
        """Convert Pydantic model to JSON schema.
        
        Args:
            model: Pydantic model class
            
        Returns:
            JSON schema dictionary
        """
        if not PYDANTIC_AVAILABLE:
            raise ImportError("Pydantic is required for this operation")
        
        return model.model_json_schema()

    def validate_with_schema(
        self,
        data: Union[str, Dict[str, Any]],
        schema: Dict[str, Any]
    ) -> SchemaValidationResult:
        """Validate data against JSON schema.
        
        Args:
            data: Data to validate (JSON string or dict)
            schema: JSON schema
            
        Returns:
            Validation result
        """
        errors: List[str] = []
        warnings: List[str] = []
        
        # Parse JSON if string
        if isinstance(data, str):
            try:
                parsed_data = json.loads(data)
            except json.JSONDecodeError as e:
                return SchemaValidationResult(
                    is_valid=False,
                    errors=[f"Invalid JSON: {str(e)}"],
                    warnings=[],
                    confidence_score=0.0,
                    schema_type="json_schema"
                )
        else:
            parsed_data = data

        # Validate with jsonschema if available
        if JSONSCHEMA_AVAILABLE and jsonschema:
            try:
                jsonschema.validate(parsed_data, schema)
            except jsonschema.ValidationError as e:
                errors.append(f"Schema validation error: {e.message}")
            except jsonschema.SchemaError as e:
                errors.append(f"Invalid schema: {e.message}")
        else:
            # Basic validation without jsonschema
            basic_result = self._basic_schema_validation(parsed_data, schema)
            errors.extend(basic_result.errors)
            warnings.extend(basic_result.warnings)

        confidence_score = 1.0 - (len(errors) * 0.2) - (len(warnings) * 0.1)
        
        return SchemaValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            confidence_score=max(confidence_score, 0.0),
            validated_data=parsed_data if len(errors) == 0 else None,
            schema_type="json_schema"
        )

    def validate_with_pydantic(
        self,
        data: Union[str, Dict[str, Any]],
        model: Type[BaseModel]
    ) -> SchemaValidationResult:
        """Validate data with Pydantic model.
        
        Args:
            data: Data to validate
            model: Pydantic model class
            
        Returns:
            Validation result
        """
        if not PYDANTIC_AVAILABLE:
            return SchemaValidationResult(
                is_valid=False,
                errors=["Pydantic is not available"],
                warnings=[],
                confidence_score=0.0,
                schema_type="pydantic"
            )

        # Parse JSON if string
        if isinstance(data, str):
            try:
                parsed_data = json.loads(data)
            except json.JSONDecodeError as e:
                return SchemaValidationResult(
                    is_valid=False,
                    errors=[f"Invalid JSON: {str(e)}"],
                    warnings=[],
                    confidence_score=0.0,
                    schema_type="pydantic"
                )
        else:
            parsed_data = data

        try:
            validated_instance = model(**parsed_data)
            return SchemaValidationResult(
                is_valid=True,
                errors=[],
                warnings=[],
                confidence_score=1.0,
                validated_data=validated_instance,
                schema_type="pydantic"
            )
        except ValidationError as e:
            errors = [f"Pydantic validation error: {error['msg']}" for error in e.errors()]
            return SchemaValidationResult(
                is_valid=False,
                errors=errors,
                warnings=[],
                confidence_score=0.0,
                schema_type="pydantic"
            )

    def _basic_schema_validation(
        self,
        data: Dict[str, Any],
        schema: Dict[str, Any]
    ) -> SchemaValidationResult:
        """Basic schema validation without jsonschema library.
        
        Args:
            data: Data to validate
            schema: JSON schema
            
        Returns:
            Validation result
        """
        errors: List[str] = []
        warnings: List[str] = []

        # Check required fields
        if "required" in schema:
            for field in schema["required"]:
                if field not in data:
                    errors.append(f"Missing required field: {field}")

        # Check field types
        if "properties" in schema:
            for field, field_schema in schema["properties"].items():
                if field in data:
                    expected_type = field_schema.get("type")
                    actual_value = data[field]
                    
                    if not self._check_type(actual_value, expected_type):
                        errors.append(
                            f"Field '{field}' should be {expected_type}, "
                            f"got {type(actual_value).__name__}"
                        )

        return SchemaValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            confidence_score=1.0 - (len(errors) * 0.2),
            schema_type="basic"
        )

    def _check_type(self, value: Any, expected_type: Optional[str]) -> bool:
        """Check if value matches expected JSON schema type.
        
        Args:
            value: Value to check
            expected_type: Expected JSON schema type
            
        Returns:
            True if type matches
        """
        if expected_type is None:
            return True
            
        type_mapping = {
            "string": str,
            "number": (int, float),
            "integer": int,
            "boolean": bool,
            "array": list,
            "object": dict,
            "null": type(None)
        }
        
        expected_python_type = type_mapping.get(expected_type)
        if expected_python_type is None:
            return True  # Unknown type, assume valid
            
        return isinstance(value, expected_python_type)

    def create_common_schemas(self) -> None:
        """Register common useful schemas."""
        # User profile schema
        user_schema = {
            "type": "object",
            "properties": {
                "name": {"type": "string", "maxLength": 100},
                "email": {"type": "string", "format": "email"},
                "age": {"type": "integer", "minimum": 0, "maximum": 150},
                "bio": {"type": "string", "maxLength": 500},
                "active": {"type": "boolean"}
            },
            "required": ["name", "email"],
            "additionalProperties": False
        }
        self.register_schema("user_profile", user_schema)

        # API response schema
        api_response_schema = {
            "type": "object",
            "properties": {
                "success": {"type": "boolean"},
                "data": {"type": "object"},
                "message": {"type": "string"},
                "error_code": {"type": "string"},
                "timestamp": {"type": "string", "format": "date-time"}
            },
            "required": ["success"],
            "additionalProperties": True
        }
        self.register_schema("api_response", api_response_schema)

        # Code generation schema
        code_schema = {
            "type": "object",
            "properties": {
                "language": {"type": "string", "enum": ["python", "javascript", "sql", "bash"]},
                "code": {"type": "string"},
                "description": {"type": "string"},
                "dependencies": {"type": "array", "items": {"type": "string"}},
                "test_cases": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "input": {"type": "string"},
                            "expected_output": {"type": "string"}
                        }
                    }
                }
            },
            "required": ["language", "code"],
            "additionalProperties": False
        }
        self.register_schema("code_generation", code_schema)

        logger.info("Registered common schemas: user_profile, api_response, code_generation")
