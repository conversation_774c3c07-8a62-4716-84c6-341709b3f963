"""
Grammar Service for EBNF grammar management and validation.
"""
import logging
import re
from typing import Any, Dict, List, Optional, Set
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


@dataclass
class EBNFGrammar:
    """EBNF Grammar definition."""
    name: str
    grammar: str
    description: Optional[str] = None
    examples: Optional[List[str]] = None


@dataclass
class GrammarValidationResult:
    """Result of grammar validation."""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    confidence_score: float
    parsed_tokens: Optional[List[str]] = None


class GrammarService:
    """Service for managing and validating EBNF grammars."""

    def __init__(self) -> None:
        """Initialize grammar service."""
        self.registered_grammars: Dict[str, EBNFGrammar] = {}
        self._create_common_grammars()

    def register_grammar(self, grammar: EBNFGrammar) -> None:
        """Register an EBNF grammar.
        
        Args:
            grammar: EBNF grammar definition
        """
        self.registered_grammars[grammar.name] = grammar
        logger.info(f"Registered grammar: {grammar.name}")

    def get_grammar(self, name: str) -> Optional[EBNFGrammar]:
        """Get registered grammar by name.
        
        Args:
            name: Grammar name
            
        Returns:
            EBNF grammar or None if not found
        """
        return self.registered_grammars.get(name)

    def list_grammars(self) -> List[str]:
        """List all registered grammar names.
        
        Returns:
            List of grammar names
        """
        return list(self.registered_grammars.keys())

    def validate_grammar_syntax(self, grammar: str) -> GrammarValidationResult:
        """Validate EBNF grammar syntax.
        
        Args:
            grammar: EBNF grammar string
            
        Returns:
            Validation result
        """
        errors: List[str] = []
        warnings: List[str] = []
        
        # Basic EBNF syntax validation
        lines = grammar.strip().split('\n')
        rules: Set[str] = set()
        referenced_rules: Set[str] = set()
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            # Check for rule definition (rule_name: definition)
            if ':' in line and not line.strip().startswith('%'):
                rule_match = re.match(r'^([a-zA-Z_?][a-zA-Z0-9_]*)\s*:', line)
                if rule_match:
                    rule_name = rule_match.group(1).lstrip('?')  # Remove optional prefix
                    rules.add(rule_name)

                    # Find referenced rules in the definition
                    definition = line.split(':', 1)[1]
                    refs = re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b', definition)
                    for ref in refs:
                        if not ref.isupper() and not ref.startswith('_'):  # Skip terminals and special rules
                            referenced_rules.add(ref)
                elif not line.strip().startswith('%') and not line.strip().startswith('#'):
                    # Only warn for non-directive lines
                    warnings.append(f"Line {line_num}: Possible rule syntax issue")
            elif line.strip().startswith('%'):
                # Skip import/directive lines
                continue
            else:
                # Continuation line or invalid syntax
                if not line.startswith((' ', '\t', '|', '"', "'")):
                    warnings.append(f"Line {line_num}: Possible syntax issue")

        # Check for undefined rules
        undefined_rules = referenced_rules - rules
        for rule in undefined_rules:
            if not self._is_builtin_rule(rule):
                errors.append(f"Undefined rule: {rule}")

        # Check for unused rules
        unused_rules = rules - referenced_rules
        if unused_rules and 'start' not in unused_rules:
            warnings.append(f"Unused rules: {', '.join(unused_rules)}")

        confidence_score = 1.0 - (len(errors) * 0.3) - (len(warnings) * 0.1)
        
        return GrammarValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            confidence_score=max(confidence_score, 0.0)
        )

    def _is_builtin_rule(self, rule: str) -> bool:
        """Check if rule is a built-in EBNF rule.
        
        Args:
            rule: Rule name
            
        Returns:
            True if built-in rule
        """
        builtin_rules = {
            'NUMBER', 'STRING', 'WORD', 'LETTER', 'DIGIT',
            'NEWLINE', 'WS', 'WHITESPACE', 'ESCAPED_STRING',
            'SIGNED_NUMBER', 'DECIMAL', 'HEXADECIMAL', 'CNAME',
            'start', 'expression', 'term', 'factor', 'value',
            'object', 'array', 'pair', 'string', 'number',
            'true', 'false', 'null', 'def', 'if', 'for', 'while',
            'in', 'None', 'True', 'False', 'AS', 'SELECT', 'FROM',
            'WHERE', 'ORDER', 'BY', 'AND', 'OR', 'LIKE'
        }
        return rule in builtin_rules

    def _create_common_grammars(self) -> None:
        """Create and register common useful grammars."""
        
        # Arithmetic expressions
        arithmetic_grammar = EBNFGrammar(
            name="arithmetic",
            grammar="""
                ?start: expression
                ?expression: term (("+" | "-") term)*
                ?term: factor (("*" | "/") factor)*
                ?factor: NUMBER
                       | "-" factor
                       | "(" expression ")"
                %import common.NUMBER
            """,
            description="Grammar for arithmetic expressions",
            examples=["2 + 3 * 4", "(5 - 2) / 3", "-42"]
        )
        self.register_grammar(arithmetic_grammar)

        # JSON grammar
        json_grammar = EBNFGrammar(
            name="json",
            grammar="""
                ?start: value
                ?value: object
                      | array
                      | string
                      | SIGNED_NUMBER
                      | "true" | "false" | "null"
                
                array: "[" [value ("," value)*] "]"
                object: "{" [pair ("," pair)*] "}"
                pair: string ":" value
                string: ESCAPED_STRING
                
                %import common.ESCAPED_STRING
                %import common.SIGNED_NUMBER
                %import common.WS
                %ignore WS
            """,
            description="Grammar for JSON format",
            examples=['{"name": "John", "age": 30}', '[1, 2, 3]', '"hello"']
        )
        self.register_grammar(json_grammar)

        # Python function definition
        python_function_grammar = EBNFGrammar(
            name="python_function",
            grammar="""
                ?start: function_def
                function_def: "def" NAME "(" [parameters] ")" ":" NEWLINE INDENT body DEDENT
                parameters: parameter ("," parameter)*
                parameter: NAME [":" type] ["=" default_value]
                type: NAME
                default_value: NUMBER | STRING | "None" | "True" | "False"
                body: statement+
                statement: simple_stmt | compound_stmt
                simple_stmt: (expression | assignment) NEWLINE
                compound_stmt: if_stmt | for_stmt | while_stmt
                assignment: NAME "=" expression
                expression: NAME | NUMBER | STRING | function_call
                function_call: NAME "(" [arguments] ")"
                arguments: expression ("," expression)*
                if_stmt: "if" expression ":" NEWLINE INDENT body DEDENT
                for_stmt: "for" NAME "in" expression ":" NEWLINE INDENT body DEDENT
                while_stmt: "while" expression ":" NEWLINE INDENT body DEDENT
                
                %import common.CNAME -> NAME
                %import common.NUMBER
                %import common.ESCAPED_STRING -> STRING
                %import common.NEWLINE
                %import common.WS
                INDENT: /    +/
                DEDENT: ""
                %ignore WS
            """,
            description="Grammar for Python function definitions",
            examples=[
                "def add(a: int, b: int) -> int:\n    return a + b",
                "def greet(name: str = 'World'):\n    print(f'Hello, {name}!')"
            ]
        )
        self.register_grammar(python_function_grammar)

        # SQL SELECT statement
        sql_select_grammar = EBNFGrammar(
            name="sql_select",
            grammar="""
                ?start: select_stmt
                select_stmt: "SELECT" select_list "FROM" table_list [where_clause] [order_clause]
                select_list: column ("," column)*
                column: NAME ["." NAME] [alias]
                alias: ["AS"] NAME
                table_list: table ("," table)*
                table: NAME [alias]
                where_clause: "WHERE" condition
                condition: expression comparison expression
                         | condition "AND" condition
                         | condition "OR" condition
                         | "(" condition ")"
                comparison: "=" | "!=" | "<" | ">" | "<=" | ">=" | "LIKE"
                expression: NAME ["." NAME] | STRING | NUMBER
                order_clause: "ORDER BY" order_list
                order_list: order_item ("," order_item)*
                order_item: column ["ASC" | "DESC"]
                
                %import common.CNAME -> NAME
                %import common.NUMBER
                %import common.ESCAPED_STRING -> STRING
                %import common.WS
                %ignore WS
            """,
            description="Grammar for SQL SELECT statements",
            examples=[
                "SELECT name, age FROM users WHERE age > 18",
                "SELECT u.name, p.title FROM users u, posts p WHERE u.id = p.user_id"
            ]
        )
        self.register_grammar(sql_select_grammar)

        # Email address
        email_grammar = EBNFGrammar(
            name="email",
            grammar="""
                ?start: email
                email: local_part "@" domain
                local_part: atom ("." atom)*
                domain: atom ("." atom)*
                atom: LETTER (LETTER | DIGIT | "-" | "_")*
                
                %import common.LETTER
                %import common.DIGIT
            """,
            description="Grammar for email addresses",
            examples=["<EMAIL>", "<EMAIL>"]
        )
        self.register_grammar(email_grammar)

        # URL
        url_grammar = EBNFGrammar(
            name="url",
            grammar="""
                ?start: url
                url: scheme "://" [authority] path ["?" query] ["#" fragment]
                scheme: LETTER (LETTER | DIGIT | "+" | "-" | ".")*
                authority: [userinfo "@"] host [":" port]
                userinfo: (LETTER | DIGIT | "-" | "." | "_" | "~" | "!" | "$" | "&" | "'" | "(" | ")" | "*" | "+" | "," | ";" | "=" | ":")+
                host: hostname | ip_address
                hostname: (LETTER | DIGIT | "-")+ ("." (LETTER | DIGIT | "-")+)*
                ip_address: DIGIT+ "." DIGIT+ "." DIGIT+ "." DIGIT+
                port: DIGIT+
                path: ("/" segment)*
                segment: (LETTER | DIGIT | "-" | "." | "_" | "~" | "!" | "$" | "&" | "'" | "(" | ")" | "*" | "+" | "," | ";" | "=" | ":" | "@")*
                query: (LETTER | DIGIT | "-" | "." | "_" | "~" | "!" | "$" | "&" | "'" | "(" | ")" | "*" | "+" | "," | ";" | "=" | ":" | "@" | "/" | "?")*
                fragment: (LETTER | DIGIT | "-" | "." | "_" | "~" | "!" | "$" | "&" | "'" | "(" | ")" | "*" | "+" | "," | ";" | "=" | ":" | "@" | "/" | "?")*
                
                %import common.LETTER
                %import common.DIGIT
            """,
            description="Grammar for URLs",
            examples=["https://example.com/path", "**************************/path?query=value#fragment"]
        )
        self.register_grammar(url_grammar)

        logger.info("Registered common grammars: arithmetic, json, python_function, sql_select, email, url")
