"""
LLM Provider Core Plugin.

Provides a unified LLM abstraction with multi-provider support, powered by LiteLLM.
This is a reusable core plugin that end users can leverage in their applications.

This plugin enables seamless interaction with over 100 LLM providers through a
single, consistent interface. It includes features like automatic provider
detection from environment variables, structured JSON output, and cost tracking.

Example:
    # In your application builder
    from plugginger.api import PluggingerAppBuilder
    from plugginger.plugins.core_loader import load_core_plugins

    builder = PluggingerAppBuilder(app_name="my_ai_app")
    # Load the llm_provider plugin
    load_core_plugins(["llm_provider"], builder)
    app = builder.build()

    # Use the service to generate text
    response = await app.call_service(
        "llm_provider.generate_text",
        prompt="Explain quantum computing in simple terms."
    )
    print(response["content"])
"""

from .llm_provider_plugin import LLMProviderPlugin
from .services import (
    LiteLLMObservability,
    LiteLLMProvider,
    LiteLLMProviderFactory,
    ProviderHealthMonitor,
    ResponseValidationService,
)

__all__ = [
    "LLMProviderPlugin",
    # Exposing key service classes for type hinting and advanced usage
    "LiteLLMProvider",
    "LiteLLMProviderFactory",
    "LiteLLMObservability",
    "ProviderHealthMonitor",
    "ResponseValidationService",
]
