name: llm_provider
version: 1.0.0
description: LLM provider abstraction with multi-provider support and structured output validation
author: Plugginger Framework
license: MIT
plugin_type: core
execution_mode: thread
dependencies:
  - name: json_validator
    version: ">=1.0.0"
    optional: false
services:
  - name: generate_structured
    description: Generate structured LLM response with EBNF grammar validation
    timeout_seconds: 30.0
  - name: generate_text
    description: Generate plain text response from LLM
    timeout_seconds: 30.0
  - name: validate_response
    description: Validate LLM response against expected format
    timeout_seconds: 5.0
  - name: get_provider_info
    description: Get information about current LLM provider
    timeout_seconds: 1.0
config_schema:
  type: object
  properties:
    provider:
      type: string
      enum: ["openai", "anthropic", "local"]
      description: LLM provider type
    api_key:
      type: string
      description: API key for the LLM service
    model:
      type: string
      description: Model name (uses provider default if not specified)
    base_url:
      type: string
      description: Base URL for API (uses provider default if not specified)
    default_temperature:
      type: number
      minimum: 0.0
      maximum: 2.0
      default: 0.1
      description: Default sampling temperature
    default_max_retries:
      type: integer
      minimum: 1
      maximum: 10
      default: 3
      description: Default maximum retry attempts
    timeout_seconds:
      type: number
      minimum: 1.0
      maximum: 300.0
      default: 30.0
      description: Default request timeout
  required:
    - provider
  additionalProperties: false
events:
  emitted:
    - llm.request.started
    - llm.request.completed
    - llm.request.failed
    - llm.response.validated
    - llm.provider.changed
  listened: []
metadata:
  tags:
    - ai
    - llm
    - generation
    - validation
  category: ai-services
  stability: stable
  documentation_url: https://plugginger.dev/plugins/core/llm-provider
  repository_url: https://github.com/jkehrhahn/plugginger
  created_at: "2025-01-27T12:00:00Z"
  updated_at: "2025-01-27T12:00:00Z"
