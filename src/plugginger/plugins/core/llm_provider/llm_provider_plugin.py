"""
LLM Provider Core Plugin.
Provides a unified LLM abstraction with multi-provider support, powered by LiteLLM.
"""
import logging
from typing import Any

from plugginger.api.depends import Depends
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service
from plugginger.core.exceptions import PluggingerConfigurationError

from .services.litellm_factory import LiteLLMProviderFactory
from .services.litellm_production import health_monitor, security_manager
from .services.litellm_provider import Li<PERSON><PERSON><PERSON>rovider
from .services.validation_service import ResponseValidationService

logger = logging.getLogger(__name__)

@plugin(name="llm_provider", version="1.0.0")
class LLMProviderPlugin(PluginBase):
    """Core plugin for LLM provider abstraction."""

    needs = [Depends("json_validator", optional=True)]

    def __init__(self, **injected_dependencies: Any) -> None:
        super().__init__(**injected_dependencies)
        self.logger = logger
        self.validation_service = ResponseValidationService()
        self.provider: LiteLLMProvider | None = None

    async def setup(self, plugin_config: Any = None) -> None:
        """Initializes the LLM provider from environment variables."""
        self.logger.info("LLM Provider plugin initializing...")
        try:
            self.provider = LiteLLMProviderFactory.create_from_env()
            self.logger.info(f"Successfully initialized LLM provider: '{self.provider.provider}' with model '{self.provider.model}'.")

            # Register provider for health monitoring
            health_monitor.register_provider(self.provider.provider, self.provider.model)

        except PluggingerConfigurationError as e:
            self.logger.warning(f"Could not initialize LLM provider: {e}. The plugin will be unavailable.")
            self.provider = None

    # KORREKTUR: Klammern hinzugefügt, um den Dekorator korrekt aufzurufen.
    @service()
    async def generate_structured(self, **kwargs: Any) -> dict[str, Any]:
        """Generates a structured JSON response."""
        if not self.provider:
            raise PluggingerConfigurationError("LLM provider is not configured or available.")

        # Security validation
        system_message = kwargs.get("system_message", "")
        user_message = kwargs.get("user_message", "")
        combined_prompt = f"{system_message}\n{user_message}"

        security_result = security_manager.validate_request(combined_prompt, self.provider.provider)
        if not security_result["is_safe"]:
            return {
                "success": False,
                "error": f"Security validation failed: {', '.join(security_result['issues'])}",
                "provider": self.provider.provider,
                "model": self.provider.model,
                "tokens_used": 0
            }

        result = await self.provider.generate_structured(**kwargs)
        return dict(result)

    # KORREKTUR: Klammern hinzugefügt.
    @service()
    async def generate_text(self, **kwargs: Any) -> dict[str, Any]:
        """Generates a plain text response."""
        if not self.provider:
            raise PluggingerConfigurationError("LLM provider is not configured or available.")

        # Security validation
        prompt = kwargs.get("prompt", "")
        security_result = security_manager.validate_request(prompt, self.provider.provider)
        if not security_result["is_safe"]:
            return {
                "success": False,
                "error": f"Security validation failed: {', '.join(security_result['issues'])}",
                "provider": self.provider.provider,
                "model": self.provider.model,
                "tokens_used": 0
            }

        result = await self.provider.generate_text(**kwargs)
        return dict(result)

    # KORREKTUR: Klammern hinzugefügt.
    @service()
    async def get_provider_info(self) -> dict[str, Any]:
        """Gets information about the current provider."""
        if not self.provider:
            return {"configured": False, "provider": None, "error": "No LLM provider is configured."}

        return {
            "configured": True,
            "provider": self.provider.provider,
            "model": self.provider.model,
            "base_url": self.provider.base_url,
        }

    @service()
    async def validate_response(self, response: str, response_type: str = "text", **kwargs: Any) -> dict[str, Any]:
        """Validates an LLM response."""
        if response_type == "json":
            result = await self.validation_service.validate_json_response(response, **kwargs)
        else:
            result = await self.validation_service.validate_text_response(response, **kwargs)

        return {
            "is_valid": result.is_valid,
            "errors": result.errors,
            "warnings": result.warnings,
            "confidence_score": result.confidence_score,
            "validated_content": result.validated_content
        }
