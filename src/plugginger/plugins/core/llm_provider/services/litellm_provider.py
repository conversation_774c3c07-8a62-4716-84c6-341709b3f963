"""
LiteLLM-based provider implementation for unified LLM access.
This file provides the core, unified interface to over 100 LLM providers.
"""
from __future__ import annotations

import asyncio
import json
import logging
import time
from types import ModuleType
from typing import Any

# from plugginger.ai.types import LLMResponse, StructuredPrompt  # Not needed for LiteLLM implementation
from plugginger.core.exceptions import PluggingerConfigurationError

from .litellm_observability import observability

litellm: ModuleType | None

try:
    import litellm
    LITELLM_AVAILABLE = True
except ImportError:
    LITELLM_AVAILABLE = False
    litellm = None

logger = logging.getLogger(__name__)

class LiteLLMProvider:
    """Unified LLM provider using LiteLLM."""

    def __init__(
        self,
        provider: str,
        model: str,
        api_key: str | None = None,
        base_url: str | None = None,
        **kwargs: Any
    ) -> None:
        if not LITELLM_AVAILABLE:
            raise PluggingerConfigurationError("The 'litellm' library is not installed. Please run: pip install litellm")

        self.provider = provider
        self.model = model
        self.api_key = api_key
        self.base_url = base_url
        self.config = kwargs
        self._configure_litellm()

    def _configure_litellm(self) -> None:
        """Sets global LiteLLM configurations."""
        if litellm:
            litellm.drop_params = True  # type: ignore[attr-defined]
            litellm.set_verbose = False  # type: ignore[attr-defined]

    def _get_litellm_model_string(self) -> str:
        """Formats the model name for LiteLLM API calls."""
        # For major providers, LiteLLM expects just the model name
        if self.provider in ["openai", "azure", "anthropic"]:
            return self.model

        # For Gemini, use direct Google AI Studio API (simpler than vertex_ai)
        if self.provider == "gemini":
            return f"gemini/{self.model}" if not self.model.startswith("gemini/") else self.model

        # For Groq, use groq/ prefix (required by LiteLLM)
        if self.provider == "groq":
            return f"groq/{self.model}" if not self.model.startswith("groq/") else self.model

        # For other providers, use provider/model format
        return f"{self.provider}/{self.model}"

    async def generate_text(self, prompt: str, **kwargs: Any) -> dict[str, Any]:
        """Generates a plain text response from the configured LLM."""
        request_id = observability.start_request(self.provider, self.model, "text")
        start_time = time.time()

        if not litellm:
            raise PluggingerConfigurationError("LiteLLM library is not available.")

        try:
            # Prepare request parameters
            request_params = {
                "model": self._get_litellm_model_string(),
                "messages": [{"role": "user", "content": prompt}],
                **self.config,
                **kwargs
            }

            # Add API key and base URL if provided
            if self.api_key:
                request_params["api_key"] = self.api_key
            if self.base_url:
                request_params["base_url"] = self.base_url

            response = await litellm.acompletion(**request_params)
            content = response.choices[0].message.content or ""
            usage = response.usage

            result: dict[str, Any] = {
                "success": True,
                "content": content,
                "provider": self.provider,
                "model": response.model,
                "tokens_used": usage.total_tokens if usage else 0,
            }
            observability.end_request(request_id, result, start_time, success=True)
            return result

        except Exception as e:
            logger.error(f"LiteLLM text generation failed: {e}")
            error_result: dict[str, Any] = {
                "success": False,
                "error": str(e),
                "provider": self.provider,
                "model": self.model,
                "tokens_used": 0
            }
            observability.end_request(request_id, error_result, start_time, success=False, error_info={"message": str(e)})
            return error_result

    async def generate_structured(self, system_message: str, user_message: str, **kwargs: Any) -> dict[str, Any]:
        """Generates a structured JSON response from the configured LLM."""
        request_id = observability.start_request(self.provider, self.model, "structured")
        start_time = time.time()
        max_retries = kwargs.pop("max_retries", 3)
        last_error = "Max retries reached without valid JSON."

        if not litellm:
            raise PluggingerConfigurationError("LiteLLM library is not available.")

        for attempt in range(max_retries):
            try:
                # Prepare request parameters
                request_params = {
                    "model": self._get_litellm_model_string(),
                    "messages": [
                        {"role": "system", "content": system_message},
                        {"role": "user", "content": user_message}
                    ],
                    **self.config,
                    **kwargs
                }

                # Add API key and base URL if provided
                if self.api_key:
                    request_params["api_key"] = self.api_key
                if self.base_url:
                    request_params["base_url"] = self.base_url

                # Add JSON mode for supported providers
                if self.provider in ["openai", "groq"]:
                    request_params["response_format"] = {"type": "json_object"}
                elif self.provider == "gemini":
                    # Gemini needs explicit JSON instruction in system message
                    if "response_format" not in kwargs:
                        request_params["response_format"] = {"type": "json_object"}

                response = await litellm.acompletion(**request_params)
                content = response.choices[0].message.content or ""

                # Validate JSON
                json.loads(content)
                usage = response.usage

                result: dict[str, Any] = {
                    "success": True,
                    "validated": True,
                    "content": content,
                    "provider": self.provider,
                    "model": response.model,
                    "tokens_used": usage.total_tokens if usage else 0,
                    "retries_used": attempt,
                }
                observability.end_request(request_id, result, start_time, success=True)
                return result

            except json.JSONDecodeError as e:
                last_error = f"Attempt {attempt + 1}: Invalid JSON response. Error: {e}"
                logger.warning(last_error)
                await asyncio.sleep(0.5)
            except Exception as e:
                last_error = f"Attempt {attempt + 1}: API call failed. Error: {e}"
                logger.error(last_error)
                if "authentication" in str(e).lower():
                    break
                await asyncio.sleep(1)

        error_result: dict[str, Any] = {
            "success": False,
            "validated": False,
            "error": last_error,
            "provider": self.provider,
            "model": self.model,
            "tokens_used": 0,
            "retries_used": max_retries,
        }
        observability.end_request(request_id, error_result, start_time, success=False, error_info={"message": last_error})
        return error_result
