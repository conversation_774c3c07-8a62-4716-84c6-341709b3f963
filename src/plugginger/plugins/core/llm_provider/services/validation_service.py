"""
Response validation service for LLM outputs.
"""
from __future__ import annotations

import json
import logging
import re
from collections.abc import Awaitable, Callable
from dataclasses import dataclass
from typing import Any

logger = logging.getLogger(__name__)


@dataclass
class ValidationResult:
    """Result of response validation."""
    is_valid: bool
    errors: list[str]
    warnings: list[str]
    confidence_score: float
    validated_content: Any | None = None


class ResponseValidationService:
    """Service for validating LLM responses."""

    def __init__(self) -> None:
        self.json_validators: dict[str, Any] = {}
        self.custom_validators: dict[str, Callable[..., Awaitable[Any]]] = {}

    async def validate_json_response(
        self,
        response: str,
        schema: dict[str, Any] | None = None,
        strict: bool = True
    ) -> ValidationResult:
        """Validate JSON response from LLM."""
        errors: list[str] = []
        warnings: list[str] = []
        validated_content = None
        confidence_score = 0.0

        # Basic JSON parsing
        try:
            parsed_json = json.loads(response)
            validated_content = parsed_json
            confidence_score += 0.5  # Base score for valid JSON
        except json.JSONDecodeError as e:
            errors.append(f"Invalid JSON format: {str(e)}")
            return ValidationResult(
                is_valid=False,
                errors=errors,
                warnings=warnings,
                confidence_score=0.0
            )

        # Schema validation if provided
        if schema:
            schema_result = await self._validate_against_schema(parsed_json, schema, strict)
            errors.extend(schema_result.errors)
            warnings.extend(schema_result.warnings)
            confidence_score += schema_result.confidence_score * 0.5

        # Content quality checks
        quality_result = self._validate_content_quality(parsed_json)
        warnings.extend(quality_result.warnings)
        confidence_score += quality_result.confidence_score * 0.2

        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            confidence_score=min(confidence_score, 1.0),
            validated_content=validated_content
        )

    async def validate_text_response(
        self,
        response: str,
        expected_format: str | None = None,
        min_length: int = 1,
        max_length: int = 50000
    ) -> ValidationResult:
        """Validate text response from LLM."""
        errors = []
        warnings = []
        confidence_score = 0.0

        # Length validation
        if len(response) < min_length:
            errors.append(f"Response too short: {len(response)} < {min_length}")
        elif len(response) > max_length:
            errors.append(f"Response too long: {len(response)} > {max_length}")
        else:
            confidence_score += 0.3

        # Content validation
        if not response.strip():
            errors.append("Response is empty or contains only whitespace")
        else:
            confidence_score += 0.2

        # Format-specific validation
        if expected_format:
            format_result = self._validate_text_format(response, expected_format)
            errors.extend(format_result.errors)
            warnings.extend(format_result.warnings)
            confidence_score += format_result.confidence_score * 0.5

        # Quality checks
        quality_score = self._calculate_text_quality(response)
        confidence_score += quality_score * 0.3

        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            confidence_score=min(confidence_score, 1.0),
            validated_content=response.strip()
        )

    async def _validate_against_schema(
        self,
        data: Any,
        schema: dict[str, Any],
        strict: bool
    ) -> ValidationResult:
        """Validate data against JSON schema."""
        errors = []
        warnings = []
        confidence_score = 0.0

        try:
            import jsonschema
            jsonschema.validate(instance=data, schema=schema)
            confidence_score = 1.0
        except ImportError:
            # Fallback to basic validation if jsonschema not available
            basic_result = self._basic_schema_validation(data, schema, strict)
            errors.extend(basic_result.errors)
            warnings.extend(basic_result.warnings)
            confidence_score = basic_result.confidence_score
        except jsonschema.ValidationError as e:
            errors.append(f"Schema validation failed: {str(e)}")
        except jsonschema.SchemaError as e:
            errors.append(f"Invalid schema: {str(e)}")

        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            confidence_score=confidence_score
        )

    def _basic_schema_validation(
        self,
        data: Any,
        schema: dict[str, Any],
        strict: bool
    ) -> ValidationResult:
        """Basic schema validation without jsonschema library."""
        errors: list[str] = []
        warnings: list[str] = []
        confidence_score = 0.0

        # Check type
        expected_type = schema.get("type")
        if expected_type:
            if expected_type == "object" and not isinstance(data, dict):
                errors.append(f"Expected object, got {type(data).__name__}")
            elif expected_type == "array" and not isinstance(data, list):
                errors.append(f"Expected array, got {type(data).__name__}")
            elif expected_type == "string" and not isinstance(data, str):
                errors.append(f"Expected string, got {type(data).__name__}")
            elif expected_type == "number" and not isinstance(data, int | float):
                errors.append(f"Expected number, got {type(data).__name__}")
            else:
                confidence_score += 0.3

        # Check required properties for objects
        if isinstance(data, dict) and "required" in schema:
            for required_field in schema["required"]:
                if required_field not in data:
                    errors.append(f"Missing required field: {required_field}")
                else:
                    confidence_score += 0.1

        # Check properties for objects
        if isinstance(data, dict) and "properties" in schema:
            for prop_name, _prop_schema in schema["properties"].items():
                if prop_name in data:
                    # Recursive validation would go here
                    confidence_score += 0.05

        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            confidence_score=min(confidence_score, 1.0)
        )

    def _validate_text_format(self, text: str, expected_format: str) -> ValidationResult:
        """Validate text against expected format."""
        errors = []
        warnings = []
        confidence_score = 0.0

        format_patterns = {
            "email": r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
            "url": r'^https?://[^\s/$.?#].[^\s]*$',
            "phone": r'^\+?[\d\s\-\(\)]{10,}$',
            "code": r'```[\s\S]*```|`[^`]+`',
            "markdown": r'#{1,6}\s+.+|^\*.+|\d+\.\s+.+',
        }

        if expected_format in format_patterns:
            pattern = format_patterns[expected_format]
            if re.search(pattern, text, re.MULTILINE):
                confidence_score = 1.0
            else:
                errors.append(f"Text does not match expected format: {expected_format}")
        else:
            warnings.append(f"Unknown format type: {expected_format}")
            confidence_score = 0.5

        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            confidence_score=confidence_score
        )

    def _validate_content_quality(self, data: Any) -> ValidationResult:
        """Validate content quality for JSON data."""
        warnings = []
        confidence_score = 1.0

        if isinstance(data, dict):
            # Check for empty values
            empty_values = [k for k, v in data.items() if not v and v != 0 and v is not False]
            if empty_values:
                warnings.append(f"Empty values found in fields: {empty_values}")
                confidence_score -= 0.1 * len(empty_values)

            # Check for reasonable field names
            suspicious_keys = [k for k in data.keys() if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', k)]
            if suspicious_keys:
                warnings.append(f"Suspicious field names: {suspicious_keys}")
                confidence_score -= 0.05 * len(suspicious_keys)

        return ValidationResult(
            is_valid=True,
            errors=[],
            warnings=warnings,
            confidence_score=max(confidence_score, 0.0)
        )

    def _calculate_text_quality(self, text: str) -> float:
        """Calculate quality score for text content."""
        score = 1.0

        # Check for reasonable sentence structure
        sentences = re.split(r'[.!?]+', text)
        if len(sentences) < 2:
            score -= 0.2

        # Check for variety in word usage
        words = re.findall(r'\b\w+\b', text.lower())
        if len(words) > 0:
            unique_ratio = len(set(words)) / len(words)
            if unique_ratio < 0.3:
                score -= 0.3

        # Check for reasonable length distribution
        avg_word_length = sum(len(word) for word in words) / len(words) if words else 0
        if avg_word_length < 3 or avg_word_length > 15:
            score -= 0.2

        return max(score, 0.0)

    def register_custom_validator(self, name: str, validator_func: Callable[..., Awaitable[Any]]) -> None:
        """Register a custom validation function."""
        self.custom_validators[name] = validator_func
        logger.info(f"Registered custom validator: {name}")

    async def validate_with_custom(self, response: str, validator_name: str, **kwargs: Any) -> ValidationResult:
        """Validate response using a custom validator."""
        if validator_name not in self.custom_validators:
            return ValidationResult(
                is_valid=False,
                errors=[f"Unknown validator: {validator_name}"],
                warnings=[],
                confidence_score=0.0
            )

        try:
            validator_func = self.custom_validators[validator_name]
            result = await validator_func(response, **kwargs)

            if isinstance(result, ValidationResult):
                return result
            elif isinstance(result, bool):
                return ValidationResult(
                    is_valid=result,
                    errors=[] if result else ["Custom validation failed"],
                    warnings=[],
                    confidence_score=1.0 if result else 0.0
                )
            else:
                return ValidationResult(
                    is_valid=False,
                    errors=["Custom validator returned invalid result type"],
                    warnings=[],
                    confidence_score=0.0
                )
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                errors=[f"Custom validator error: {str(e)}"],
                warnings=[],
                confidence_score=0.0
            )
