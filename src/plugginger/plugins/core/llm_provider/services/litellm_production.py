"""
Production-ready features for LiteLLM: Health monitoring, security, and reliability.
"""
from __future__ import annotations

import asyncio
import hashlib
import logging
import re
import time
from dataclasses import dataclass
from datetime import datetime
from typing import Any

logger = logging.getLogger(__name__)


@dataclass
class ProviderHealth:
    """Health status for a specific provider."""
    provider: str
    model: str
    is_healthy: bool
    last_check: datetime
    consecutive_failures: int
    last_error: str | None = None
    response_time_ms: float | None = None


class ProviderHealthMonitor:
    """Monitor health of LLM providers."""

    def __init__(self, check_interval_seconds: int = 300) -> None:
        self.check_interval = check_interval_seconds
        self.provider_health: dict[str, ProviderHealth] = {}
        self.max_consecutive_failures = 3
        self.health_check_timeout = 10.0
        self._monitoring_task: asyncio.Task[None] | None = None

    async def start_monitoring(self) -> None:
        """Start background health monitoring."""
        if self._monitoring_task is not None:
            logger.warning("Health monitoring already started")
            return

        logger.info("Starting LLM provider health monitoring")
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())

    async def stop_monitoring(self) -> None:
        """Stop background health monitoring."""
        if self._monitoring_task is not None:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
            self._monitoring_task = None
            logger.info("Stopped LLM provider health monitoring")

    async def _monitoring_loop(self) -> None:
        """Background monitoring loop."""
        while True:
            try:
                await self._check_all_providers()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait before retrying

    async def _check_all_providers(self) -> None:
        """Check health of all registered providers."""
        for provider_key in list(self.provider_health.keys()):
            try:
                await self._check_provider_health(provider_key)
            except Exception as e:
                logger.error(f"Error checking health for {provider_key}: {e}")

    async def _check_provider_health(self, provider_key: str) -> None:
        """Check health of a specific provider."""
        # This is a placeholder - in a real implementation, you would
        # make a lightweight test request to the provider
        health = self.provider_health.get(provider_key)
        if health is None:
            return

        # Simulate health check (replace with actual implementation)
        try:
            start_time = time.time()
            # TODO: Implement actual health check request
            await asyncio.sleep(0.1)  # Simulate network request
            response_time = (time.time() - start_time) * 1000

            health.is_healthy = True
            health.consecutive_failures = 0
            health.last_error = None
            health.response_time_ms = response_time
            health.last_check = datetime.now()

        except Exception as e:
            health.consecutive_failures += 1
            health.last_error = str(e)
            health.last_check = datetime.now()

            if health.consecutive_failures >= self.max_consecutive_failures:
                health.is_healthy = False
                logger.warning(
                    f"Provider {health.provider}/{health.model} marked as unhealthy "
                    f"after {health.consecutive_failures} consecutive failures"
                )

    def register_provider(self, provider: str, model: str) -> None:
        """Register a provider for health monitoring."""
        provider_key = f"{provider}/{model}"
        if provider_key not in self.provider_health:
            self.provider_health[provider_key] = ProviderHealth(
                provider=provider,
                model=model,
                is_healthy=True,
                last_check=datetime.now(),
                consecutive_failures=0
            )
            logger.info(f"Registered provider for health monitoring: {provider_key}")

    def is_provider_healthy(self, provider: str, model: str) -> bool:
        """Check if a provider is currently healthy."""
        provider_key = f"{provider}/{model}"
        health = self.provider_health.get(provider_key)
        return health.is_healthy if health else True  # Default to healthy if not monitored

    def get_health_status(self) -> dict[str, Any]:
        """Get overall health status."""
        total_providers = len(self.provider_health)
        healthy_providers = sum(1 for h in self.provider_health.values() if h.is_healthy)

        return {
            "total_providers": total_providers,
            "healthy_providers": healthy_providers,
            "unhealthy_providers": total_providers - healthy_providers,
            "overall_health": "healthy" if healthy_providers == total_providers else "degraded",
            "providers": {
                key: {
                    "provider": health.provider,
                    "model": health.model,
                    "is_healthy": health.is_healthy,
                    "last_check": health.last_check.isoformat(),
                    "consecutive_failures": health.consecutive_failures,
                    "last_error": health.last_error,
                    "response_time_ms": health.response_time_ms
                }
                for key, health in self.provider_health.items()
            }
        }


class SecurityManager:
    """Security features for LLM requests."""

    def __init__(self) -> None:
        self.blocked_patterns: set[str] = set()
        self.rate_limits: dict[str, list[float]] = {}
        self.max_requests_per_minute = 60
        self.max_prompt_length = 50000
        self.blocked_keywords = {
            "system_prompt_injection", "ignore_previous", "disregard_instructions",
            "act_as_different", "pretend_to_be", "jailbreak", "developer_mode"
        }

    def validate_request(self, prompt: str, provider: str, user_id: str | None = None) -> dict[str, Any]:
        """Validate a request for security issues."""
        issues = []

        # Check prompt length
        if len(prompt) > self.max_prompt_length:
            issues.append(f"Prompt too long: {len(prompt)} > {self.max_prompt_length}")

        # Check for blocked keywords
        prompt_lower = prompt.lower()
        for keyword in self.blocked_keywords:
            if keyword in prompt_lower:
                issues.append(f"Blocked keyword detected: {keyword}")

        # Check rate limits
        if user_id:
            rate_limit_issue = self._check_rate_limit(user_id)
            if rate_limit_issue:
                issues.append(rate_limit_issue)

        # Check for potential injection patterns
        injection_patterns = [
            r"ignore\s+(?:all\s+)?(?:previous|above|prior)\s+(?:instructions|prompts?|commands?)",
            r"act\s+as\s+(?:if\s+you\s+are\s+)?(?:a\s+)?different",
            r"pretend\s+(?:to\s+be\s+|you\s+are\s+)",
            r"developer\s+mode",
            r"jailbreak\s+mode"
        ]

        for pattern in injection_patterns:
            if re.search(pattern, prompt_lower, re.IGNORECASE):
                issues.append(f"Potential prompt injection detected: {pattern}")

        return {
            "is_safe": len(issues) == 0,
            "issues": issues,
            "prompt_hash": hashlib.sha256(prompt.encode()).hexdigest()[:16]
        }

    def _check_rate_limit(self, user_id: str) -> str | None:
        """Check if user has exceeded rate limits."""
        now = time.time()
        minute_ago = now - 60

        # Clean old requests
        if user_id in self.rate_limits:
            self.rate_limits[user_id] = [
                req_time for req_time in self.rate_limits[user_id]
                if req_time > minute_ago
            ]
        else:
            self.rate_limits[user_id] = []

        # Check current rate
        current_requests = len(self.rate_limits[user_id])
        if current_requests >= self.max_requests_per_minute:
            return f"Rate limit exceeded: {current_requests}/{self.max_requests_per_minute} requests per minute"

        # Record this request
        self.rate_limits[user_id].append(now)
        return None

    def sanitize_response(self, response: str) -> str:
        """Sanitize LLM response for potential security issues."""
        # Remove potential sensitive information patterns
        sanitized = response

        # Remove potential API keys or tokens
        api_key_pattern = r'\b[A-Za-z0-9]{20,}\b'
        sanitized = re.sub(api_key_pattern, '[REDACTED]', sanitized)

        # Remove potential email addresses in certain contexts
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        # Only redact if it looks like it might be sensitive
        if 'password' in response.lower() or 'secret' in response.lower():
            sanitized = re.sub(email_pattern, '[EMAIL_REDACTED]', sanitized)

        return sanitized


# Global instances
health_monitor = ProviderHealthMonitor()
security_manager = SecurityManager()
