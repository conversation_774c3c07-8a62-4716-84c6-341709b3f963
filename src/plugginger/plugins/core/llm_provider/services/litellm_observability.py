"""
LiteLLM Observability Service for monitoring and logging LLM requests.
"""
from __future__ import annotations

import logging
import time
import uuid
from dataclasses import dataclass
from datetime import datetime
from typing import Any

logger = logging.getLogger(__name__)


@dataclass
class RequestMetrics:
    """Metrics for a single LLM request."""
    request_id: str
    provider: str
    model: str
    request_type: str  # "text" or "structured"
    start_time: float
    end_time: float | None = None
    success: bool = False
    tokens_used: int = 0
    error_info: dict[str, Any] | None = None
    response_size: int = 0
    retries_used: int = 0


class LiteLLMObservability:
    """Observability service for LiteLLM requests."""

    def __init__(self) -> None:
        self.active_requests: dict[str, RequestMetrics] = {}
        self.completed_requests: list[RequestMetrics] = []
        self.max_completed_requests = 1000  # Keep last 1000 requests in memory

    def start_request(self, provider: str, model: str, request_type: str) -> str:
        """Start tracking a new LLM request."""
        request_id = str(uuid.uuid4())
        metrics = RequestMetrics(
            request_id=request_id,
            provider=provider,
            model=model,
            request_type=request_type,
            start_time=time.time()
        )
        self.active_requests[request_id] = metrics

        logger.debug(f"Started tracking request {request_id} for {provider}/{model}")
        return request_id

    def end_request(
        self,
        request_id: str,
        result: dict[str, Any],
        start_time: float,
        success: bool = True,
        error_info: dict[str, Any] | None = None
    ) -> None:
        """End tracking of an LLM request."""
        if request_id not in self.active_requests:
            logger.warning(f"Request {request_id} not found in active requests")
            return

        metrics = self.active_requests.pop(request_id)
        metrics.end_time = time.time()
        metrics.success = success
        metrics.tokens_used = result.get("tokens_used", 0)
        metrics.error_info = error_info
        metrics.response_size = len(str(result.get("content", "")))
        metrics.retries_used = result.get("retries_used", 0)

        # Add to completed requests
        self.completed_requests.append(metrics)

        # Keep only the last N requests
        if len(self.completed_requests) > self.max_completed_requests:
            self.completed_requests = self.completed_requests[-self.max_completed_requests:]

        duration = metrics.end_time - metrics.start_time
        logger.info(
            f"Request {request_id} completed: "
            f"provider={metrics.provider}, model={metrics.model}, "
            f"success={success}, duration={duration:.2f}s, "
            f"tokens={metrics.tokens_used}"
        )

    def get_metrics_summary(self) -> dict[str, Any]:
        """Get summary of all tracked requests."""
        all_requests = list(self.completed_requests)

        if not all_requests:
            return {
                "total_requests": 0,
                "success_rate": 0.0,
                "average_duration": 0.0,
                "total_tokens": 0,
                "providers": {},
                "models": {}
            }

        successful_requests = [r for r in all_requests if r.success]
        total_requests = len(all_requests)
        success_rate = len(successful_requests) / total_requests if total_requests > 0 else 0.0

        # Calculate average duration for successful requests
        successful_durations = [
            r.end_time - r.start_time for r in successful_requests
            if r.end_time is not None
        ]
        avg_duration = sum(successful_durations) / len(successful_durations) if successful_durations else 0.0

        # Provider and model statistics
        provider_stats: dict[str, int] = {}
        model_stats: dict[str, int] = {}
        total_tokens = 0

        for request in all_requests:
            provider_stats[request.provider] = provider_stats.get(request.provider, 0) + 1
            model_stats[request.model] = model_stats.get(request.model, 0) + 1
            total_tokens += request.tokens_used

        return {
            "total_requests": total_requests,
            "successful_requests": len(successful_requests),
            "failed_requests": total_requests - len(successful_requests),
            "success_rate": success_rate,
            "average_duration": avg_duration,
            "total_tokens": total_tokens,
            "providers": provider_stats,
            "models": model_stats,
            "last_updated": datetime.now().isoformat()
        }

    def get_recent_errors(self, limit: int = 10) -> list[dict[str, Any]]:
        """Get recent failed requests with error information."""
        failed_requests = [
            r for r in self.completed_requests
            if not r.success and r.error_info is not None
        ]

        # Sort by end_time (most recent first)
        failed_requests.sort(key=lambda x: x.end_time or 0, reverse=True)

        return [
            {
                "request_id": r.request_id,
                "provider": r.provider,
                "model": r.model,
                "request_type": r.request_type,
                "error": r.error_info,
                "timestamp": datetime.fromtimestamp(r.end_time or 0).isoformat() if r.end_time else None,
                "duration": (r.end_time - r.start_time) if r.end_time else None
            }
            for r in failed_requests[:limit]
        ]

    def clear_metrics(self) -> None:
        """Clear all stored metrics."""
        self.active_requests.clear()
        self.completed_requests.clear()
        logger.info("Cleared all observability metrics")


# Global observability instance
observability = LiteLLMObservability()
