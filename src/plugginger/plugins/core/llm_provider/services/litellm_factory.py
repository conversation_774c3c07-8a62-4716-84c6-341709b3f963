"""
LiteLLM Provider Factory with intelligent auto-detection.
"""
from __future__ import annotations

import logging
import os
from typing import Any

from plugginger.core.exceptions import PluggingerConfigurationError

from .litellm_provider import LiteLLMProvider

logger = logging.getLogger(__name__)

class LiteLLMProviderFactory:
    """Factory for creating LiteLLM providers with auto-detection."""

    PROVIDER_CONFIGS: dict[str, dict[str, Any]] = {
        "openai": {"api_key": "OPENAI_API_KEY", "default_model": "gpt-4o-mini"},
        "gemini": {"api_key": "GOOGLE_API_KEY", "default_model": "gemini-1.5-flash"},
        "groq": {"api_key": "GROQ_API_KEY", "default_model": "llama3-8b-8192"},
        "ollama": {"api_key": None, "default_model": "qwen2.5-coder:7b", "base_url": "http://localhost:11434"},
    }
    PROVIDER_DETECTION_ORDER: list[str] = ["openai", "gemini", "groq", "ollama"]

    @classmethod
    def create_from_env(cls) -> LiteLLMProvider:
        """Creates a provider by auto-detecting configuration from environment variables."""
        provider_name_env = os.getenv("PLUGGINGER_LLM_PROVIDER")
        if provider_name_env:
            provider_name = provider_name_env.lower()
            if provider_name not in cls.PROVIDER_CONFIGS:
                raise PluggingerConfigurationError(f"Unsupported provider specified: '{provider_name}'")

            config = cls.PROVIDER_CONFIGS[provider_name]
            api_key = os.getenv("PLUGGINGER_LLM_API_KEY")
            model = os.getenv("PLUGGINGER_LLM_MODEL", config.get("default_model"))
            base_url = os.getenv("PLUGGINGER_LLM_BASE_URL")

            if config.get("api_key") and not api_key:
                raise PluggingerConfigurationError(f"PLUGGINGER_LLM_API_KEY is required for provider '{provider_name}'.")

            logger.info(f"Creating LLM provider '{provider_name}' from PLUGGINGER_LLM env vars.")
            return LiteLLMProvider(provider=provider_name, model=str(model), api_key=api_key, base_url=base_url)

        for name in cls.PROVIDER_DETECTION_ORDER:
            config = cls.PROVIDER_CONFIGS[name]
            api_key_env_var = config.get("api_key")

            if api_key_env_var and (api_key := os.getenv(api_key_env_var)):
                logger.info(f"Auto-detected provider '{name}' via env var '{api_key_env_var}'.")
                return LiteLLMProvider(provider=name, model=str(config.get("default_model")), api_key=api_key)

        if "ollama" in cls.PROVIDER_DETECTION_ORDER:
             logger.info("No remote provider keys found. Defaulting to Ollama.")
             ollama_config = cls.PROVIDER_CONFIGS["ollama"]
             return LiteLLMProvider(
                 provider="ollama",
                 model=str(ollama_config.get("default_model")),
                 base_url=os.getenv("OLLAMA_BASE_URL", ollama_config.get("base_url"))
             )

        raise PluggingerConfigurationError("Could not auto-detect any LLM provider. Please set an API key environment variable (e.g., OPENAI_API_KEY).")
