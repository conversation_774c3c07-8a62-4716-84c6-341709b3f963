name: json_validator
version: 1.0.0
description: JSON schema validation with retry logic and EBNF grammar support
author: Plugginger Framework
license: MIT
plugin_type: core
execution_mode: thread
services:
  - name: validate_json
    description: Validate JSON data against schema with retry logic
    timeout_seconds: 30.0
  - name: validate_with_ebnf
    description: Validate JSON using EBNF grammar constraints
    timeout_seconds: 30.0
  - name: create_schema
    description: Create JSON schema from example data
    timeout_seconds: 30.0
  - name: validate_batch
    description: Validate multiple JSON strings against a schema
    timeout_seconds: 60.0
dependencies: []
metadata:
  tags:
    - json
    - validation
    - schema
    - ebnf
    - retry
  category: core
  stability: stable
  created_at: "2025-01-27T16:00:00Z"
  updated_at: "2025-01-27T16:00:00Z"
