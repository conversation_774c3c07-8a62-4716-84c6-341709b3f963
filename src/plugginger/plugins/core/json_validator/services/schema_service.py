"""
JSON Schema Generation Service.

Provides functionality to generate JSON schemas from example data.
"""

import json
import logging
from typing import Any

logger = logging.getLogger(__name__)


class SchemaService:
    """Service for JSON schema generation and manipulation."""

    def __init__(self) -> None:
        """Initialize schema service."""
        self.logger = logger

    async def generate_schema(
        self,
        example_data: str,
        strict: bool = True,
        allow_additional_properties: bool = False
    ) -> dict[str, Any]:
        """Generate JSON schema from example data.

        Args:
            example_data: Example JSON string
            strict: Generate strict schema with required fields
            allow_additional_properties: Allow additional properties in objects

        Returns:
            Generated JSON schema
        """
        self.logger.debug(f"Generating schema from example data, strict={strict}")

        try:
            parsed_data = json.loads(example_data)
            schema = self._generate_schema_recursive(parsed_data, strict, allow_additional_properties)

            # Add schema metadata
            schema["$schema"] = "http://json-schema.org/draft-07/schema#"

            self.logger.debug("Schema generation completed successfully")
            return schema

        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in example data: {e}") from e

    def _generate_schema_recursive(
        self,
        data: Any,
        strict: bool,
        allow_additional_properties: bool
    ) -> dict[str, Any]:
        """Recursively generate schema from data.

        Args:
            data: Data to generate schema from
            strict: Generate strict schema
            allow_additional_properties: Allow additional properties

        Returns:
            Schema for the data
        """
        if data is None:
            return {"type": "null"}

        elif isinstance(data, bool):
            return {"type": "boolean"}

        elif isinstance(data, int):
            return {"type": "integer"}

        elif isinstance(data, float):
            return {"type": "number"}

        elif isinstance(data, str):
            return self._generate_string_schema(data)

        elif isinstance(data, list):
            return self._generate_array_schema(data, strict, allow_additional_properties)

        elif isinstance(data, dict):
            return self._generate_object_schema(data, strict, allow_additional_properties)

        else:
            # Fallback for unknown types
            return {"type": "string", "description": f"Unknown type: {type(data).__name__}"}

    def _generate_string_schema(self, value: str) -> dict[str, Any]:
        """Generate schema for string value with format detection.

        Args:
            value: String value to analyze

        Returns:
            String schema with potential format
        """
        schema: dict[str, Any] = {"type": "string"}

        # Detect common formats
        if "@" in value and "." in value:
            # Simple email detection
            schema["format"] = "email"
        elif value.startswith(("http://", "https://")):
            schema["format"] = "uri"
        elif self._is_date_time(value):
            schema["format"] = "date-time"
        elif self._is_date(value):
            schema["format"] = "date"
        elif self._is_uuid(value):
            schema["format"] = "uuid"

        # Add length constraints based on example
        if len(value) > 0:
            schema["minLength"] = 1
            schema["maxLength"] = max(len(value) * 2, 100)  # Reasonable upper bound

        return schema

    def _generate_array_schema(
        self,
        data: list[Any],
        strict: bool,
        allow_additional_properties: bool
    ) -> dict[str, Any]:
        """Generate schema for array data.

        Args:
            data: Array data
            strict: Generate strict schema
            allow_additional_properties: Allow additional properties

        Returns:
            Array schema
        """
        schema: dict[str, Any] = {"type": "array"}

        if not data:
            # Empty array - allow any items
            schema["items"] = {}
        else:
            # Analyze items to determine schema
            item_schemas: list[dict[str, Any]] = []
            for item in data:
                item_schema = self._generate_schema_recursive(item, strict, allow_additional_properties)
                item_schemas.append(item_schema)

            # Try to find common schema for all items
            if len({json.dumps(s, sort_keys=True) for s in item_schemas}) == 1:
                # All items have the same schema
                schema["items"] = item_schemas[0]
            else:
                # Mixed types - use anyOf
                unique_schemas: list[dict[str, Any]] = []
                seen: set[str] = set()
                for item_schema in item_schemas:
                    schema_str = json.dumps(item_schema, sort_keys=True)
                    if schema_str not in seen:
                        unique_schemas.append(item_schema)
                        seen.add(schema_str)

                if len(unique_schemas) == 1:
                    schema["items"] = unique_schemas[0]
                else:
                    schema["items"] = {"anyOf": unique_schemas}

        # Add array constraints
        if strict and data:
            schema["minItems"] = 1
            schema["maxItems"] = max(len(data) * 2, 100)  # Reasonable upper bound

        return schema

    def _generate_object_schema(
        self,
        data: dict[str, Any],
        strict: bool,
        allow_additional_properties: bool
    ) -> dict[str, Any]:
        """Generate schema for object data.

        Args:
            data: Object data
            strict: Generate strict schema
            allow_additional_properties: Allow additional properties

        Returns:
            Object schema
        """
        schema: dict[str, Any] = {
            "type": "object",
            "properties": {},
            "additionalProperties": allow_additional_properties
        }

        # Generate properties
        for key, value in data.items():
            schema["properties"][key] = self._generate_schema_recursive(
                value, strict, allow_additional_properties
            )

        # Add required fields if strict
        if strict and data:
            schema["required"] = list(data.keys())

        return schema

    def _is_date_time(self, value: str) -> bool:
        """Check if string looks like a datetime."""
        import re
        # Simple ISO 8601 datetime pattern
        pattern = r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?(Z|[+-]\d{2}:\d{2})$'
        return bool(re.match(pattern, value))

    def _is_date(self, value: str) -> bool:
        """Check if string looks like a date."""
        import re
        # Simple date pattern
        pattern = r'^\d{4}-\d{2}-\d{2}$'
        return bool(re.match(pattern, value))

    def _is_uuid(self, value: str) -> bool:
        """Check if string looks like a UUID."""
        import re
        # UUID pattern
        pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
        return bool(re.match(pattern, value.lower()))

    async def merge_schemas(
        self,
        schemas: list[dict[str, Any]]
    ) -> dict[str, Any]:
        """Merge multiple schemas into one.

        Args:
            schemas: List of schemas to merge

        Returns:
            Merged schema
        """
        if not schemas:
            return {}

        if len(schemas) == 1:
            return schemas[0]

        # Start with first schema
        merged = schemas[0].copy()

        # Merge with remaining schemas
        for schema in schemas[1:]:
            merged = self._merge_two_schemas(merged, schema)

        return merged

    def _merge_two_schemas(
        self,
        schema1: dict[str, Any],
        schema2: dict[str, Any]
    ) -> dict[str, Any]:
        """Merge two schemas.

        Args:
            schema1: First schema
            schema2: Second schema

        Returns:
            Merged schema
        """
        # Simple merge strategy - use anyOf for different types
        if schema1.get("type") != schema2.get("type"):
            return {"anyOf": [schema1, schema2]}

        # Same type - merge properties
        merged = schema1.copy()

        if schema1.get("type") == "object":
            # Merge object properties
            properties1 = schema1.get("properties", {})
            properties2 = schema2.get("properties", {})

            merged_properties = properties1.copy()
            for key, prop_schema in properties2.items():
                if key in merged_properties:
                    merged_properties[key] = self._merge_two_schemas(
                        merged_properties[key], prop_schema
                    )
                else:
                    merged_properties[key] = prop_schema

            merged["properties"] = merged_properties

            # Merge required fields (intersection for strict merging)
            required1 = set(schema1.get("required", []))
            required2 = set(schema2.get("required", []))
            common_required = required1.intersection(required2)
            if common_required:
                merged["required"] = list(common_required)

        return merged
