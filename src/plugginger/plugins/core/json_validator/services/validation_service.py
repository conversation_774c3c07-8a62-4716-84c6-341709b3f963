"""
JSON Validation Service.

Provides core JSON validation functionality with retry logic and schema validation.
"""

import json
import logging
from typing import Any

import jsonschema
from jsonschema import Draft7Validator

logger = logging.getLogger(__name__)


class ValidationService:
    """Service for JSON schema validation with retry logic."""

    def __init__(self) -> None:
        """Initialize validation service."""
        self.logger = logger

    async def validate_with_retry(
        self,
        data: str,
        schema: dict[str, Any],
        retry_count: int = 3,
        strict: bool = True,
        allow_additional_properties: bool = False
    ) -> dict[str, Any]:
        """Validate JSON data against schema with retry logic.

        Args:
            data: JSON string to validate
            schema: JSON schema dictionary
            retry_count: Number of retry attempts
            strict: Enable strict validation
            allow_additional_properties: Allow additional properties in objects

        Returns:
            Validation result dictionary
        """
        self.logger.debug(f"Starting validation with {retry_count} retries")

        # Prepare schema
        validation_schema = self._prepare_schema(schema, strict, allow_additional_properties)

        last_error = None

        for attempt in range(retry_count):
            try:
                result = await self._validate_single_attempt(data, validation_schema)

                if result["valid"]:
                    self.logger.debug(f"Validation successful on attempt {attempt + 1}")
                    return result
                else:
                    self.logger.debug(f"Validation failed on attempt {attempt + 1}: {result['errors']}")
                    last_error = result

            except Exception as e:
                self.logger.debug(f"Validation error on attempt {attempt + 1}: {e}")
                last_error = {
                    "valid": False,
                    "errors": [str(e)],
                    "attempt": attempt + 1
                }

        # All attempts failed
        self.logger.warning(f"Validation failed after {retry_count} attempts")
        return last_error or {
            "valid": False,
            "errors": ["Validation failed after all retry attempts"],
            "attempts": retry_count
        }

    async def _validate_single_attempt(
        self,
        data: str,
        schema: dict[str, Any]
    ) -> dict[str, Any]:
        """Perform a single validation attempt.

        Args:
            data: JSON string to validate
            schema: Prepared JSON schema

        Returns:
            Validation result for this attempt
        """


        try:
            # Parse JSON data
            parsed_data = json.loads(data)

            # Create validator
            validator = Draft7Validator(schema)

            # Validate
            errors = list(validator.iter_errors(parsed_data))

            if not errors:
                return {
                    "valid": True,
                    "data": parsed_data,
                    "schema": schema
                }
            else:
                error_messages = [
                    f"Path '{'.'.join(str(p) for p in error.absolute_path)}': {error.message}"
                    for error in errors
                ]

                return {
                    "valid": False,
                    "errors": error_messages,
                    "data": parsed_data,
                    "schema": schema,
                    "error_count": len(errors)
                }

        except json.JSONDecodeError as e:
            return {
                "valid": False,
                "errors": [f"Invalid JSON: {e}"],
                "raw_data": data
            }

        except jsonschema.SchemaError as e:
            return {
                "valid": False,
                "errors": [f"Invalid schema: {e}"],
                "schema": schema
            }

    def _prepare_schema(
        self,
        schema: dict[str, Any],
        strict: bool,
        allow_additional_properties: bool
    ) -> dict[str, Any]:
        """Prepare schema with validation options.

        Args:
            schema: Original JSON schema
            strict: Enable strict validation
            allow_additional_properties: Allow additional properties

        Returns:
            Prepared schema with validation options applied
        """
        prepared_schema = schema.copy()

        if strict and not allow_additional_properties:
            # Recursively set additionalProperties to False for all objects
            self._set_additional_properties_recursive(prepared_schema, False)

        return prepared_schema

    def _set_additional_properties_recursive(
        self,
        schema_part: Any,
        allow_additional: bool
    ) -> None:
        """Recursively set additionalProperties in schema.

        Args:
            schema_part: Part of schema to process
            allow_additional: Whether to allow additional properties
        """
        if isinstance(schema_part, dict):
            # If this is an object type, set additionalProperties
            if schema_part.get("type") == "object":
                if "additionalProperties" not in schema_part:
                    schema_part["additionalProperties"] = allow_additional

            # Recursively process all values
            for value in schema_part.values():
                self._set_additional_properties_recursive(value, allow_additional)

        elif isinstance(schema_part, list):
            # Recursively process list items
            for item in schema_part:
                self._set_additional_properties_recursive(item, allow_additional)

    async def validate_format(
        self,
        data: str,
        expected_format: str
    ) -> dict[str, Any]:
        """Validate JSON data format.

        Args:
            data: JSON string to validate
            expected_format: Expected format (e.g., 'email', 'uri', 'date-time')

        Returns:
            Format validation result
        """
        try:
            parsed_data = json.loads(data)

            # Create a simple schema with format validation
            schema = {
                "type": "string",
                "format": expected_format
            }

            validator = Draft7Validator(schema)
            errors = list(validator.iter_errors(parsed_data))

            return {
                "valid": len(errors) == 0,
                "format": expected_format,
                "data": parsed_data,
                "errors": [error.message for error in errors] if errors else []
            }

        except json.JSONDecodeError as e:
            return {
                "valid": False,
                "format": expected_format,
                "errors": [f"Invalid JSON: {e}"]
            }

    async def validate_type(
        self,
        data: str,
        expected_type: str
    ) -> dict[str, Any]:
        """Validate JSON data type.

        Args:
            data: JSON string to validate
            expected_type: Expected JSON type (string, number, object, array, boolean, null)

        Returns:
            Type validation result
        """
        try:
            parsed_data = json.loads(data)

            # Create a simple schema with type validation
            schema = {
                "type": expected_type
            }

            validator = Draft7Validator(schema)
            errors = list(validator.iter_errors(parsed_data))

            return {
                "valid": len(errors) == 0,
                "expected_type": expected_type,
                "actual_type": type(parsed_data).__name__,
                "data": parsed_data,
                "errors": [error.message for error in errors] if errors else []
            }

        except json.JSONDecodeError as e:
            return {
                "valid": False,
                "expected_type": expected_type,
                "errors": [f"Invalid JSON: {e}"]
            }
