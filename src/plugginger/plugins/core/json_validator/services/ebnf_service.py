"""
EBNF Grammar Validation Service.

Provides JSON validation using Extended Backus-Naur Form (EBNF) grammars.
This is particularly useful for LLM-generated JSON validation.
"""

import json
import logging
import re
from typing import Any

logger = logging.getLogger(__name__)


class EbnfService:
    """Service for EBNF grammar-based JSON validation."""

    def __init__(self) -> None:
        """Initialize EBNF service."""
        self.logger = logger

    async def validate_with_grammar(
        self,
        data: str,
        grammar: str,
        retry_count: int = 3
    ) -> dict[str, Any]:
        """Validate JSON using EBNF grammar with retry logic.

        Args:
            data: JSON string to validate
            grammar: EBNF grammar string
            retry_count: Number of retry attempts

        Returns:
            Validation result dictionary
        """
        self.logger.debug(f"Starting EBNF validation with {retry_count} retries")

        last_error = None

        for attempt in range(retry_count):
            try:
                result = await self._validate_single_attempt(data, grammar)

                if result["valid"]:
                    self.logger.debug(f"EBNF validation successful on attempt {attempt + 1}")
                    return result
                else:
                    self.logger.debug(f"EBNF validation failed on attempt {attempt + 1}: {result['errors']}")
                    last_error = result

            except Exception as e:
                self.logger.debug(f"EBNF validation error on attempt {attempt + 1}: {e}")
                last_error = {
                    "valid": False,
                    "errors": [str(e)],
                    "attempt": attempt + 1
                }

        # All attempts failed
        self.logger.warning(f"EBNF validation failed after {retry_count} attempts")
        return last_error or {
            "valid": False,
            "errors": ["EBNF validation failed after all retry attempts"],
            "attempts": retry_count
        }

    async def _validate_single_attempt(
        self,
        data: str,
        grammar: str
    ) -> dict[str, Any]:
        """Perform a single EBNF validation attempt.

        Args:
            data: JSON string to validate
            grammar: EBNF grammar string

        Returns:
            Validation result for this attempt
        """
        try:
            # Parse JSON data first
            parsed_data = json.loads(data)

            # Convert EBNF grammar to validation rules
            validation_rules = self._parse_ebnf_grammar(grammar)

            # Validate against rules
            validation_result = self._validate_against_rules(parsed_data, validation_rules)

            if validation_result["valid"]:
                return {
                    "valid": True,
                    "data": parsed_data,
                    "grammar": grammar,
                    "matched_rules": validation_result.get("matched_rules", [])
                }
            else:
                return {
                    "valid": False,
                    "errors": validation_result["errors"],
                    "data": parsed_data,
                    "grammar": grammar
                }

        except json.JSONDecodeError as e:
            return {
                "valid": False,
                "errors": [f"Invalid JSON: {e}"],
                "raw_data": data
            }

        except Exception as e:
            return {
                "valid": False,
                "errors": [f"EBNF validation error: {e}"],
                "grammar": grammar
            }

    def _parse_ebnf_grammar(self, grammar: str) -> dict[str, Any]:
        """Parse EBNF grammar into validation rules.

        Args:
            grammar: EBNF grammar string

        Returns:
            Parsed validation rules
        """
        # This is a simplified EBNF parser for common JSON patterns
        # In a production system, you'd want a full EBNF parser

        rules = {}

        # Split grammar into individual rules
        rule_lines = [line.strip() for line in grammar.split('\n') if line.strip()]

        for line in rule_lines:
            if '::=' in line:
                rule_name, rule_definition = line.split('::=', 1)
                rule_name = rule_name.strip()
                rule_definition = rule_definition.strip()

                rules[rule_name] = self._parse_rule_definition(rule_definition)

        return rules

    def _parse_rule_definition(self, definition: str) -> dict[str, Any]:
        """Parse a single rule definition.

        Args:
            definition: Rule definition string

        Returns:
            Parsed rule structure
        """
        definition = definition.strip()

        # Handle simple patterns
        if definition.startswith('"') and definition.endswith('"'):
            # Literal string
            return {
                "type": "literal",
                "value": definition[1:-1]  # Remove quotes
            }

        elif definition.startswith("'") and definition.endswith("'"):
            # Literal string (single quotes)
            return {
                "type": "literal",
                "value": definition[1:-1]  # Remove quotes
            }

        elif '|' in definition:
            # Choice (alternatives)
            alternatives = [alt.strip() for alt in definition.split('|')]
            return {
                "type": "choice",
                "alternatives": [self._parse_rule_definition(alt) for alt in alternatives]
            }

        elif definition.startswith('{') and definition.endswith('}'):
            # Object pattern
            return {
                "type": "object",
                "pattern": definition
            }

        elif definition.startswith('[') and definition.endswith(']'):
            # Array pattern
            return {
                "type": "array",
                "pattern": definition
            }

        else:
            # Reference to another rule or simple pattern
            return {
                "type": "reference",
                "name": definition
            }

    def _validate_against_rules(
        self,
        data: Any,
        rules: dict[str, Any]
    ) -> dict[str, Any]:
        """Validate data against parsed rules.

        Args:
            data: Parsed JSON data
            rules: Parsed validation rules

        Returns:
            Validation result
        """
        matched_rules: list[str] = []

        # Start with the root rule (usually the first one)
        if not rules:
            return {
                "valid": False,
                "errors": ["No validation rules found in grammar"]
            }

        root_rule_name = next(iter(rules.keys()))
        root_rule = rules[root_rule_name]

        try:
            is_valid = self._validate_rule(data, root_rule, rules, matched_rules)

            if is_valid:
                return {
                    "valid": True,
                    "matched_rules": matched_rules
                }
            else:
                return {
                    "valid": False,
                    "errors": [f"Data does not match rule '{root_rule_name}'"]
                }

        except Exception as e:
            return {
                "valid": False,
                "errors": [f"Rule validation error: {e}"]
            }

    def _validate_rule(
        self,
        data: Any,
        rule: dict[str, Any],
        all_rules: dict[str, Any],
        matched_rules: list[str]
    ) -> bool:
        """Validate data against a specific rule.

        Args:
            data: Data to validate
            rule: Rule to validate against
            all_rules: All available rules
            matched_rules: List to track matched rules

        Returns:
            True if data matches rule
        """
        rule_type = rule.get("type")

        if rule_type == "literal":
            # Check if data matches literal value
            expected = rule["value"]
            matches = str(data) == expected
            if matches:
                matched_rules.append(f"literal:{expected}")
            return bool(matches)

        elif rule_type == "choice":
            # Check if data matches any alternative
            alternatives = rule["alternatives"]
            for alt in alternatives:
                if self._validate_rule(data, alt, all_rules, matched_rules):
                    return True
            return False

        elif rule_type == "reference":
            # Look up referenced rule
            ref_name = rule["name"]
            if ref_name in all_rules:
                return self._validate_rule(data, all_rules[ref_name], all_rules, matched_rules)
            else:
                # Unknown reference - try simple pattern matching
                return self._simple_pattern_match(data, ref_name)

        elif rule_type == "object":
            # Validate object pattern
            if not isinstance(data, dict):
                return False
            matched_rules.append("object")
            return True  # Simplified object validation

        elif rule_type == "array":
            # Validate array pattern
            if not isinstance(data, list):
                return False
            matched_rules.append("array")
            return True  # Simplified array validation

        else:
            # Unknown rule type
            return False

    def _simple_pattern_match(self, data: Any, pattern: str) -> bool:
        """Simple pattern matching for common cases.

        Args:
            data: Data to match
            pattern: Pattern name

        Returns:
            True if data matches pattern
        """
        pattern_lower = pattern.lower()

        if pattern_lower in ["string", "str"]:
            return isinstance(data, str)
        elif pattern_lower in ["number", "num", "integer", "int"]:
            return isinstance(data, int | float)
        elif pattern_lower in ["boolean", "bool"]:
            return isinstance(data, bool)
        elif pattern_lower in ["null", "none"]:
            return data is None
        elif pattern_lower in ["object", "dict"]:
            return isinstance(data, dict)
        elif pattern_lower in ["array", "list"]:
            return isinstance(data, list)
        else:
            # Try regex pattern matching for strings
            if isinstance(data, str):
                try:
                    return bool(re.match(pattern, data))
                except re.error:
                    return False
            return False

    async def generate_grammar_from_schema(
        self,
        schema: dict[str, Any]
    ) -> str:
        """Generate EBNF grammar from JSON schema.

        Args:
            schema: JSON schema dictionary

        Returns:
            Generated EBNF grammar string
        """
        # This is a simplified schema-to-EBNF converter
        # In practice, you'd want a more comprehensive implementation

        grammar_lines = []

        def schema_to_ebnf(schema_part: dict[str, Any], rule_name: str = "root") -> str:
            schema_type = schema_part.get("type")

            if schema_type == "object":
                properties = schema_part.get("properties", {})
                if properties:
                    prop_rules = []
                    for prop_name, prop_schema in properties.items():
                        prop_rule = f'"{prop_name}": {schema_to_ebnf(prop_schema, f"{rule_name}_{prop_name}")}'
                        prop_rules.append(prop_rule)
                    return "{" + ", ".join(prop_rules) + "}"
                else:
                    return "{}"

            elif schema_type == "array":
                items_schema = schema_part.get("items", {})
                if items_schema:
                    item_rule = schema_to_ebnf(items_schema, f"{rule_name}_item")
                    return f"[{item_rule}*]"
                else:
                    return "[]"

            elif schema_type == "string":
                return "string"
            elif schema_type == "number":
                return "number"
            elif schema_type == "integer":
                return "integer"
            elif schema_type == "boolean":
                return "boolean"
            elif schema_type == "null":
                return "null"
            else:
                return "any"

        root_grammar = schema_to_ebnf(schema, "root")
        grammar_lines.append(f"root ::= {root_grammar}")

        return "\n".join(grammar_lines)
