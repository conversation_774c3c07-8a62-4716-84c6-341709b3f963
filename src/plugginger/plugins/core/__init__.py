"""
Plugginger Core Plugins.

Core plugins are reusable, user-facing plugins shipped with the Plugginger framework.
These plugins provide common functionality that end users can leverage in their applications.

Available Core Plugins:
- json-validator: JSON schema validation with retry logic
- llm-provider: LLM abstraction for OpenAI, Anthropic, local models
- wiring-analyzer: Application structure analysis and dependency mapping

Usage:
    # Load core plugins in your application
    builder = PluggingerAppBuilder()
    builder.include_core_plugins(["json-validator", "llm-provider"])

    # Use core plugin services
    app = builder.build()
    result = await app.call_service("json_validator.validate", {
        "data": json_string,
        "schema": schema_dict
    })

Core Plugin Guidelines:
- Must be useful for end users
- Must have stable, documented APIs
- Must follow semantic versioning
- Must include comprehensive tests
- Must have user-facing documentation
"""

__all__: list[str] = []
