"""
Plugin Compatibility Validation Service.

Provides validation of plugin compatibility with existing app structure.
"""

import logging
from typing import Any

logger = logging.getLogger(__name__)


class CompatibilityValidationService:
    """Service for validating plugin compatibility with existing apps."""

    def __init__(self) -> None:
        """Initialize validation service."""
        self.logger = logger

    async def validate_plugin_compatibility(
        self,
        plugin_spec: dict[str, Any],
        app_context: dict[str, Any],
        enable_dependency_validation: bool = True,
        enable_event_analysis: bool = True
    ) -> dict[str, Any]:
        """Validate if new plugin is compatible with existing app.

        Args:
            plugin_spec: Specification of the new plugin
            app_context: Context of the existing app
            enable_dependency_validation: Enable dependency cycle validation
            enable_event_analysis: Enable event pattern analysis

        Returns:
            Validation result with errors, warnings, and suggestions
        """
        self.logger.info(f"Validating compatibility for plugin: {plugin_spec.get('name', 'unknown')}")

        errors = []
        warnings = []
        suggestions = []

        # Validate service dependencies
        if enable_dependency_validation:
            service_errors = await self._validate_service_dependencies(plugin_spec, app_context)
            errors.extend(service_errors)

            # Check for dependency cycles
            cycle_errors = await self._check_dependency_cycles(plugin_spec, app_context)
            errors.extend(cycle_errors)

        # Validate event patterns
        if enable_event_analysis:
            event_warnings = await self._validate_event_patterns(plugin_spec, app_context)
            warnings.extend(event_warnings)

        # Generate integration suggestions
        integration_suggestions = await self._generate_integration_suggestions(plugin_spec, app_context)
        suggestions.extend(integration_suggestions)

        return {
            "valid": len(errors) == 0,
            "plugin_name": plugin_spec.get("name", "unknown"),
            "errors": errors,
            "warnings": warnings,
            "suggestions": suggestions,
            "compatibility_score": self._calculate_compatibility_score(errors, warnings, suggestions),
            "validation_metadata": {
                "dependency_validation_enabled": enable_dependency_validation,
                "event_analysis_enabled": enable_event_analysis,
                "validated_at": "2025-01-27T14:00:00Z"
            }
        }

    async def _validate_service_dependencies(
        self,
        plugin_spec: dict[str, Any],
        app_context: dict[str, Any]
    ) -> list[str]:
        """Validate service dependencies against available services.

        Args:
            plugin_spec: Plugin specification to validate
            app_context: App context with available services

        Returns:
            List of validation error messages
        """
        errors = []
        available_services = {svc["name"]: svc for svc in app_context.get("services", [])}
        dependencies = plugin_spec.get("dependencies", [])

        for dependency in dependencies:
            dep_name = dependency.get("name", "")
            is_optional = dependency.get("optional", False)

            # Check if dependency provides required services
            if dep_name not in app_context.get("dependency_graph", {}):
                if not is_optional:
                    errors.append(f"Required dependency '{dep_name}' not found in app")

            # Check if required services are available
            required_services = dependency.get("required_services", [])
            for service_name in required_services:
                if service_name not in available_services:
                    if not is_optional:
                        errors.append(f"Required service '{service_name}' from dependency '{dep_name}' not available")

        return errors

    async def _validate_event_patterns(
        self,
        plugin_spec: dict[str, Any],
        app_context: dict[str, Any]
    ) -> list[str]:
        """Validate event patterns against available events.

        Args:
            plugin_spec: Plugin specification to validate
            app_context: App context with available events

        Returns:
            List of validation warning messages
        """
        warnings = []
        available_events = app_context.get("events", [])
        event_listeners = plugin_spec.get("event_listeners", [])

        for listener in event_listeners:
            pattern = listener.get("event_pattern", "")
            if not pattern:
                continue

            # Check if pattern matches any existing events
            matching_events = [
                event for event in available_events
                if self._pattern_matches_event(pattern, event)
            ]

            if not matching_events:
                warnings.append(f"Event pattern '{pattern}' may not match any existing events")

        return warnings

    def _pattern_matches_event(self, pattern: str, event: str) -> bool:
        """Check if event pattern matches event name.

        Args:
            pattern: Event pattern (may contain wildcards)
            event: Event name

        Returns:
            True if pattern matches event
        """
        # Simple pattern matching - can be enhanced with regex
        if "*" in pattern:
            # Wildcard matching
            pattern_parts = pattern.split("*")
            if len(pattern_parts) == 2:
                prefix, suffix = pattern_parts
                return event.startswith(prefix) and event.endswith(suffix)

        # Exact match or substring match
        return pattern == event or pattern in event or event in pattern

    async def _check_dependency_cycles(
        self,
        plugin_spec: dict[str, Any],
        app_context: dict[str, Any]
    ) -> list[str]:
        """Check for potential dependency cycles.

        Args:
            plugin_spec: Plugin specification to validate
            app_context: App context with dependency graph

        Returns:
            List of cycle error messages
        """
        errors = []
        plugin_name = plugin_spec.get("name", "")
        dependencies = plugin_spec.get("dependencies", [])
        dependency_graph = app_context.get("dependency_graph", {})

        for dependency in dependencies:
            dep_name = dependency.get("name", "")
            if dep_name in dependency_graph:
                # Check if dependency has transitive dependency on this plugin
                if self._has_transitive_dependency(dep_name, plugin_name, dependency_graph):
                    errors.append(
                        f"Dependency cycle detected: {plugin_name} -> {dep_name} -> ... -> {plugin_name}"
                    )

        return errors

    def _has_transitive_dependency(
        self,
        start: str,
        target: str,
        dependency_graph: dict[str, list[str]],
        visited: set[str] | None = None
    ) -> bool:
        """Check if start has transitive dependency on target.

        Args:
            start: Starting node
            target: Target node to find
            dependency_graph: Dependency graph
            visited: Set of visited nodes (for cycle detection)

        Returns:
            True if transitive dependency exists
        """
        if visited is None:
            visited = set()

        # Don't check self-dependency
        if start == target:
            return False

        if start in visited:
            return False  # Cycle detected, but not the target we're looking for

        visited.add(start)

        dependencies = dependency_graph.get(start, [])
        if target in dependencies:
            return True

        for dep in dependencies:
            if self._has_transitive_dependency(dep, target, dependency_graph, visited.copy()):
                return True

        return False

    async def _generate_integration_suggestions(
        self,
        plugin_spec: dict[str, Any],
        app_context: dict[str, Any]
    ) -> list[str]:
        """Generate suggestions for better plugin integration.

        Args:
            plugin_spec: Plugin specification
            app_context: App context

        Returns:
            List of integration suggestions
        """
        suggestions = []
        plugin_services = plugin_spec.get("services", [])
        plugin_events = plugin_spec.get("event_listeners", [])
        app_services = app_context.get("services", [])
        app_events = app_context.get("events", [])

        # Suggest compatible services
        if not plugin_services:
            suggestions.append("Consider adding services to make this plugin more useful")

        # Suggest event integration
        if not plugin_events and app_events:
            common_events = [event for event in app_events if any(
                keyword in event.lower() for keyword in ["user", "auth", "error", "complete"]
            )]
            if common_events:
                suggestions.append(f"Consider listening to events like: {', '.join(common_events[:3])}")

        # Suggest dependencies based on service usage
        if not plugin_spec.get("dependencies") and app_services:
            common_service_types = ["auth", "database", "logging", "email"]
            available_common = []

            for service in app_services:
                service_name = service.get("name", "").lower()
                plugin_name = service.get("plugin", "")
                for service_type in common_service_types:
                    if service_type in service_name and plugin_name not in available_common:
                        available_common.append(plugin_name)

            if available_common:
                suggestions.append(f"Consider depending on: {', '.join(available_common[:2])}")

        return suggestions

    def _calculate_compatibility_score(
        self,
        errors: list[str],
        warnings: list[str],
        suggestions: list[str]
    ) -> float:
        """Calculate compatibility score based on validation results.

        Args:
            errors: List of validation errors
            warnings: List of validation warnings
            suggestions: List of integration suggestions

        Returns:
            Compatibility score (0.0 to 1.0)
        """
        # Start with perfect score
        score = 1.0

        # Deduct for errors (major issues)
        score -= len(errors) * 0.3

        # Deduct for warnings (minor issues)
        score -= len(warnings) * 0.1

        # Bonus for having suggestions (shows integration potential)
        if suggestions:
            score += min(len(suggestions) * 0.05, 0.2)

        # Ensure score is within bounds
        return max(0.0, min(1.0, score))

    async def validate_service_signature_compatibility(
        self,
        plugin_service: dict[str, Any],
        app_services: list[dict[str, Any]]
    ) -> dict[str, Any]:
        """Validate service signature compatibility.

        Args:
            plugin_service: Service from plugin specification
            app_services: Available services in app

        Returns:
            Signature compatibility result
        """
        service_name = plugin_service.get("name", "")
        conflicts = []
        compatible_services = []

        for app_service in app_services:
            app_service_name = app_service.get("name", "")

            # Check for name conflicts
            if service_name == app_service_name:
                conflicts.append({
                    "type": "name_conflict",
                    "message": f"Service name '{service_name}' already exists in app",
                    "existing_plugin": app_service.get("plugin", "unknown"),
                    "existing_signature": app_service.get("signature", "")
                })

            # Check for compatible services (similar functionality)
            elif self._services_are_compatible(plugin_service, app_service):
                compatible_services.append({
                    "service": app_service_name,
                    "plugin": app_service.get("plugin", ""),
                    "compatibility_reason": "Similar functionality detected"
                })

        return {
            "service_name": service_name,
            "has_conflicts": len(conflicts) > 0,
            "conflicts": conflicts,
            "compatible_services": compatible_services,
            "recommendation": self._generate_service_recommendation(conflicts, compatible_services)
        }

    def _services_are_compatible(
        self,
        service1: dict[str, Any],
        service2: dict[str, Any]
    ) -> bool:
        """Check if two services have compatible functionality.

        Args:
            service1: First service specification
            service2: Second service specification

        Returns:
            True if services are compatible
        """
        # Simple compatibility check based on name similarity
        name1 = service1.get("name", "").lower()
        name2 = service2.get("name", "").lower()

        # Check for similar keywords
        keywords1 = set(name1.split("_"))
        keywords2 = set(name2.split("_"))

        # If they share significant keywords, they might be compatible
        common_keywords = keywords1.intersection(keywords2)
        return len(common_keywords) > 0 and len(common_keywords) >= min(len(keywords1), len(keywords2)) * 0.5

    def _generate_service_recommendation(
        self,
        conflicts: list[dict[str, Any]],
        compatible_services: list[dict[str, Any]]
    ) -> str:
        """Generate recommendation for service integration.

        Args:
            conflicts: List of service conflicts
            compatible_services: List of compatible services

        Returns:
            Recommendation string
        """
        if conflicts:
            return "Consider renaming the service to avoid conflicts"
        elif compatible_services:
            return f"Consider using existing service: {compatible_services[0]['service']}"
        else:
            return "Service appears to be unique and safe to add"
