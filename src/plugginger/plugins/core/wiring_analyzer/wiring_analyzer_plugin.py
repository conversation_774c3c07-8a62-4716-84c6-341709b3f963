"""
Wiring Analyzer Core Plugin.

Provides intelligent plugin wiring analysis and integration suggestions.
"""

import logging
from typing import Any

from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service
from plugginger.core.exceptions import PluggingerValidationError
from plugginger.plugins.core.wiring_analyzer.services.analysis_service import AppAnalysisService
from plugginger.plugins.core.wiring_analyzer.services.suggestion_service import (
    WiringSuggestionService,
)
from plugginger.plugins.core.wiring_analyzer.services.validation_service import (
    CompatibilityValidationService,
)

logger = logging.getLogger(__name__)


@plugin(name="wiring_analyzer", version="1.0.0")
class WiringAnalyzerPlugin(PluginBase):
    """Core plugin for intelligent plugin wiring analysis and integration suggestions."""

    def __init__(self, **injected_dependencies: Any) -> None:
        """Initialize wiring analyzer plugin.

        Args:
            **injected_dependencies: Injected dependencies from DI container
        """
        super().__init__(**injected_dependencies)
        self.logger = logger

        # Initialize services
        self.analysis_service = AppAnalysisService()
        self.validation_service = CompatibilityValidationService()
        self.suggestion_service = WiringSuggestionService()

        # Configuration from manifest
        self.analysis_depth = "detailed"
        self.suggestion_confidence_threshold = 0.6
        self.max_suggestions = 10
        self.enable_dependency_validation = True
        self.enable_event_analysis = True

    async def setup(self, plugin_config: Any = None) -> None:
        """Setup the wiring analyzer plugin."""
        self.logger.info("Wiring Analyzer plugin initializing")

        # Load configuration from plugin_config if provided
        if plugin_config:
            if hasattr(plugin_config, 'analysis_depth'):
                self.analysis_depth = plugin_config.analysis_depth
            if hasattr(plugin_config, 'suggestion_confidence_threshold'):
                self.suggestion_confidence_threshold = plugin_config.suggestion_confidence_threshold
            if hasattr(plugin_config, 'max_suggestions'):
                self.max_suggestions = plugin_config.max_suggestions
            if hasattr(plugin_config, 'enable_dependency_validation'):
                self.enable_dependency_validation = plugin_config.enable_dependency_validation
            if hasattr(plugin_config, 'enable_event_analysis'):
                self.enable_event_analysis = plugin_config.enable_event_analysis

        self.logger.info(f"Wiring Analyzer configured: depth={self.analysis_depth}, "
                        f"threshold={self.suggestion_confidence_threshold}")

    async def teardown(self) -> None:
        """Cleanup the wiring analyzer plugin."""
        self.logger.info("Wiring Analyzer plugin shutting down")

    @service(name="analyze_app_context")
    async def analyze_app_context(
        self,
        app_path: str,
        analysis_depth: str | None = None
    ) -> dict[str, Any]:
        """Analyze existing app structure and extract wiring context.

        Args:
            app_path: Path to app factory function (module:function) or manifest.yaml
            analysis_depth: Depth of analysis (basic, detailed, comprehensive)

        Returns:
            App wiring context with analyzed structure

        Example:
            context = await app.call_service("wiring_analyzer.analyze_app_context", {
                "app_path": "myapp.main:create_app",
                "analysis_depth": "detailed"
            })
        """
        analysis_depth = analysis_depth or self.analysis_depth

        self.logger.info(f"Analyzing app context: {app_path}")

        try:
            # Emit start event
            await self.app.emit_event("wiring.analysis.started", {
                "app_path": app_path,
                "analysis_depth": analysis_depth
            })

            result = await self.analysis_service.analyze_app_structure(
                app_path=app_path,
                analysis_depth=analysis_depth
            )

            # Emit completion event
            await self.app.emit_event("wiring.analysis.completed", {
                "app_path": app_path,
                "success": result.get("success", False),
                "plugin_count": len(result.get("plugins", [])),
                "service_count": len(result.get("services", []))
            })

            self.logger.info(f"App analysis {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"App analysis error: {e}")
            raise PluggingerValidationError(f"App analysis failed: {e}") from e

    @service(name="validate_plugin_compatibility")
    async def validate_plugin_compatibility(
        self,
        plugin_spec: dict[str, Any],
        app_context: dict[str, Any]
    ) -> dict[str, Any]:
        """Validate if new plugin is compatible with existing app.

        Args:
            plugin_spec: Specification of the new plugin
            app_context: Context of the existing app

        Returns:
            Validation result with errors, warnings, and suggestions

        Example:
            result = await app.call_service("wiring_analyzer.validate_plugin_compatibility", {
                "plugin_spec": {"name": "my_plugin", "dependencies": [...]},
                "app_context": {"plugins": [...], "services": [...]}
            })
        """
        self.logger.info(f"Validating compatibility for plugin: {plugin_spec.get('name', 'unknown')}")

        try:
            result = await self.validation_service.validate_plugin_compatibility(
                plugin_spec=plugin_spec,
                app_context=app_context,
                enable_dependency_validation=self.enable_dependency_validation,
                enable_event_analysis=self.enable_event_analysis
            )

            # Emit validation event
            await self.app.emit_event("wiring.validation.performed", {
                "plugin_name": plugin_spec.get("name", "unknown"),
                "valid": result.get("valid", False),
                "error_count": len(result.get("errors", [])),
                "warning_count": len(result.get("warnings", []))
            })

            self.logger.info(f"Plugin compatibility validation {'passed' if result.get('valid') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Plugin compatibility validation error: {e}")
            raise PluggingerValidationError(f"Plugin compatibility validation failed: {e}") from e

    @service(name="suggest_wiring_improvements")
    async def suggest_wiring_improvements(
        self,
        plugin_spec: dict[str, Any],
        app_context: dict[str, Any],
        confidence_threshold: float | None = None,
        max_suggestions: int | None = None
    ) -> dict[str, Any]:
        """Suggest improvements for plugin wiring and integration.

        Args:
            plugin_spec: Plugin specification
            app_context: App context
            confidence_threshold: Minimum confidence for suggestions
            max_suggestions: Maximum number of suggestions to return

        Returns:
            Wiring suggestions with confidence scores

        Example:
            suggestions = await app.call_service("wiring_analyzer.suggest_wiring_improvements", {
                "plugin_spec": {"name": "my_plugin", "description": "Email service"},
                "app_context": {"services": [...], "events": [...]}
            })
        """
        confidence_threshold = confidence_threshold or self.suggestion_confidence_threshold
        max_suggestions = max_suggestions or self.max_suggestions

        self.logger.info(f"Generating wiring suggestions for plugin: {plugin_spec.get('name', 'unknown')}")

        try:
            result = await self.suggestion_service.suggest_wiring_improvements(
                plugin_spec=plugin_spec,
                app_context=app_context,
                confidence_threshold=confidence_threshold,
                max_suggestions=max_suggestions
            )

            # Emit suggestions event
            await self.app.emit_event("wiring.suggestions.generated", {
                "plugin_name": plugin_spec.get("name", "unknown"),
                "suggestion_count": len(result.get("suggestions", [])),
                "confidence_threshold": confidence_threshold
            })

            self.logger.info(f"Generated {len(result.get('suggestions', []))} wiring suggestions")
            return result

        except Exception as e:
            self.logger.error(f"Wiring suggestions error: {e}")
            raise PluggingerValidationError(f"Wiring suggestions failed: {e}") from e

    @service(name="generate_integration_code")
    async def generate_integration_code(
        self,
        plugin_spec: dict[str, Any],
        selected_suggestions: list[dict[str, Any]]
    ) -> dict[str, Any]:
        """Generate code snippets for plugin integration.

        Args:
            plugin_spec: Plugin specification
            selected_suggestions: List of selected suggestions to implement

        Returns:
            Generated integration code and instructions

        Example:
            code = await app.call_service("wiring_analyzer.generate_integration_code", {
                "plugin_spec": {"name": "my_plugin"},
                "selected_suggestions": [{"type": "service_integration", ...}]
            })
        """
        self.logger.info(f"Generating integration code for {len(selected_suggestions)} suggestions")

        try:
            result = await self.suggestion_service.generate_integration_code(
                plugin_spec=plugin_spec,
                selected_suggestions=selected_suggestions
            )

            self.logger.info(f"Generated integration code for plugin: {plugin_spec.get('name', 'unknown')}")
            return result

        except Exception as e:
            self.logger.error(f"Integration code generation error: {e}")
            raise PluggingerValidationError(f"Integration code generation failed: {e}") from e

    async def analyze_service_dependencies(
        self,
        plugin_spec: dict[str, Any],
        app_context: dict[str, Any]
    ) -> dict[str, Any]:
        """Analyze service dependencies for a plugin.

        Args:
            plugin_spec: Plugin specification
            app_context: App context

        Returns:
            Service dependency analysis
        """
        plugin_services = plugin_spec.get("services", [])
        app_services = app_context.get("services", [])
        analysis_results = []

        for plugin_service in plugin_services:
            service_analysis = await self.validation_service.validate_service_signature_compatibility(
                plugin_service=plugin_service,
                app_services=app_services
            )
            analysis_results.append(service_analysis)

        return {
            "plugin_name": plugin_spec.get("name", "unknown"),
            "service_analyses": analysis_results,
            "total_services": len(plugin_services),
            "conflict_count": sum(1 for analysis in analysis_results if analysis.get("has_conflicts", False)),
            "compatibility_summary": self._generate_compatibility_summary(analysis_results)
        }

    def _generate_compatibility_summary(self, analyses: list[dict[str, Any]]) -> dict[str, Any]:
        """Generate compatibility summary from service analyses.

        Args:
            analyses: List of service analyses

        Returns:
            Compatibility summary
        """
        total_services = len(analyses)
        conflicted_services = sum(1 for analysis in analyses if analysis.get("has_conflicts", False))
        compatible_services = sum(1 for analysis in analyses if analysis.get("compatible_services", []))

        return {
            "total_services": total_services,
            "conflicted_services": conflicted_services,
            "compatible_services": compatible_services,
            "compatibility_score": (total_services - conflicted_services) / total_services if total_services > 0 else 1.0,
            "recommendations": [
                analysis.get("recommendation", "")
                for analysis in analyses
                if analysis.get("recommendation")
            ]
        }
