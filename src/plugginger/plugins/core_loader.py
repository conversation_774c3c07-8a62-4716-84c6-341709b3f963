"""
Core Plugin Loading Infrastructure.

Provides automatic discovery and loading of core plugins shipped with Plugginger.
Core plugins are user-facing, reusable plugins with stable APIs.
"""

import logging
from pathlib import Path

from plugginger.api.builder import PluggingerAppBuilder
from plugginger.core.exceptions import PluggingerConfigurationError

logger = logging.getLogger(__name__)


class CorePluginLoader:
    """Loader for Plugginger core plugins."""

    def __init__(self) -> None:
        """Initialize core plugin loader."""
        self.core_plugins_path = Path(__file__).parent / "core"
        self.available_plugins: dict[str, Path] = {}
        self._discover_core_plugins()

    def _discover_core_plugins(self) -> None:
        """Discover available core plugins."""
        if not self.core_plugins_path.exists():
            logger.warning(f"Core plugins directory not found: {self.core_plugins_path}")
            return

        for plugin_dir in self.core_plugins_path.iterdir():
            if plugin_dir.is_dir() and (plugin_dir / "manifest.yaml").exists():
                plugin_name = plugin_dir.name
                self.available_plugins[plugin_name] = plugin_dir
                logger.debug(f"Discovered core plugin: {plugin_name}")

        logger.info(f"Discovered {len(self.available_plugins)} core plugins")

    def get_available_plugins(self) -> list[str]:
        """Get list of available core plugin names.

        Returns:
            List of core plugin names
        """
        return list(self.available_plugins.keys())

    def load_core_plugin(self, plugin_name: str, builder: PluggingerAppBuilder) -> None:
        """Load a specific core plugin into the builder.

        Args:
            plugin_name: Name of the core plugin to load
            builder: App builder to load plugin into

        Raises:
            PluggingerConfigurationError: If plugin not found or loading fails
        """
        if plugin_name not in self.available_plugins:
            available = ", ".join(self.available_plugins.keys())
            raise PluggingerConfigurationError(
                f"Core plugin '{plugin_name}' not found. "
                f"Available core plugins: {available}"
            )

        plugin_path = self.available_plugins[plugin_name]

        try:
            # Load plugin using discovery mechanism
            result = builder.discover_and_include_plugins(
                directory=plugin_path,
                recursive=False,
                pattern="manifest.yaml"
            )

            if result.successful_discoveries == 0:
                raise PluggingerConfigurationError(
                    f"No valid plugin found in core plugin directory: {plugin_path}"
                )

            logger.info(f"Loaded core plugin: {plugin_name}")

        except Exception as e:
            raise PluggingerConfigurationError(
                f"Failed to load core plugin '{plugin_name}': {e}"
            ) from e

    def load_core_plugins(self, plugin_names: list[str], builder: PluggingerAppBuilder) -> None:
        """Load multiple core plugins into the builder.

        Args:
            plugin_names: List of core plugin names to load
            builder: App builder to load plugins into

        Raises:
            PluggingerConfigurationError: If any plugin fails to load
        """
        for plugin_name in plugin_names:
            self.load_core_plugin(plugin_name, builder)

    def load_all_core_plugins(self, builder: PluggingerAppBuilder) -> None:
        """Load all available core plugins into the builder.

        Args:
            builder: App builder to load plugins into
        """
        plugin_names = self.get_available_plugins()
        if plugin_names:
            self.load_core_plugins(plugin_names, builder)
            logger.info(f"Loaded all {len(plugin_names)} core plugins")
        else:
            logger.warning("No core plugins available to load")


# Global core plugin loader instance
_core_loader: CorePluginLoader | None = None


def get_core_loader() -> CorePluginLoader:
    """Get the global core plugin loader instance.

    Returns:
        CorePluginLoader instance
    """
    global _core_loader
    if _core_loader is None:
        _core_loader = CorePluginLoader()
    return _core_loader


def load_core_plugins(plugin_names: list[str], builder: PluggingerAppBuilder) -> None:
    """Convenience function to load core plugins.

    Args:
        plugin_names: List of core plugin names to load
        builder: App builder to load plugins into
    """
    loader = get_core_loader()
    loader.load_core_plugins(plugin_names, builder)


def get_available_core_plugins() -> list[str]:
    """Get list of available core plugin names.

    Returns:
        List of core plugin names
    """
    loader = get_core_loader()
    return loader.get_available_plugins()


# Convenience functions for builder integration
def include_core_plugins_in_builder(builder: PluggingerAppBuilder, plugin_names: list[str]) -> PluggingerAppBuilder:
    """Include core plugins in the application builder.

    Args:
        builder: App builder instance
        plugin_names: List of core plugin names to include

    Returns:
        Builder instance for chaining

    Example:
        from plugginger.plugins.core_loader import include_core_plugins_in_builder

        builder = PluggingerAppBuilder("my_app")
        include_core_plugins_in_builder(builder, ["json-validator", "llm-provider"])
    """
    load_core_plugins(plugin_names, builder)
    return builder


def include_all_core_plugins_in_builder(builder: PluggingerAppBuilder) -> PluggingerAppBuilder:
    """Include all available core plugins in the application builder.

    Args:
        builder: App builder instance

    Returns:
        Builder instance for chaining

    Example:
        from plugginger.plugins.core_loader import include_all_core_plugins_in_builder

        builder = PluggingerAppBuilder("my_app")
        include_all_core_plugins_in_builder(builder)
    """
    loader = get_core_loader()
    loader.load_all_core_plugins(builder)
    return builder
