"""
Internal Plugin Loading Infrastructure.

Provides automatic discovery and loading of internal plugins used by the framework.
Internal plugins are framework-specific and not intended for direct user interaction.
"""

import logging
from pathlib import Path

from plugginger.api.builder import PluggingerAppBuilder
from plugginger.core.exceptions import PluggingerConfigurationError

logger = logging.getLogger(__name__)


class InternalPluginLoader:
    """Loader for Plugginger internal plugins."""

    def __init__(self) -> None:
        """Initialize internal plugin loader."""
        self.internal_plugins_path = Path(__file__).parent / "internal"
        self.available_plugins: dict[str, Path] = {}
        self._discover_internal_plugins()

    def _discover_internal_plugins(self) -> None:
        """Discover available internal plugins."""
        if not self.internal_plugins_path.exists():
            logger.warning(f"Internal plugins directory not found: {self.internal_plugins_path}")
            return

        for plugin_dir in self.internal_plugins_path.iterdir():
            if plugin_dir.is_dir() and (plugin_dir / "manifest.yaml").exists():
                plugin_name = plugin_dir.name
                self.available_plugins[plugin_name] = plugin_dir
                logger.debug(f"Discovered internal plugin: {plugin_name}")

        logger.info(f"Discovered {len(self.available_plugins)} internal plugins")

    def get_available_plugins(self) -> list[str]:
        """Get list of available internal plugin names.

        Returns:
            List of internal plugin names
        """
        return list(self.available_plugins.keys())

    def load_internal_plugin(self, plugin_name: str, builder: PluggingerAppBuilder) -> None:
        """Load a specific internal plugin into the builder.

        Args:
            plugin_name: Name of the internal plugin to load
            builder: App builder to load plugin into

        Raises:
            PluggingerConfigurationError: If plugin not found or loading fails
        """
        if plugin_name not in self.available_plugins:
            available = ", ".join(self.available_plugins.keys())
            raise PluggingerConfigurationError(
                f"Internal plugin '{plugin_name}' not found. "
                f"Available internal plugins: {available}"
            )

        plugin_path = self.available_plugins[plugin_name]

        try:
            # Load plugin using discovery mechanism
            result = builder.discover_and_include_plugins(
                directory=plugin_path,
                recursive=False,
                pattern="manifest.yaml"
            )

            if result.successful_discoveries == 0:
                raise PluggingerConfigurationError(
                    f"No valid plugin found in internal plugin directory: {plugin_path}"
                )

            logger.debug(f"Loaded internal plugin: {plugin_name}")

        except Exception as e:
            raise PluggingerConfigurationError(
                f"Failed to load internal plugin '{plugin_name}': {e}"
            ) from e

    def load_internal_plugins(self, plugin_names: list[str], builder: PluggingerAppBuilder) -> None:
        """Load multiple internal plugins into the builder.

        Args:
            plugin_names: List of internal plugin names to load
            builder: App builder to load plugins into

        Raises:
            PluggingerConfigurationError: If any plugin fails to load
        """
        for plugin_name in plugin_names:
            self.load_internal_plugin(plugin_name, builder)

    def load_all_internal_plugins(self, builder: PluggingerAppBuilder) -> None:
        """Load all available internal plugins into the builder.

        Args:
            builder: App builder to load plugins into
        """
        plugin_names = self.get_available_plugins()
        if plugin_names:
            self.load_internal_plugins(plugin_names, builder)
            logger.debug(f"Loaded all {len(plugin_names)} internal plugins")
        else:
            logger.debug("No internal plugins available to load")


# Global internal plugin loader instance
_internal_loader: InternalPluginLoader | None = None


def get_internal_loader() -> InternalPluginLoader:
    """Get the global internal plugin loader instance.

    Returns:
        InternalPluginLoader instance
    """
    global _internal_loader
    if _internal_loader is None:
        _internal_loader = InternalPluginLoader()
    return _internal_loader


def load_internal_plugins(plugin_names: list[str], builder: PluggingerAppBuilder) -> None:
    """Convenience function to load internal plugins.

    Args:
        plugin_names: List of internal plugin names to load
        builder: App builder to load plugins into
    """
    loader = get_internal_loader()
    loader.load_internal_plugins(plugin_names, builder)


def get_available_internal_plugins() -> list[str]:
    """Get list of available internal plugin names.

    Returns:
        List of internal plugin names
    """
    loader = get_internal_loader()
    return loader.get_available_plugins()


def load_framework_plugins(builder: PluggingerAppBuilder) -> None:
    """Load all framework-required internal plugins.

    This function is called automatically by the framework to load
    internal plugins needed for CLI and framework operations.

    Args:
        builder: App builder to load plugins into
    """
    loader = get_internal_loader()

    # Load specific internal plugins needed by framework
    # (This will be populated as we create internal plugins)
    required_plugins: list[str] = []

    available_plugins = loader.get_available_plugins()
    for plugin_name in required_plugins:
        if plugin_name in available_plugins:
            loader.load_internal_plugin(plugin_name, builder)
        else:
            logger.warning(f"Required internal plugin '{plugin_name}' not available")


# Convenience functions for framework integration (internal use only)
def include_internal_plugins_in_builder(builder: PluggingerAppBuilder, plugin_names: list[str]) -> PluggingerAppBuilder:
    """Include internal plugins in the application builder (framework use only).

    Args:
        builder: App builder instance
        plugin_names: List of internal plugin names to include

    Returns:
        Builder instance for chaining

    Note:
        This function is for framework internal use only.
        Users should not call this directly.
    """
    load_internal_plugins(plugin_names, builder)
    return builder


def include_framework_plugins_in_builder(builder: PluggingerAppBuilder) -> PluggingerAppBuilder:
    """Include framework-required internal plugins in the builder (framework use only).

    Args:
        builder: App builder instance

    Returns:
        Builder instance for chaining

    Note:
        This function is for framework internal use only.
        Users should not call this directly.
    """
    load_framework_plugins(builder)
    return builder
