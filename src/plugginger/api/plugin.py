# src/plugginger/api/plugin.py

"""
Plugin base class and @plugin decorator for Plugginger framework.

This module provides the core plugin functionality including the PluginBase
class that all plugins must inherit from, and the @plugin decorator that
registers classes as Plugginger plugins with metadata.
"""

from __future__ import annotations

import inspect
from abc import ABCMeta
from collections.abc import Callable
from typing import TYPE_CHECKING, Any

from pydantic import BaseModel

from plugginger.api.depends import Depends
from plugginger.core.constants import PLUGIN_METADATA_KEY
from plugginger.core.exceptions import PluginRegistrationError

if TYPE_CHECKING:
    from plugginger.api.app import PluggingerAppInstance


class PluginBase(metaclass=ABCMeta):
    """
    Base class for all plugins in the Plugginger framework.

    This class provides the foundation for plugin development with automatic
    dependency injection and lifecycle management. All plugin classes must
    inherit from this base class and be decorated with @plugin.

    Attributes:
        needs: List of dependencies that this plugin requires
        app: Reference to the PluggingerAppInstance (set by builder)
        _plugginger_plugin_name: Plugin name (set by @plugin decorator)
        _plugginger_plugin_version: Plugin version (set by @plugin decorator)
        _plugginger_config_schema: Optional Pydantic model for config validation
        _plugginger_instance_id: Unique instance ID (set by builder at runtime)

    Example:
        ```python
        @plugin(name="my_service", version="1.0.0", config_schema=MyConfig)
        class MyServicePlugin(PluginBase):
            needs: List[Depends] = [Depends("logger")]

            async def setup(self, plugin_config: MyConfig) -> None:
                self.logger.info("Setting up my service")

            async def teardown(self) -> None:
                self.logger.info("Tearing down my service")
        ```
    """

    # Class attributes for metadata (set by @plugin decorator)
    _plugginger_plugin_name: str
    _plugginger_plugin_version: str
    _plugginger_config_schema: type[BaseModel] | None
    _plugginger_instance_id: str

    # Class attribute for dependency specification
    needs: list[Depends] = []

    # Instance attribute for app reference (forward reference to avoid circular imports)
    app: PluggingerAppInstance

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        """
        Initialize the plugin with app reference and injected dependencies.

        This constructor is called by the PluggingerAppBuilder during plugin
        instantiation. The builder resolves all dependencies specified in the
        'needs' class attribute and passes them as keyword arguments.

        Args:
            app: Reference to the PluggingerAppInstance
            **injected_dependencies: Dependencies resolved by the DI system
                                   (e.g., logger=LoggerProxy(), db=DatabaseProxy())
        """
        self.app = app

        # Store injected dependencies as instance attributes
        for dep_name, dep_instance in injected_dependencies.items():
            setattr(self, dep_name, dep_instance)

    async def setup(self, plugin_config: BaseModel) -> None:  # pragma: no cover  # noqa: B027
        """
        Setup hook called when the plugin is initialized.

        Override this method to perform plugin-specific initialization.
        This method is called after dependency injection and app assignment,
        but before the plugin's services and event handlers are registered.

        Args:
            plugin_config: Configuration instance for this plugin. If a
                         config_schema was specified in @plugin decorator,
                         this will be an instance of that Pydantic model.
                         Otherwise, it will be a generic empty model.

        Note:
            This method is optional. The default implementation does nothing.
        """
        pass

    async def teardown(self) -> None:  # pragma: no cover  # noqa: B027
        """
        Teardown hook called when the plugin is being shut down.

        Override this method to perform plugin-specific cleanup such as
        closing connections, saving state, or releasing resources.
        This method is called before the plugin's services and event
        handlers are unregistered.

        Note:
            This method is optional. The default implementation does nothing.
        """
        pass


def plugin(
    *, name: str, version: str, config_schema: type[BaseModel] | None = None
) -> Callable[[type[PluginBase]], type[PluginBase]]:
    """
    Decorator to mark a class as a Plugginger plugin.

    This decorator validates the plugin class, adds metadata, and ensures
    proper inheritance from PluginBase. All parameters must be keyword-only.

    Args:
        name: Plugin name (must be valid Python identifier, preferably snake_case)
        version: Plugin version string (should follow PEP 440)
        config_schema: Optional Pydantic model class for configuration validation

    Returns:
        Decorator function that transforms a class into a valid plugin

    Raises:
        PluginRegistrationError: If validation fails (invalid class, name, version, etc.)
        TypeError: If config_schema is not a BaseModel subclass

    Example:
        ```python
        from pydantic import BaseModel

        class MyConfig(BaseModel):
            api_key: str
            timeout: int = 30

        @plugin(name="data_fetcher", version="0.1.0", config_schema=MyConfig)
        class DataFetcherPlugin(PluginBase):
            needs: List[Depends] = [Depends("logger")]

            async def setup(self, plugin_config: MyConfig) -> None:
                self.api_key = plugin_config.api_key
        ```
    """

    def decorator(cls: type[PluginBase]) -> type[PluginBase]:
        # Validate that the decorated object is a class
        if not inspect.isclass(cls):
            raise PluginRegistrationError(
                f"@plugin decorator can only be applied to classes, got {type(cls).__name__}"
            )

        # Validate that the class inherits from PluginBase
        if not issubclass(cls, PluginBase):
            raise PluginRegistrationError(
                f"Plugin class '{cls.__name__}' must inherit from PluginBase"
            )

        # Validate plugin name
        if not name or not isinstance(name, str):
            raise PluginRegistrationError(f"Plugin name must be a non-empty string, got: {name!r}")

        if not name.isidentifier():
            raise PluginRegistrationError(f"Plugin name '{name}' must be a valid Python identifier")

        if not name.islower() or name.startswith("_") or name.endswith("_"):
            raise PluginRegistrationError(
                f"Plugin name '{name}' should be lowercase without leading/trailing underscores"
            )

        # Validate plugin version
        if not version or not isinstance(version, str):
            raise PluginRegistrationError(
                f"Plugin version must be a non-empty string, got: {version!r}"
            )

        # Basic PEP 440 validation (starts with digit or 'v'+digit)
        if not (
            version[0].isdigit()
            or (version.startswith("v") and len(version) > 1 and version[1].isdigit())
        ):
            raise PluginRegistrationError(
                f"Plugin version '{version}' does not appear to be a valid version string"
            )

        # Validate config_schema if provided
        if config_schema is not None:
            if not inspect.isclass(config_schema) or not issubclass(config_schema, BaseModel):
                raise TypeError(
                    f"config_schema must be a subclass of pydantic.BaseModel, got {config_schema}"
                )

        # Set metadata attributes on the class
        cls._plugginger_plugin_name = name
        cls._plugginger_plugin_version = version
        cls._plugginger_config_schema = config_schema

        # Set the plugin metadata flag
        setattr(cls, PLUGIN_METADATA_KEY, True)

        return cls

    return decorator


def get_plugin_metadata(plugin_class: type[PluginBase]) -> dict[str, Any]:
    """
    Get plugin metadata from a plugin class.

    Args:
        plugin_class: The plugin class to inspect

    Returns:
        Dictionary containing plugin metadata

    Raises:
        PluginRegistrationError: If the class is not a valid plugin
    """
    if not hasattr(plugin_class, PLUGIN_METADATA_KEY):
        raise PluginRegistrationError(
            f"Class '{plugin_class.__name__}' is not a valid plugin (missing @plugin decorator)"
        )

    return {
        "name": getattr(plugin_class, "_plugginger_plugin_name", "unknown"),
        "version": getattr(plugin_class, "_plugginger_plugin_version", "unknown"),
        "config_schema": getattr(plugin_class, "_plugginger_config_schema", None),
        "class_name": plugin_class.__name__,
        "module": plugin_class.__module__,
    }


def is_plugin_class(cls: type[object]) -> bool:
    """
    Check if a class is a valid plugin class.

    Args:
        cls: The class to check

    Returns:
        True if the class is a valid plugin, False otherwise
    """
    return (
        inspect.isclass(cls)
        and hasattr(cls, PLUGIN_METADATA_KEY)
        and issubclass(cls, PluginBase)
        and not inspect.isabstract(cls)
    )
