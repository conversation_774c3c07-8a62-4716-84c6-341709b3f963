"""
Environment variable loader with .env file support.
"""
import os
import logging
from pathlib import Path
from typing import Optional, Dict, Any

try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False

logger = logging.getLogger(__name__)


class EnvLoader:
    """Environment variable loader with .env file support."""
    
    def __init__(self, env_file: Optional[str] = None) -> None:
        """Initialize environment loader.
        
        Args:
            env_file: Path to .env file (default: .env in project root)
        """
        self.env_file = env_file or self._find_env_file()
        self.loaded = False
        self._load_env_file()
    
    def _find_env_file(self) -> Optional[str]:
        """Find .env file in project hierarchy."""
        current_dir = Path.cwd()
        
        # Look for .env file in current directory and parent directories
        for path in [current_dir] + list(current_dir.parents):
            env_file = path / ".env"
            if env_file.exists():
                return str(env_file)
        
        return None
    
    def _load_env_file(self) -> None:
        """Load environment variables from .env file."""
        if not DOTENV_AVAILABLE:
            logger.warning("python-dotenv not available. Install with: pip install python-dotenv")
            return
        
        if self.env_file and Path(self.env_file).exists():
            load_dotenv(self.env_file, override=False)
            self.loaded = True
            logger.info(f"Loaded environment variables from: {self.env_file}")
        else:
            logger.debug("No .env file found or specified")
    
    def get(self, key: str, default: Optional[str] = None) -> Optional[str]:
        """Get environment variable value.
        
        Args:
            key: Environment variable name
            default: Default value if not found
            
        Returns:
            Environment variable value or default
        """
        return os.getenv(key, default)
    
    def get_bool(self, key: str, default: bool = False) -> bool:
        """Get boolean environment variable.
        
        Args:
            key: Environment variable name
            default: Default value if not found
            
        Returns:
            Boolean value
        """
        value = self.get(key)
        if value is None:
            return default
        return value.lower() in ("true", "1", "yes", "on")
    
    def get_int(self, key: str, default: int = 0) -> int:
        """Get integer environment variable.
        
        Args:
            key: Environment variable name
            default: Default value if not found
            
        Returns:
            Integer value
        """
        value = self.get(key)
        if value is None:
            return default
        try:
            return int(value)
        except ValueError:
            logger.warning(f"Invalid integer value for {key}: {value}")
            return default
    
    def has_api_key(self, provider: str) -> bool:
        """Check if API key is available for provider.
        
        Args:
            provider: Provider name (openai, google, groq, etc.)
            
        Returns:
            True if API key is available and valid
        """
        key_mapping = {
            "openai": "OPENAI_API_KEY",
            "google": "GOOGLE_API_KEY", 
            "gemini": "GOOGLE_API_KEY",
            "groq": "GROQ_API_KEY",
            "ollama": "OLLAMA_BASE_URL"
        }
        
        env_key = key_mapping.get(provider.lower())
        if not env_key:
            return False
        
        api_key = self.get(env_key)
        if not api_key:
            return False
        
        # Check for placeholder values
        placeholder_patterns = [
            "your-", "sk-your-", "gsk_your-", "replace-", "enter-", "add-"
        ]
        
        if any(pattern in api_key.lower() for pattern in placeholder_patterns):
            return False
        
        # Minimum length checks
        min_lengths = {
            "OPENAI_API_KEY": 40,  # OpenAI keys are typically 51+ chars
            "GOOGLE_API_KEY": 30,  # Google keys vary
            "GROQ_API_KEY": 40,    # Groq keys are typically 50+ chars
        }
        
        min_length = min_lengths.get(env_key, 10)
        return len(api_key) >= min_length
    
    def get_available_providers(self) -> Dict[str, bool]:
        """Get list of available providers based on API keys.
        
        Returns:
            Dictionary mapping provider names to availability
        """
        providers = {
            "openai": self.has_api_key("openai"),
            "google": self.has_api_key("google"),
            "gemini": self.has_api_key("gemini"),
            "groq": self.has_api_key("groq"),
            "ollama": self.has_api_key("ollama")
        }
        
        return providers
    
    def should_run_integration_tests(self) -> bool:
        """Check if integration tests should be run.
        
        Returns:
            True if integration tests should be run
        """
        return self.get_bool("RUN_INTEGRATION_TESTS", False)
    
    def should_run_e2e_tests(self) -> bool:
        """Check if E2E tests should be run.
        
        Returns:
            True if E2E tests should be run
        """
        return self.get_bool("RUN_E2E_TESTS", False)
    
    def get_test_config(self) -> Dict[str, Any]:
        """Get test configuration.
        
        Returns:
            Test configuration dictionary
        """
        return {
            "run_integration_tests": self.should_run_integration_tests(),
            "run_e2e_tests": self.should_run_e2e_tests(),
            "available_providers": self.get_available_providers(),
            "log_level": self.get("LOG_LEVEL", "INFO"),
            "debug_logging": self.get_bool("ENABLE_DEBUG_LOGGING", False)
        }
    
    def create_env_file_if_missing(self) -> None:
        """Create .env file from template if it doesn't exist."""
        env_path = Path(".env")
        template_path = Path(".env.template")
        
        if not env_path.exists() and template_path.exists():
            try:
                env_path.write_text(template_path.read_text())
                logger.info("Created .env file from template. Please fill in your API keys.")
            except Exception as e:
                logger.error(f"Failed to create .env file: {e}")


# Global instance
env_loader = EnvLoader()


def get_env_loader() -> EnvLoader:
    """Get global environment loader instance."""
    return env_loader


def has_api_key(provider: str) -> bool:
    """Check if API key is available for provider."""
    return env_loader.has_api_key(provider)


def get_api_key(provider: str) -> Optional[str]:
    """Get API key for provider."""
    key_mapping = {
        "openai": "OPENAI_API_KEY",
        "google": "GOOGLE_API_KEY",
        "gemini": "GOOGLE_API_KEY", 
        "groq": "GROQ_API_KEY"
    }
    
    env_key = key_mapping.get(provider.lower())
    if env_key:
        return env_loader.get(env_key)
    return None
