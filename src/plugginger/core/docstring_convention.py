"""Plugginger Docstring Convention System.

This module defines the standard docstring format for Plugginger services,
optimized for both human readability and AI-agent compatibility.

The convention is based on Google-style docstrings with Plugginger-specific
extensions for service metadata, dependency information, and AI-agent hints.
"""

from __future__ import annotations

import re
from dataclasses import dataclass
from enum import Enum
from typing import Any

from plugginger.core.exceptions import ConfigurationError


class DocstringSectionType(Enum):
    """Types of docstring sections supported by Plugginger convention."""

    SUMMARY = "summary"
    DESCRIPTION = "description"
    ARGS = "args"
    RETURNS = "returns"
    RAISES = "raises"
    EXAMPLE = "example"
    NOTE = "note"
    SINCE = "since"
    SEE_ALSO = "see_also"
    DEPRECATED = "deprecated"
    AI_METADATA = "ai_metadata"
    USAGE_PATTERNS = "usage_patterns"
    ERROR_HANDLING = "error_handling"


@dataclass
class DocstringSection:
    """Represents a section within a docstring."""

    section_type: DocstringSectionType
    content: str
    line_number: int = 0

    def __post_init__(self) -> None:
        """Validate section content after initialization."""
        if not self.content.strip():
            raise ConfigurationError(
                f"Docstring section '{self.section_type.value}' cannot be empty"
            )


@dataclass
class ServiceDocstring:
    """Parsed representation of a service docstring following Plugginger convention."""

    summary: str
    description: str | None = None
    args: dict[str, str] | None = None
    returns: str | None = None
    raises: dict[str, str] | None = None
    examples: list[str] | None = None
    notes: list[str] | None = None
    since: str | None = None
    see_also: list[str] | None = None
    deprecated: str | None = None
    ai_metadata: dict[str, Any] | None = None
    usage_patterns: list[str] | None = None
    error_handling: list[str] | None = None

    def __post_init__(self) -> None:
        """Initialize default values for optional fields."""
        if self.args is None:
            self.args = {}
        if self.raises is None:
            self.raises = {}
        if self.examples is None:
            self.examples = []
        if self.notes is None:
            self.notes = []
        if self.see_also is None:
            self.see_also = []
        if self.ai_metadata is None:
            self.ai_metadata = {}
        if self.usage_patterns is None:
            self.usage_patterns = []
        if self.error_handling is None:
            self.error_handling = []

    def validate(self) -> list[str]:
        """Validate the docstring against Plugginger conventions.

        Returns:
            List of validation error messages. Empty list if valid.
        """
        errors = []

        # Summary validation
        if not self.summary:
            errors.append("Summary is required")
        elif len(self.summary) > 80:
            errors.append(f"Summary too long ({len(self.summary)} chars, max 80)")
        elif self.summary.endswith('.'):
            errors.append("Summary should not end with a period")

        # Example validation
        if not self.examples:
            errors.append("At least one example is required")

        # Args validation
        if self.args:
            for arg_name, arg_desc in self.args.items():
                if not arg_desc.strip():
                    errors.append(f"Argument '{arg_name}' description is empty")

        # Raises validation
        if self.raises:
            for exc_name, exc_desc in self.raises.items():
                if not exc_desc.strip():
                    errors.append(f"Exception '{exc_name}' description is empty")

        return errors

    def is_valid(self) -> bool:
        """Check if the docstring is valid according to Plugginger conventions."""
        return len(self.validate()) == 0


class DocstringParser:
    """Parser for Plugginger service docstrings."""

    # Regex patterns for parsing docstring sections
    SECTION_PATTERNS = {
        DocstringSectionType.ARGS: re.compile(r'^Args:\s*$', re.MULTILINE),
        DocstringSectionType.RETURNS: re.compile(r'^Returns:\s*$', re.MULTILINE),
        DocstringSectionType.RAISES: re.compile(r'^Raises:\s*$', re.MULTILINE),
        DocstringSectionType.EXAMPLE: re.compile(r'^Example[s]?:\s*$', re.MULTILINE),
        DocstringSectionType.NOTE: re.compile(r'^Note[s]?:\s*$', re.MULTILINE),
        DocstringSectionType.SINCE: re.compile(r'^Since:\s*$', re.MULTILINE),
        DocstringSectionType.SEE_ALSO: re.compile(r'^See Also:\s*$', re.MULTILINE),
        DocstringSectionType.DEPRECATED: re.compile(r'^Deprecated:\s*$', re.MULTILINE),
        DocstringSectionType.AI_METADATA: re.compile(r'^AI_METADATA:\s*$', re.MULTILINE),
        DocstringSectionType.USAGE_PATTERNS: re.compile(r'^USAGE_PATTERNS:\s*$', re.MULTILINE),
        DocstringSectionType.ERROR_HANDLING: re.compile(r'^ERROR_HANDLING:\s*$', re.MULTILINE),
    }

    def parse(self, docstring: str) -> ServiceDocstring:
        """Parse a docstring into a ServiceDocstring object.

        Args:
            docstring: Raw docstring text to parse

        Returns:
            ServiceDocstring object with parsed sections

        Raises:
            ConfigurationError: When docstring format is invalid
        """
        if not docstring or not docstring.strip():
            raise ConfigurationError("Docstring cannot be empty")

        # Clean and normalize the docstring
        cleaned = self._clean_docstring(docstring)

        # Extract summary (first line)
        lines = cleaned.split('\n')
        summary = lines[0].strip()

        if not summary:
            raise ConfigurationError("Docstring must start with a summary line")

        # Parse sections
        sections = self._parse_sections(cleaned)

        # Build ServiceDocstring object
        return ServiceDocstring(
            summary=summary,
            description=self._extract_description(cleaned),
            args=self._parse_args_section(sections.get(DocstringSectionType.ARGS, "")),
            returns=sections.get(DocstringSectionType.RETURNS),
            raises=self._parse_raises_section(sections.get(DocstringSectionType.RAISES, "")),
            examples=self._parse_examples_section(sections.get(DocstringSectionType.EXAMPLE, "")),
            notes=self._parse_notes_section(sections.get(DocstringSectionType.NOTE, "")),
            since=sections.get(DocstringSectionType.SINCE),
            see_also=self._parse_see_also_section(sections.get(DocstringSectionType.SEE_ALSO, "")),
            deprecated=sections.get(DocstringSectionType.DEPRECATED),
            ai_metadata=self._parse_ai_metadata_section(sections.get(DocstringSectionType.AI_METADATA, "")),
            usage_patterns=self._parse_usage_patterns_section(sections.get(DocstringSectionType.USAGE_PATTERNS, "")),
            error_handling=self._parse_error_handling_section(sections.get(DocstringSectionType.ERROR_HANDLING, "")),
        )

    def _clean_docstring(self, docstring: str) -> str:
        """Clean and normalize docstring formatting."""
        # Remove leading/trailing quotes and whitespace
        cleaned = docstring.strip()
        if cleaned.startswith('"""') and cleaned.endswith('"""'):
            cleaned = cleaned[3:-3]
        elif cleaned.startswith("'''") and cleaned.endswith("'''"):
            cleaned = cleaned[3:-3]
        cleaned = cleaned.strip()

        # Normalize line endings
        cleaned = cleaned.replace('\r\n', '\n').replace('\r', '\n')

        # Remove common leading whitespace
        lines = cleaned.split('\n')
        if len(lines) > 1:
            # Find minimum indentation (excluding empty lines)
            min_indent = float('inf')
            for line in lines[1:]:  # Skip first line
                if line.strip():
                    indent = len(line) - len(line.lstrip())
                    min_indent = min(min_indent, indent)

            # Remove common indentation
            if min_indent != float('inf') and min_indent > 0:
                min_indent_int = int(min_indent)
                lines = [lines[0]] + [line[min_indent_int:] if line.strip() else line for line in lines[1:]]

        return '\n'.join(lines)

    def _parse_sections(self, docstring: str) -> dict[DocstringSectionType, str]:
        """Parse docstring into sections."""
        sections = {}

        # Find all section headers
        section_positions = []
        for section_type, pattern in self.SECTION_PATTERNS.items():
            for match in pattern.finditer(docstring):
                section_positions.append((match.start(), section_type, match.end()))

        # Sort by position
        section_positions.sort()

        # Extract section content
        for i, (_start_pos, section_type, header_end) in enumerate(section_positions):
            # Find end of this section (start of next section or end of string)
            if i + 1 < len(section_positions):
                end_pos = section_positions[i + 1][0]
            else:
                end_pos = len(docstring)

            # Extract section content
            section_content = docstring[header_end:end_pos].strip()
            if section_content:
                sections[section_type] = section_content

        return sections

    def _extract_description(self, docstring: str) -> str | None:
        """Extract the description section (between summary and first section)."""
        lines = docstring.split('\n')
        if len(lines) < 3:  # Need at least summary + blank line + description
            return None

        # Find first section header
        first_section_line = None
        for i, line in enumerate(lines[2:], start=2):  # Skip summary and blank line
            for pattern in self.SECTION_PATTERNS.values():
                if pattern.match(line.strip()):
                    first_section_line = i
                    break
            if first_section_line:
                break

        # Extract description
        if first_section_line:
            desc_lines = lines[2:first_section_line]
        else:
            desc_lines = lines[2:]

        description = '\n'.join(desc_lines).strip()
        return description if description else None

    def _parse_args_section(self, content: str) -> dict[str, str]:
        """Parse Args section into argument descriptions."""
        args: dict[str, str] = {}
        if not content:
            return args

        # Parse argument lines (format: "arg_name: description")
        for line in content.split('\n'):
            line = line.strip()
            if ':' in line:
                arg_name, description = line.split(':', 1)
                args[arg_name.strip()] = description.strip()

        return args

    def _parse_raises_section(self, content: str) -> dict[str, str]:
        """Parse Raises section into exception descriptions."""
        raises: dict[str, str] = {}
        if not content:
            return raises

        # Parse exception lines (format: "ExceptionType: description")
        for line in content.split('\n'):
            line = line.strip()
            if ':' in line:
                exc_name, description = line.split(':', 1)
                raises[exc_name.strip()] = description.strip()

        return raises

    def _parse_examples_section(self, content: str) -> list[str]:
        """Parse Examples section into list of examples."""
        if not content:
            return []

        # Split by >>> or blank lines to separate examples
        examples: list[str] = []
        current_example: list[str] = []

        for line in content.split('\n'):
            if line.strip().startswith('>>>') or (not line.strip() and current_example):
                if current_example:
                    examples.append('\n'.join(current_example))
                    current_example = []
            if line.strip():
                current_example.append(line)

        if current_example:
            examples.append('\n'.join(current_example))

        return examples

    def _parse_notes_section(self, content: str) -> list[str]:
        """Parse Notes section into list of notes."""
        if not content:
            return []

        # Split by lines and handle both bullet points and plain text
        notes: list[str] = []
        current_note: list[str] = []

        for line in content.split('\n'):
            line = line.strip()
            if not line:
                # Empty line - finish current note if any
                if current_note:
                    notes.append(' '.join(current_note))
                    current_note = []
            elif line.startswith('-') or line.startswith('*'):
                # Bullet point - finish previous note and start new one
                if current_note:
                    notes.append(' '.join(current_note))
                current_note = [line[1:].strip()]
            else:
                # Continuation of current note or standalone note
                if current_note:
                    current_note.append(line)
                else:
                    current_note = [line]

        # Add final note if any
        if current_note:
            notes.append(' '.join(current_note))

        return notes

    def _parse_see_also_section(self, content: str) -> list[str]:
        """Parse See Also section into list of references."""
        if not content:
            return []

        see_also = []
        for line in content.split('\n'):
            line = line.strip()
            if line and (line.startswith('-') or line.startswith('*')):
                see_also.append(line[1:].strip())
            elif line:
                see_also.append(line)

        return see_also

    def _parse_ai_metadata_section(self, content: str) -> dict[str, Any]:
        """Parse AI_METADATA section into structured metadata."""
        metadata: dict[str, Any] = {}
        if not content:
            return metadata

        # Parse YAML-like key: value pairs
        for line in content.split('\n'):
            line = line.strip()
            if ':' in line:
                key, value = line.split(':', 1)
                key = key.strip()
                value = value.strip()

                # Try to parse value as appropriate type
                if value.lower() in ('true', 'false'):
                    metadata[key] = value.lower() == 'true'
                elif value.startswith('[') and value.endswith(']'):
                    # Parse list
                    items = value[1:-1].split(',')
                    metadata[key] = [item.strip().strip('"\'') for item in items if item.strip()]
                elif value.isdigit():
                    metadata[key] = int(value)
                else:
                    metadata[key] = value

        return metadata

    def _parse_usage_patterns_section(self, content: str) -> list[str]:
        """Parse USAGE_PATTERNS section into list of patterns."""
        if not content:
            return []

        patterns = []
        for line in content.split('\n'):
            line = line.strip()
            if line and (line.startswith('-') or line.startswith('*')):
                patterns.append(line[1:].strip())
            elif line:
                patterns.append(line)

        return patterns

    def _parse_error_handling_section(self, content: str) -> list[str]:
        """Parse ERROR_HANDLING section into list of error handling strategies."""
        if not content:
            return []

        strategies = []
        for line in content.split('\n'):
            line = line.strip()
            if line and (line.startswith('-') or line.startswith('*')):
                strategies.append(line[1:].strip())
            elif line:
                strategies.append(line)

        return strategies


class DocstringGenerator:
    """Generator for Plugginger-compliant service docstrings."""

    def generate_template(
        self,
        service_name: str,
        args: dict[str, str] | None = None,
        returns: str | None = None,
        raises: dict[str, str] | None = None,
        include_ai_metadata: bool = True
    ) -> str:
        """Generate a docstring template for a service.

        Args:
            service_name: Name of the service
            args: Dictionary of argument names and types
            returns: Return type description
            raises: Dictionary of exception types and descriptions
            include_ai_metadata: Whether to include AI metadata sections

        Returns:
            Formatted docstring template
        """
        template_parts = []

        # Summary placeholder
        template_parts.append(f'"""{service_name.replace("_", " ").title()} service.')
        template_parts.append('')

        # Description placeholder
        template_parts.append('Detailed description of what this service does and how it works.')
        template_parts.append('')

        # Args section
        if args:
            template_parts.append('Args:')
            for arg_name, arg_type in args.items():
                template_parts.append(f'    {arg_name}: {arg_type} - Description of {arg_name}')
            template_parts.append('')

        # Returns section
        if returns:
            template_parts.append('Returns:')
            template_parts.append(f'    {returns} - Description of return value')
            template_parts.append('')

        # Raises section
        if raises:
            template_parts.append('Raises:')
            for exc_type, exc_desc in raises.items():
                template_parts.append(f'    {exc_type}: {exc_desc}')
            template_parts.append('')

        # Example section
        template_parts.append('Example:')
        template_parts.append(f'    >>> result = await plugin.{service_name}()')
        template_parts.append('    >>> print(result)')
        template_parts.append('    # Expected output')
        template_parts.append('')

        # AI metadata section
        if include_ai_metadata:
            template_parts.append('AI_METADATA:')
            template_parts.append('    complexity: low')
            template_parts.append('    dependencies: []')
            template_parts.append('    side_effects: none')
            template_parts.append('    idempotent: true')
            template_parts.append('    async_safe: true')
            template_parts.append('')

        template_parts.append('"""')

        return '\n'.join(template_parts)


# Export main classes and functions
__all__ = [
    'DocstringSectionType',
    'DocstringSection',
    'ServiceDocstring',
    'DocstringParser',
    'DocstringGenerator',
]
