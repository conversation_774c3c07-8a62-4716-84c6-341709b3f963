# Migration Guide: Docstring Convention

**Target**: Existing Plugginger plugins  
**Goal**: Update to new docstring convention  
**Effort**: Low to Medium (depending on plugin complexity)

## Overview

This guide helps you migrate existing Plugginger plugins to the new docstring convention. The migration ensures your plugins are compatible with AI agents and follow best practices for documentation.

## Quick Assessment

### Check Current State

```python
# Run this script to assess your plugin's docstring status
from plugginger.core.docstring_convention import DocstringParser
import inspect

def assess_plugin_docstrings(plugin_class):
    """Assess docstring quality for a plugin class."""
    parser = DocstringParser()
    results = []
    
    for attr_name in dir(plugin_class):
        if not attr_name.startswith("_"):
            attr = getattr(plugin_class, attr_name)
            if hasattr(attr, "_plugginger_service"):
                docstring = attr.__doc__
                if docstring:
                    try:
                        parsed = parser.parse(docstring)
                        errors = parsed.validate()
                        results.append({
                            "service": attr_name,
                            "status": "valid" if len(errors) == 0 else "invalid",
                            "errors": errors
                        })
                    except Exception as e:
                        results.append({
                            "service": attr_name,
                            "status": "error",
                            "errors": [str(e)]
                        })
                else:
                    results.append({
                        "service": attr_name,
                        "status": "missing",
                        "errors": ["No docstring found"]
                    })
    
    return results

# Usage
# results = assess_plugin_docstrings(YourPluginClass)
# for result in results:
#     print(f"{result['service']}: {result['status']}")
```

## Migration Steps

### Step 1: Add Missing Docstrings

**Before** (no docstring):
```python
@service()
async def process_data(self, data: dict) -> dict:
    return {"processed": True, **data}
```

**After** (with convention):
```python
@service()
async def process_data(self, data: dict[str, Any]) -> dict[str, Any]:
    """Process Data service.
    
    Transforms input data and returns processed results.
    
    Args:
        data: Input data dictionary to process
        
    Returns:
        dict[str, Any]: Processed data with additional metadata
        
    Example:
        >>> result = await plugin.process_data({"key": "value"})
        >>> print(result["processed"])
        True
        
    AI_METADATA:
        complexity: low
        dependencies: []
        side_effects: none
        idempotent: true
        async_safe: true
    """
    return {"processed": True, **data}
```

### Step 2: Enhance Existing Docstrings

**Before** (minimal docstring):
```python
@service()
async def fetch_user(self, user_id: str) -> dict:
    """Get user by ID."""
    # Implementation...
```

**After** (enhanced with convention):
```python
@service()
async def fetch_user(self, user_id: str) -> dict[str, Any]:
    """Fetch User service.
    
    Retrieves user information from the database by user ID
    with caching and error handling.
    
    Args:
        user_id: Unique identifier for the user
        
    Returns:
        dict[str, Any] containing:
            - id: User identifier
            - name: User display name
            - email: User email address
            - created_at: Account creation timestamp
            
    Raises:
        UserNotFoundError: When user_id does not exist
        DatabaseError: When database connection fails
        
    Example:
        >>> user = await plugin.fetch_user("user123")
        >>> print(user["name"])
        "John Doe"
        
    AI_METADATA:
        complexity: medium
        dependencies: ["database", "cache"]
        side_effects: read
        idempotent: true
        async_safe: true
        timeout: 5
    """
    # Implementation...
```

### Step 3: Add AI Metadata

Choose appropriate values for your services:

#### Complexity Levels
- **low**: Simple operations, minimal logic
- **medium**: Some business logic, moderate complexity
- **high**: Complex algorithms, multiple decision points

#### Dependencies
List all services/plugins your service depends on:
```python
dependencies: ["database", "cache", "notification_service"]
```

#### Side Effects
- **none**: Pure function, no external changes
- **read**: Reads from external systems (DB, API, files)
- **write**: Modifies external systems
- **network**: Makes network calls

#### Other Metadata
```python
AI_METADATA:
    complexity: medium
    dependencies: ["database"]
    side_effects: read
    idempotent: true          # Safe to retry
    async_safe: true          # Thread/async safe
    rate_limit: 100/minute    # Optional: rate limiting
    timeout: 30               # Optional: timeout in seconds
```

## Automated Migration Tools

### Using DocstringGenerator

```python
from plugginger.core.docstring_convention import DocstringGenerator
import inspect

def migrate_service_docstring(service_method, service_name: str):
    """Automatically generate docstring for a service method."""
    generator = DocstringGenerator()
    
    # Extract method signature
    sig = inspect.signature(service_method)
    
    # Build args dictionary
    args = {}
    for param_name, param in sig.parameters.items():
        if param_name != 'self':
            param_type = str(param.annotation) if param.annotation != param.empty else "Any"
            args[param_name] = f"{param_type} - Description needed"
    
    # Get return type
    return_type = str(sig.return_annotation) if sig.return_annotation != sig.empty else "Any"
    
    # Generate docstring template
    docstring = generator.generate_template(
        service_name=service_name,
        args=args,
        returns=return_type,
        include_ai_metadata=True
    )
    
    return docstring

# Usage
# new_docstring = migrate_service_docstring(your_service_method, "your_service")
# print(new_docstring)
```

### Batch Migration Script

```python
def migrate_plugin_docstrings(plugin_class):
    """Migrate all services in a plugin to new docstring convention."""
    from plugginger.core.docstring_convention import DocstringGenerator
    
    generator = DocstringGenerator()
    migrations = []
    
    for attr_name in dir(plugin_class):
        if not attr_name.startswith("_"):
            attr = getattr(plugin_class, attr_name)
            if hasattr(attr, "_plugginger_service"):
                # Generate new docstring
                sig = inspect.signature(attr)
                
                args = {}
                for param_name, param in sig.parameters.items():
                    if param_name != 'self':
                        args[param_name] = f"{param.annotation} - TODO: Add description"
                
                returns = str(sig.return_annotation) if sig.return_annotation != sig.empty else "Any"
                
                new_docstring = generator.generate_template(
                    service_name=attr_name,
                    args=args,
                    returns=returns,
                    include_ai_metadata=True
                )
                
                migrations.append({
                    "service": attr_name,
                    "old_docstring": attr.__doc__,
                    "new_docstring": new_docstring
                })
    
    return migrations

# Usage
# migrations = migrate_plugin_docstrings(YourPluginClass)
# for migration in migrations:
#     print(f"Service: {migration['service']}")
#     print(f"New docstring:\n{migration['new_docstring']}")
```

## Validation and Testing

### Validate Migration Results

```python
from plugginger.core.docstring_convention import DocstringParser

def validate_migrated_plugin(plugin_class):
    """Validate that all services have valid docstrings after migration."""
    parser = DocstringParser()
    issues = []
    
    for attr_name in dir(plugin_class):
        if not attr_name.startswith("_"):
            attr = getattr(plugin_class, attr_name)
            if hasattr(attr, "_plugginger_service"):
                docstring = attr.__doc__
                if not docstring:
                    issues.append(f"{attr_name}: Missing docstring")
                    continue
                
                try:
                    parsed = parser.parse(docstring)
                    errors = parsed.validate()
                    if errors:
                        issues.append(f"{attr_name}: {', '.join(errors)}")
                except Exception as e:
                    issues.append(f"{attr_name}: Parse error - {e}")
    
    return issues

# Usage
# issues = validate_migrated_plugin(YourPluginClass)
# if issues:
#     print("Issues found:")
#     for issue in issues:
#         print(f"  - {issue}")
# else:
#     print("✅ All docstrings are valid!")
```

### Add Tests for Docstring Compliance

```python
import pytest
from plugginger.core.docstring_convention import DocstringParser

class TestDocstringCompliance:
    """Test that all services follow docstring convention."""
    
    def setup_method(self):
        self.parser = DocstringParser()
    
    def test_all_services_have_valid_docstrings(self):
        """Test that all services have valid docstrings."""
        plugin_instance = YourPluginClass()
        
        for attr_name in dir(plugin_instance):
            if not attr_name.startswith("_"):
                attr = getattr(plugin_instance, attr_name)
                if hasattr(attr, "_plugginger_service"):
                    docstring = attr.__doc__
                    assert docstring is not None, f"Service {attr_name} missing docstring"
                    
                    parsed = self.parser.parse(docstring)
                    errors = parsed.validate()
                    assert len(errors) == 0, f"Service {attr_name} docstring errors: {errors}"
```

## Common Migration Patterns

### Pattern 1: Simple Service
```python
# Before
@service()
async def get_status(self) -> str:
    return "active"

# After
@service()
async def get_status(self) -> str:
    """Get Status service.
    
    Returns the current status of the plugin.
    
    Returns:
        str: Current plugin status ("active", "inactive", "error")
        
    Example:
        >>> status = await plugin.get_status()
        >>> print(status)
        "active"
        
    AI_METADATA:
        complexity: low
        dependencies: []
        side_effects: none
        idempotent: true
        async_safe: true
    """
    return "active"
```

### Pattern 2: Data Processing Service
```python
# Before
@service()
async def transform(self, data):
    return {"transformed": data}

# After
@service()
async def transform(self, data: dict[str, Any]) -> dict[str, Any]:
    """Transform service.
    
    Applies data transformation rules to input data.
    
    Args:
        data: Input data dictionary to transform
        
    Returns:
        dict[str, Any]: Transformed data with processing metadata
        
    Raises:
        ValidationError: When input data format is invalid
        
    Example:
        >>> result = await plugin.transform({"key": "value"})
        >>> print(result["transformed"])
        {"key": "value"}
        
    AI_METADATA:
        complexity: medium
        dependencies: ["validator"]
        side_effects: none
        idempotent: true
        async_safe: true
    """
    return {"transformed": data}
```

### Pattern 3: External API Service
```python
# Before
@service()
async def fetch_data(self, url):
    # HTTP request implementation
    pass

# After
@service()
async def fetch_data(self, url: str) -> dict[str, Any]:
    """Fetch Data service.
    
    Retrieves data from external URL with retry logic and caching.
    
    Args:
        url: Target URL to fetch data from
        
    Returns:
        dict[str, Any]: Response data from the external service
        
    Raises:
        ConnectionError: When URL is unreachable
        TimeoutError: When request exceeds timeout
        
    Example:
        >>> data = await plugin.fetch_data("https://api.example.com/data")
        >>> print(data["status"])
        "success"
        
    AI_METADATA:
        complexity: high
        dependencies: ["http_client", "cache"]
        side_effects: network
        idempotent: true
        async_safe: true
        rate_limit: 60/minute
        timeout: 30
    """
    # HTTP request implementation
    pass
```

## Best Practices

### 1. Gradual Migration
- Start with most important/frequently used services
- Migrate one service at a time
- Test each migration before proceeding

### 2. Documentation Quality
- Write clear, concise summaries
- Include realistic examples
- Document all parameters and return values
- Be accurate with AI metadata

### 3. Testing
- Add docstring validation tests
- Verify examples are executable
- Test error conditions mentioned in docstrings

### 4. Team Coordination
- Communicate migration timeline
- Review migrated docstrings with team
- Update development guidelines

## Troubleshooting

### Common Issues

1. **"Summary too long"**
   - Keep summaries under 80 characters
   - Move details to description section

2. **"Missing example section"**
   - Add at least one working example
   - Show realistic usage patterns

3. **"Invalid AI metadata format"**
   - Use YAML-like key: value format
   - Check supported metadata keys

4. **"Type annotation errors"**
   - Update function signatures with proper types
   - Use `dict[str, Any]` instead of `dict`

### Getting Help

- Check [Docstring Convention Guide](../conventions/docstrings.md)
- Use validation tools to identify issues
- Review template examples for patterns
- Ask team for docstring reviews

---

**Migration Status**: Ready for use  
**Estimated Time**: 1-3 hours per plugin (depending on size)  
**Support**: Available via documentation and examples
