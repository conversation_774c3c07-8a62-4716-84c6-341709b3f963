# Documentation Archive

This directory contains documentation files that are no longer current but are preserved for historical reference.

## Archived Files

### Roadmap and Planning Documents
- **`ROADMAP_BACKUP_20250606_100850.md`** - Backup of roadmap from June 6, 2025
- **`ROADMAP_OLD.md`** - Previous version of the main roadmap
- **`ROADMAP_DETAILED.md`** - Detailed roadmap superseded by current ROADMAP.md
- **`ROADMAP_README.md`** - Redundant roadmap documentation

### Development Artifacts
- **`AGENT_PROMPT_OPTIMIZED.md`** - AI agent prompt optimization document
- **`STUBGEN_HANDOFF.md`** - Development handoff documentation
- **`PROMPT.md`** - AI prompt development artifact
- **`REFACTORING_SUMMARY.md`** - Historical refactoring summary
- **`ERROR_PATTERNS.md`** - Development error pattern analysis
- **`CODE_QUALITY_ANALYSIS.md`** - Historical code quality analysis
- **`CIRCULAR_DEPENDENCIES.md`** - Circular dependency analysis artifact

### Completed Design Documents
- **`CLI_MODEL_SELECTION_DESIGN.md`** - Design document for CLI model selection (implemented in Sprint S5.1)

### Empty/Obsolete Files
- **`Backlog.md`** - Empty backlog file (5 bytes)

## Archive Policy

Files are archived when they:
- Contain outdated information that no longer applies
- Describe completed features or resolved issues
- Are superseded by newer documentation
- Serve as development artifacts no longer needed for current work

## Accessing Archived Content

If you need information from archived files for historical context or reference:
1. Check the current documentation first (see [ROADMAP.md](../../ROADMAP.md))
2. Browse this archive directory
3. Contact the development team if specific historical context is needed

---
**Archive Created**: 2025-01-27  
**Last Updated**: 2025-01-27
