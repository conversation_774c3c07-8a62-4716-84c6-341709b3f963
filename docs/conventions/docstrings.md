# Plugginger Docstring Convention

**Status**: ✅ Stable (v6.0+)  
**Sprint**: S2.3 - Docstring-Konvention  
**Coverage**: 90% (docstring_convention.py)

## Overview

The Plugginger Docstring Convention defines a standardized format for documenting services, optimized for both human readability and AI-agent compatibility. It extends Google-style docstrings with Plugginger-specific sections for service metadata and AI-agent hints.

## Key Features

### 🎯 Core Capabilities
- **Google-Style Base**: Built on familiar Google docstring format
- **AI-Agent Optimized**: Structured metadata for autonomous development
- **Service-Focused**: Specialized sections for service documentation
- **Validation Support**: Automatic format validation and linting
- **Template Generation**: Automated docstring template creation

### 🔧 Supported Sections

#### Mandatory Sections
- **Summary**: One-line service description (≤80 chars, no period)
- **Description**: Detailed functionality explanation
- **Args**: Parameter descriptions with types
- **Returns**: Return value structure and meaning
- **Example**: At least one working code example

#### Optional Sections
- **Raises**: Exception types and conditions
- **Note**: Important usage notes and dependencies
- **Since**: Version information
- **See Also**: Related services and functions
- **Deprecated**: Deprecation warnings

#### AI-Agent Sections
- **AI_METADATA**: Structured metadata for AI agents
- **USAGE_PATTERNS**: Common usage scenarios
- **ERROR_HANDLING**: Error handling strategies

## Standard Format

### Basic Service Docstring

```python
@service()
async def process_data(self, input_data: dict[str, Any], options: ProcessOptions | None = None) -> ProcessResult:
    """Process input data with configurable options.
    
    This service transforms input data according to the specified processing
    options and returns a structured result with metadata.
    
    Args:
        input_data: Raw data to process. Must contain 'content' key.
        options: Processing configuration. Defaults to standard options.
        
    Returns:
        ProcessResult containing:
            - processed_data: Transformed data
            - metadata: Processing statistics
            - status: Success/error status
            
    Raises:
        ValidationError: When input_data is malformed
        ProcessingError: When processing fails
        
    Example:
        >>> result = await plugin.process_data(
        ...     {"content": "Hello World"},
        ...     ProcessOptions(format="json")
        ... )
        >>> print(result.processed_data)
        {"content": "Hello World", "format": "json"}
        
    Note:
        This service requires the 'data_validator' dependency to be available.
        Processing time scales linearly with input size.
        
    Since:
        1.0.0
        
    See Also:
        - validate_data(): Input validation service
        - format_output(): Output formatting service
    """
```

### AI-Agent Enhanced Docstring

```python
@service()
async def ai_optimized_service(self, data: InputType) -> OutputType:
    """AI-friendly service with structured metadata.
    
    Comprehensive service documentation optimized for AI agent understanding
    and autonomous plugin development.
    
    Args:
        data: Input data with specific structure requirements
        
    Returns:
        OutputType with processed results and metadata
        
    Example:
        >>> result = await plugin.ai_optimized_service(data)
        >>> print(result.status)
        
    AI_METADATA:
        complexity: low
        dependencies: ["logger", "cache"]
        side_effects: none
        idempotent: true
        async_safe: true
        rate_limit: 100/minute
        
    USAGE_PATTERNS:
        - Data transformation pipeline
        - Real-time processing
        - Batch operations
        
    ERROR_HANDLING:
        - Graceful degradation on cache miss
        - Automatic retry on transient failures
        - Detailed error context in exceptions
    """
```

## API Reference

### DocstringParser

```python
from plugginger.core.docstring_convention import DocstringParser

parser = DocstringParser()
parsed = parser.parse(docstring_text)

# Access parsed sections
print(parsed.summary)
print(parsed.args)
print(parsed.ai_metadata)

# Validate format
if parsed.is_valid():
    print("✅ Valid docstring")
else:
    errors = parsed.validate()
    print(f"❌ Validation errors: {errors}")
```

### DocstringGenerator

```python
from plugginger.core.docstring_convention import DocstringGenerator

generator = DocstringGenerator()

# Generate basic template
template = generator.generate_template(
    service_name="process_data",
    args={"data": "dict[str, Any]", "options": "ProcessOptions | None"},
    returns="ProcessResult",
    raises={"ValidationError": "When input is invalid"},
    include_ai_metadata=True
)

print(template)
```

### ServiceDocstring

```python
from plugginger.core.docstring_convention import ServiceDocstring

# Create programmatically
docstring = ServiceDocstring(
    summary="Process data efficiently",
    description="Detailed processing logic",
    args={"data": "Input data to process"},
    returns="Processed result",
    examples=[">>> result = process_data(data)"],
    ai_metadata={"complexity": "low", "async_safe": True}
)

# Validate
assert docstring.is_valid()
```

## Validation Rules

### Summary Requirements
- **Length**: Maximum 80 characters
- **Format**: No trailing period
- **Content**: Clear, concise service description

### Example Requirements
- **Mandatory**: At least one working example
- **Format**: Executable code snippets
- **Coverage**: Demonstrate main functionality

### Argument Documentation
- **Completeness**: All parameters must be documented
- **Format**: `param_name: Description of parameter`
- **Types**: Type information should be in function signature

### AI Metadata Format
```yaml
AI_METADATA:
    complexity: low|medium|high
    dependencies: ["dep1", "dep2"]
    side_effects: none|read|write|network
    idempotent: true|false
    async_safe: true|false
    rate_limit: "100/minute"
    timeout: 30
```

## Template Integration

### Scaffold CLI Integration

The docstring convention is integrated into the Plugginger scaffold CLI:

```bash
# Generate plugin with proper docstrings
plugginger new plugin my_service --with-examples

# Generated service will include:
@service()
async def my_service_method(self, data: dict[str, Any]) -> dict[str, Any]:
    """My Service Method service.
    
    Detailed description of what this service does and how it works.
    
    Args:
        data: dict[str, Any] - Description of data
        
    Returns:
        dict[str, Any] - Description of return value
        
    Example:
        >>> result = await plugin.my_service_method({'key': 'value'})
        >>> print(result)
        # Expected output
        
    AI_METADATA:
        complexity: low
        dependencies: []
        side_effects: none
        idempotent: true
        async_safe: true
    """
```

### IDE Integration

For VS Code and other IDEs, use the docstring templates:

1. **Install Plugginger Extension** (future)
2. **Use Snippets**: Type `plugdoc` for template
3. **Auto-completion**: AI metadata suggestions

## Best Practices

### 1. Write Clear Summaries

```python
# ✅ Good: Clear and concise
"""Process user authentication data"""

# ❌ Bad: Too long or unclear
"""This service processes the user authentication data and validates it."""
```

### 2. Provide Comprehensive Examples

```python
# ✅ Good: Complete, executable example
"""
Example:
    >>> result = await plugin.authenticate_user({
    ...     "username": "john_doe",
    ...     "password": "secure_password"
    ... })
    >>> print(result.is_authenticated)
    True
"""

# ❌ Bad: Incomplete or non-executable
"""
Example:
    authenticate_user(data)
"""
```

### 3. Document All Parameters

```python
# ✅ Good: All parameters documented
"""
Args:
    username: User identifier for authentication
    password: User password in plain text
    options: Additional authentication options (optional)
"""

# ❌ Bad: Missing parameter documentation
"""
Args:
    username: User identifier
    # password parameter not documented
"""
```

### 4. Use AI Metadata Effectively

```python
# ✅ Good: Comprehensive AI metadata
"""
AI_METADATA:
    complexity: medium
    dependencies: ["database", "cache"]
    side_effects: read
    idempotent: true
    async_safe: true
    rate_limit: 50/minute
"""

# ❌ Bad: Minimal or incorrect metadata
"""
AI_METADATA:
    complexity: low
    # Missing important metadata
"""
```

## Linting Integration

### ruff Configuration

Add to `pyproject.toml`:

```toml
[tool.ruff.lint]
select = ["D"]  # Enable docstring checks

[tool.ruff.lint.pydocstyle]
convention = "google"  # Use Google-style docstrings
```

### Custom Validation

```python
from plugginger.core.docstring_convention import DocstringParser

def validate_service_docstring(func):
    """Decorator to validate service docstrings."""
    if func.__doc__:
        parser = DocstringParser()
        try:
            parsed = parser.parse(func.__doc__)
            if not parsed.is_valid():
                errors = parsed.validate()
                raise ValueError(f"Invalid docstring: {errors}")
        except Exception as e:
            raise ValueError(f"Docstring parsing failed: {e}")
    return func
```

## Migration Guide

### From Legacy Docstrings

```python
# Before: Minimal documentation
@service()
async def old_service(self, data):
    """Does something with data."""
    pass

# After: Plugginger convention
@service()
async def new_service(self, data: dict[str, Any]) -> dict[str, Any]:
    """Process data with advanced algorithms.
    
    This service applies machine learning algorithms to transform
    input data into structured output format.
    
    Args:
        data: Input data dictionary with required 'content' key
        
    Returns:
        dict[str, Any] containing:
            - processed_data: Transformed data
            - metadata: Processing statistics
            
    Raises:
        ValueError: When input data is malformed
        
    Example:
        >>> result = await plugin.new_service({"content": "test"})
        >>> print(result["processed_data"])
        
    AI_METADATA:
        complexity: medium
        dependencies: ["ml_processor"]
        side_effects: none
        idempotent: true
        async_safe: true
    """
```

### Automated Migration

```python
from plugginger.core.docstring_convention import DocstringGenerator

def migrate_docstring(func, service_name: str):
    """Migrate existing function to Plugginger docstring convention."""
    generator = DocstringGenerator()
    
    # Extract function signature information
    import inspect
    sig = inspect.signature(func)
    
    args = {}
    for param_name, param in sig.parameters.items():
        if param_name != 'self':
            args[param_name] = str(param.annotation) if param.annotation != param.empty else "Any"
    
    returns = str(sig.return_annotation) if sig.return_annotation != sig.empty else "Any"
    
    # Generate new docstring
    new_docstring = generator.generate_template(
        service_name=service_name,
        args=args,
        returns=returns,
        include_ai_metadata=True
    )
    
    func.__doc__ = new_docstring
    return func
```

## Testing

### Unit Testing Docstrings

```python
import pytest
from plugginger.core.docstring_convention import DocstringParser

def test_service_docstring_validity():
    """Test that service docstrings follow convention."""
    parser = DocstringParser()
    
    # Test valid docstring
    valid_docstring = '''"""Process data efficiently.
    
    Example:
        >>> result = process_data()
    """'''
    
    parsed = parser.parse(valid_docstring)
    assert parsed.is_valid()
    
    # Test invalid docstring
    invalid_docstring = '''"""Process data."""'''  # No example
    
    parsed = parser.parse(invalid_docstring)
    assert not parsed.is_valid()
    errors = parsed.validate()
    assert "At least one example is required" in errors
```

### Integration Testing

```python
def test_scaffold_generated_docstrings():
    """Test that scaffold CLI generates valid docstrings."""
    from plugginger.cli.cmd_new import generate_plugin_template
    
    # Generate plugin template
    template = generate_plugin_template("test_plugin")
    
    # Extract service docstrings
    # ... validation logic ...
    
    # Verify all docstrings are valid
    parser = DocstringParser()
    for docstring in extracted_docstrings:
        parsed = parser.parse(docstring)
        assert parsed.is_valid(), f"Invalid docstring: {parsed.validate()}"
```

## Troubleshooting

### Common Issues

1. **"Summary too long"**
   - Solution: Keep summary under 80 characters
   - Use description section for details

2. **"At least one example is required"**
   - Solution: Add executable code example
   - Show realistic usage scenario

3. **"Argument description is empty"**
   - Solution: Document all parameters
   - Provide meaningful descriptions

4. **"Invalid AI metadata format"**
   - Solution: Follow YAML-like key: value format
   - Use supported metadata keys

### Debug Tips

```python
from plugginger.core.docstring_convention import DocstringParser

# Debug docstring parsing
parser = DocstringParser()
try:
    parsed = parser.parse(your_docstring)
    print(f"Summary: {parsed.summary}")
    print(f"Args: {parsed.args}")
    print(f"Valid: {parsed.is_valid()}")
    if not parsed.is_valid():
        print(f"Errors: {parsed.validate()}")
except Exception as e:
    print(f"Parsing failed: {e}")
```

## Future Enhancements

### Planned Features
- 🔄 IDE extension for real-time validation
- 🔄 Automatic docstring generation from type hints
- 🔄 Integration with mkdocs for API documentation
- 🔄 AI-powered docstring suggestions
- 🔄 Multi-language support (German, etc.)

### Integration Roadmap
- **Phase 1**: Core convention implementation ✅
- **Phase 2**: Scaffold CLI integration ✅
- **Phase 3**: Linting and validation tools
- **Phase 4**: IDE extensions and tooling
- **Phase 5**: AI-powered enhancements
