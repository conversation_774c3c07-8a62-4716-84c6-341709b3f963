# AI-Agent Documentation Guide

**Status**: ✅ Stable (v6.0+)  
**Target**: Autonomous AI Agents  
**Convention**: Plugginger Docstring Standard

## Overview

This guide provides AI agents with structured instructions for creating, validating, and maintaining high-quality documentation in Plugginger projects. The documentation system is designed for autonomous operation with predictable patterns and validation.

## Core Principles

### 🤖 AI-Agent Optimized
- **Structured Metadata**: Machine-readable service information
- **Predictable Patterns**: Consistent documentation templates
- **Validation Support**: Automatic format checking
- **Error Recovery**: Clear error messages with solutions

### 📋 Documentation Standards
- **Google-Style Base**: Familiar docstring format
- **Mandatory Sections**: Summary, Args, Returns, Example
- **AI Extensions**: Metadata, usage patterns, error handling
- **Quality Gates**: Automated validation and linting

## Quick Start for AI Agents

### 1. Import Required Classes

```python
from plugginger.core.docstring_convention import (
    DocstringParser,
    DocstringGenerator,
    ServiceDocstring
)
```

### 2. Generate Service Documentation

```python
# For new services
generator = DocstringGenerator()
docstring = generator.generate_template(
    service_name="process_data",
    args={"data": "dict[str, Any]", "options": "ProcessOptions | None"},
    returns="ProcessResult",
    raises={"ValidationError": "When input is invalid"},
    include_ai_metadata=True
)
```

### 3. Validate Existing Documentation

```python
# For existing services
parser = DocstringParser()
parsed = parser.parse(existing_docstring)

if not parsed.is_valid():
    errors = parsed.validate()
    # Handle validation errors
    for error in errors:
        print(f"❌ {error}")
```

## AI Metadata Schema

### Required Fields

```yaml
AI_METADATA:
    complexity: low|medium|high
    dependencies: ["service1", "service2"]
    side_effects: none|read|write|network
    idempotent: true|false
    async_safe: true|false
```

### Optional Fields

```yaml
AI_METADATA:
    rate_limit: "100/minute"
    timeout: 30
    cache_ttl: 300
    retry_policy: "exponential_backoff"
    circuit_breaker: true
    monitoring: ["metrics", "logs", "traces"]
```

### Field Definitions

| Field | Type | Values | Description |
|-------|------|--------|-------------|
| `complexity` | string | low, medium, high | Implementation complexity |
| `dependencies` | list | service names | Required service dependencies |
| `side_effects` | string | none, read, write, network | Service side effects |
| `idempotent` | boolean | true, false | Safe to retry operation |
| `async_safe` | boolean | true, false | Thread/async safe |
| `rate_limit` | string | "N/unit" | Rate limiting information |
| `timeout` | integer | seconds | Operation timeout |

## Template Patterns

### Basic Service Template

```python
@service()
async def {service_name}(self, {parameters}) -> {return_type}:
    """{Service Name} service.
    
    {Detailed description of service functionality and purpose.}
    
    Args:
        {param_name}: {param_type} - {param_description}
        
    Returns:
        {return_type} containing:
            - {field1}: {description1}
            - {field2}: {description2}
            
    Raises:
        {ExceptionType}: {exception_condition}
        
    Example:
        >>> result = await plugin.{service_name}({example_args})
        >>> print(result.{example_field})
        {expected_output}
        
    AI_METADATA:
        complexity: {low|medium|high}
        dependencies: [{dependencies}]
        side_effects: {none|read|write|network}
        idempotent: {true|false}
        async_safe: true
    """
```

### Data Processing Service

```python
@service()
async def process_data(self, input_data: dict[str, Any], options: ProcessOptions | None = None) -> ProcessResult:
    """Process Data service.
    
    Transforms input data using configurable processing algorithms
    and returns structured results with metadata.
    
    Args:
        input_data: Raw data dictionary with 'content' key required
        options: Processing configuration options (optional)
        
    Returns:
        ProcessResult containing:
            - processed_data: Transformed data structure
            - metadata: Processing statistics and timing
            - status: Success/error status indicator
            
    Raises:
        ValidationError: When input_data format is invalid
        ProcessingError: When data processing fails
        
    Example:
        >>> result = await plugin.process_data(
        ...     {"content": "Hello World", "format": "text"},
        ...     ProcessOptions(algorithm="standard")
        ... )
        >>> print(result.processed_data)
        {"content": "Hello World", "format": "text", "processed": True}
        
    AI_METADATA:
        complexity: medium
        dependencies: ["validator", "processor"]
        side_effects: none
        idempotent: true
        async_safe: true
        rate_limit: 100/minute
        timeout: 30
    """
```

### External API Service

```python
@service()
async def fetch_external_data(self, endpoint: str, params: dict[str, Any] | None = None) -> APIResponse:
    """Fetch External Data service.
    
    Retrieves data from external APIs with automatic retry logic,
    error handling, and response caching.
    
    Args:
        endpoint: API endpoint URL to fetch data from
        params: Query parameters for the API request (optional)
        
    Returns:
        APIResponse containing:
            - data: Retrieved data from external API
            - status_code: HTTP response status code
            - headers: Response headers dictionary
            - cached: Whether response was served from cache
            
    Raises:
        ConnectionError: When API is unreachable
        TimeoutError: When request exceeds timeout limit
        APIError: When API returns error response
        
    Example:
        >>> result = await plugin.fetch_external_data(
        ...     "https://api.example.com/data",
        ...     {"limit": 10, "offset": 0}
        ... )
        >>> print(result.status_code)
        200
        
    AI_METADATA:
        complexity: high
        dependencies: ["http_client", "cache"]
        side_effects: network
        idempotent: true
        async_safe: true
        rate_limit: 50/minute
        timeout: 60
        retry_policy: exponential_backoff
        circuit_breaker: true
    """
```

## Validation Workflow

### 1. Automatic Validation

```python
def validate_service_documentation(service_func) -> list[str]:
    """Validate service documentation against Plugginger standards."""
    if not service_func.__doc__:
        return ["Service missing docstring"]
    
    parser = DocstringParser()
    try:
        parsed = parser.parse(service_func.__doc__)
        return parsed.validate()
    except Exception as e:
        return [f"Docstring parsing failed: {e}"]

# Usage in AI agent workflow
errors = validate_service_documentation(my_service)
if errors:
    # Fix documentation issues
    fix_documentation_errors(my_service, errors)
```

### 2. Error Handling

```python
def fix_documentation_errors(service_func, errors: list[str]) -> None:
    """Fix common documentation errors automatically."""
    for error in errors:
        if "Summary too long" in error:
            # Truncate summary to 80 characters
            fix_summary_length(service_func)
        elif "At least one example is required" in error:
            # Generate basic example
            add_example_section(service_func)
        elif "Argument description is empty" in error:
            # Add parameter descriptions
            add_parameter_descriptions(service_func)
```

### 3. Quality Assurance

```python
def ensure_documentation_quality(plugin_class) -> bool:
    """Ensure all services in plugin have quality documentation."""
    services = get_service_methods(plugin_class)
    
    for service in services:
        errors = validate_service_documentation(service)
        if errors:
            print(f"❌ {service.__name__}: {errors}")
            return False
    
    print("✅ All services have valid documentation")
    return True
```

## AI Agent Workflow

### 1. Service Creation Workflow

```python
def create_documented_service(
    service_name: str,
    parameters: dict[str, str],
    return_type: str,
    complexity: str = "low"
) -> str:
    """Generate complete service with documentation."""
    
    # 1. Generate docstring
    generator = DocstringGenerator()
    docstring = generator.generate_template(
        service_name=service_name,
        args=parameters,
        returns=return_type,
        include_ai_metadata=True
    )
    
    # 2. Create service method
    service_code = f'''
@service()
async def {service_name}(self, {format_parameters(parameters)}) -> {return_type}:
    {docstring}
    # TODO: Implement service logic
    pass
'''
    
    # 3. Validate generated documentation
    parser = DocstringParser()
    parsed = parser.parse(docstring)
    
    if not parsed.is_valid():
        errors = parsed.validate()
        raise ValueError(f"Generated invalid docstring: {errors}")
    
    return service_code
```

### 2. Documentation Update Workflow

```python
def update_service_documentation(
    service_func,
    new_parameters: dict[str, str] | None = None,
    new_return_type: str | None = None
) -> None:
    """Update existing service documentation."""
    
    # 1. Parse existing docstring
    parser = DocstringParser()
    if service_func.__doc__:
        parsed = parser.parse(service_func.__doc__)
    else:
        parsed = ServiceDocstring(summary="Service needs documentation")
    
    # 2. Update with new information
    if new_parameters:
        parsed.args.update(new_parameters)
    if new_return_type:
        parsed.returns = new_return_type
    
    # 3. Regenerate docstring
    generator = DocstringGenerator()
    new_docstring = generator.generate_template(
        service_name=service_func.__name__,
        args=parsed.args,
        returns=parsed.returns,
        include_ai_metadata=True
    )
    
    # 4. Update service
    service_func.__doc__ = new_docstring
```

### 3. Batch Documentation Workflow

```python
def document_all_services(plugin_class) -> None:
    """Document all services in a plugin class."""
    services = get_service_methods(plugin_class)
    
    for service in services:
        if not service.__doc__ or not is_valid_docstring(service.__doc__):
            # Extract service information
            service_info = extract_service_info(service)
            
            # Generate documentation
            generator = DocstringGenerator()
            docstring = generator.generate_template(
                service_name=service.__name__,
                args=service_info.parameters,
                returns=service_info.return_type,
                include_ai_metadata=True
            )
            
            # Update service
            service.__doc__ = docstring
            
            print(f"✅ Documented {service.__name__}")
```

## Error Recovery Patterns

### Common Validation Errors

| Error | Cause | Solution |
|-------|-------|----------|
| "Summary too long" | Summary > 80 chars | Truncate and move details to description |
| "Summary should not end with period" | Summary ends with "." | Remove trailing period |
| "At least one example is required" | Missing example section | Add realistic code example |
| "Argument description is empty" | Empty parameter docs | Add meaningful descriptions |
| "Exception description is empty" | Empty exception docs | Document exception conditions |

### Automatic Error Fixes

```python
def auto_fix_docstring_errors(docstring: str) -> str:
    """Automatically fix common docstring errors."""
    parser = DocstringParser()
    parsed = parser.parse(docstring)
    errors = parsed.validate()
    
    for error in errors:
        if "Summary too long" in error:
            # Truncate summary
            parsed.summary = parsed.summary[:77] + "..."
        elif "Summary should not end with period" in error:
            # Remove trailing period
            parsed.summary = parsed.summary.rstrip(".")
        elif "At least one example is required" in error:
            # Add basic example
            parsed.examples = [f">>> result = await plugin.{extract_service_name(docstring)}()"]
    
    # Regenerate docstring
    return regenerate_docstring(parsed)
```

## Integration with Development Tools

### Pre-commit Hooks

```python
# .pre-commit-config.yaml
repos:
  - repo: local
    hooks:
      - id: validate-docstrings
        name: Validate Plugginger Docstrings
        entry: python -m plugginger.tools.validate_docs
        language: system
        files: \.py$
```

### CI/CD Integration

```yaml
# .github/workflows/documentation.yml
name: Documentation Quality
on: [push, pull_request]

jobs:
  validate-docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install dependencies
        run: pip install plugginger[dev]
      - name: Validate documentation
        run: python -m plugginger.tools.validate_docs --strict
```

### IDE Integration

```json
// VS Code settings.json
{
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": false,
  "python.linting.ruffEnabled": true,
  "python.linting.ruffArgs": ["--select", "D"],
  "plugginger.docstring.autoValidate": true,
  "plugginger.docstring.showAIMetadata": true
}
```

## Best Practices for AI Agents

### 1. Always Validate Generated Documentation

```python
# ✅ Good: Validate after generation
docstring = generator.generate_template(...)
parsed = parser.parse(docstring)
assert parsed.is_valid(), f"Invalid docstring: {parsed.validate()}"

# ❌ Bad: No validation
docstring = generator.generate_template(...)
# Use without validation
```

### 2. Use Structured Error Handling

```python
# ✅ Good: Handle specific errors
try:
    parsed = parser.parse(docstring)
except ConfigurationError as e:
    if "empty" in str(e):
        # Handle empty docstring
        generate_new_docstring()
    else:
        # Handle other configuration errors
        fix_configuration_error(e)

# ❌ Bad: Generic error handling
try:
    parsed = parser.parse(docstring)
except Exception:
    # Too generic
    pass
```

### 3. Maintain Consistency Across Services

```python
# ✅ Good: Consistent metadata across plugin
ai_metadata_template = {
    "complexity": "low",
    "dependencies": plugin_dependencies,
    "side_effects": "none",
    "idempotent": True,
    "async_safe": True
}

for service in services:
    update_ai_metadata(service, ai_metadata_template)

# ❌ Bad: Inconsistent metadata
# Each service has different metadata format
```

### 4. Leverage Template Patterns

```python
# ✅ Good: Use established patterns
if service_type == "data_processor":
    template = DATA_PROCESSING_TEMPLATE
elif service_type == "api_client":
    template = API_CLIENT_TEMPLATE
else:
    template = BASIC_SERVICE_TEMPLATE

# ❌ Bad: Generate from scratch each time
# Reinvent documentation patterns
```

## Troubleshooting Guide

### Debug Documentation Issues

```python
def debug_docstring_issues(service_func) -> None:
    """Debug documentation issues step by step."""
    print(f"🔍 Debugging {service_func.__name__}")
    
    # 1. Check if docstring exists
    if not service_func.__doc__:
        print("❌ No docstring found")
        return
    
    # 2. Try parsing
    parser = DocstringParser()
    try:
        parsed = parser.parse(service_func.__doc__)
        print("✅ Docstring parsed successfully")
    except Exception as e:
        print(f"❌ Parsing failed: {e}")
        return
    
    # 3. Validate format
    errors = parsed.validate()
    if errors:
        print("❌ Validation errors:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("✅ Docstring is valid")
    
    # 4. Check AI metadata
    if parsed.ai_metadata:
        print(f"✅ AI metadata: {parsed.ai_metadata}")
    else:
        print("⚠️  No AI metadata found")
```

### Common Integration Issues

1. **Import Errors**: Ensure `plugginger.core.docstring_convention` is available
2. **Validation Failures**: Check docstring format against examples
3. **Template Generation**: Verify parameter types and return types
4. **AI Metadata**: Follow exact YAML-like format for metadata

### Performance Considerations

- **Caching**: Parser results can be cached for repeated validation
- **Batch Processing**: Validate multiple docstrings in single operation
- **Lazy Loading**: Only parse docstrings when validation is needed
- **Memory Usage**: Large docstrings consume more memory during parsing
