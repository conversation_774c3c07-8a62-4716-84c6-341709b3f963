"""Web API Plugin - FastAPI HTTP Endpoints for Web Interface."""

from __future__ import annotations

import asyncio
import time
from typing import Any

from pydantic import BaseModel

from plugginger.api.app import PluggingerAppInstance
from plugginger.api.depends import Depends
from plugginger.api.events import on_event
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service


class ChatRequest(BaseModel):
    """Request model for chat endpoint."""
    message: str
    conversation_id: str | None = None
    model: str = "gpt-3.5-turbo"


class ChatResponse(BaseModel):
    """Response model for chat endpoint."""
    response: str
    conversation_id: str
    model: str
    tokens_used: int | None = None
    response_time: float | None = None


@plugin(name="web_api", version="1.0.0")
class WebAPIPlugin(PluginBase):
    """
    Web API Plugin for FastAPI HTTP Endpoints.

    Provides REST API endpoints for the AI chat application.
    Handles HTTP requests and integrates with chat_ai and memory_store plugins.
    """

    needs: list[Depends] = [
        Depends("chat_ai"),
        Depends("memory_store"),
        Depends("frontend_ui")
    ]

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        """Initialize the web API plugin."""
        super().__init__(app, **injected_dependencies)

        # Get injected dependencies
        self.chat_ai = injected_dependencies.get("chat_ai")
        self.memory_store = injected_dependencies.get("memory_store")
        self.frontend_ui = injected_dependencies.get("frontend_ui")

        # FastAPI app instance (will be created in setup)
        self._fastapi_app: Any = None
        self._server_task: asyncio.Task[Any] | None = None
        self._server_port = 8000
        self._server_host = "0.0.0.0"

    async def setup(self, plugin_config: BaseModel) -> None:
        """Setup the web API plugin."""
        try:
            # Import FastAPI here to avoid dependency issues if not installed
            from fastapi import FastAPI
            from fastapi.middleware.cors import CORSMiddleware

            # Create FastAPI app
            self._fastapi_app = FastAPI(
                title="AI Chat Reference App",
                description="Plugginger Framework Reference Implementation",
                version="1.0.0"
            )

            # Add CORS middleware
            self._fastapi_app.add_middleware(
                CORSMiddleware,
                allow_origins=["*"],  # Configure appropriately for production
                allow_credentials=True,
                allow_methods=["*"],
                allow_headers=["*"],
            )

            # Register routes
            self._register_routes()

            self.app.logger.info("WebAPI plugin initialized with FastAPI")

        except ImportError:
            self.app.logger.error("FastAPI not installed, WebAPI plugin will use mock mode")
            self._fastapi_app = None

    def _register_routes(self) -> None:
        """Register FastAPI routes."""
        if not self._fastapi_app:
            return

        from fastapi import HTTPException
        from fastapi.responses import HTMLResponse

        @self._fastapi_app.get("/", response_class=HTMLResponse)  # type: ignore[misc]
        async def chat_interface() -> str:
            """Serve the main chat interface."""
            if self.frontend_ui is None:
                raise HTTPException(status_code=500, detail="Frontend UI service not available")
            html_content = await self.frontend_ui.get_chat_html()
            assert isinstance(html_content, str)
            return html_content

        @self._fastapi_app.get("/health")  # type: ignore[misc]
        async def health_check() -> dict[str, str]:
            """Health check endpoint."""
            return await self.get_health_status()

        @self._fastapi_app.post("/chat", response_model=ChatResponse)  # type: ignore[misc]
        async def chat_endpoint(request: ChatRequest) -> ChatResponse:
            """Chat endpoint for sending messages and getting AI responses."""
            try:
                start_time = time.time()

                # Type guards for dependencies
                if self.memory_store is None:
                    raise HTTPException(status_code=500, detail="Memory store service not available")
                if self.chat_ai is None:
                    raise HTTPException(status_code=500, detail="Chat AI service not available")

                # Create conversation if not provided
                conversation_id = request.conversation_id
                if not conversation_id:
                    conversation_id = await self.memory_store.create_conversation()

                # Emit request received event
                await self.app.emit_event("api.request_received", {
                    "endpoint": "/chat",
                    "conversation_id": conversation_id,
                    "message": request.message,
                    "timestamp": start_time
                })

                # Generate AI response
                response = await self.chat_ai.generate_response(
                    message=request.message,
                    conversation_id=conversation_id,
                    model=request.model
                )

                response_time = time.time() - start_time

                # Model info not needed for response, removed unused variable

                # Emit response sent event
                await self.app.emit_event("api.response_sent", {
                    "endpoint": "/chat",
                    "conversation_id": conversation_id,
                    "response_time": response_time,
                    "timestamp": time.time()
                })

                # Ensure conversation_id is not None for response
                if conversation_id is None:
                    raise HTTPException(status_code=500, detail="Failed to create conversation")

                return ChatResponse(
                    response=response,
                    conversation_id=conversation_id,
                    model=request.model,
                    response_time=response_time
                )

            except Exception as e:
                self.app.logger.error(f"Chat endpoint error: {e}")
                raise HTTPException(status_code=500, detail=str(e)) from None

        @self._fastapi_app.get("/conversations")  # type: ignore[misc]
        async def list_conversations() -> list[dict[str, Any]]:
            """List all conversations."""
            try:
                if self.memory_store is None:
                    raise HTTPException(status_code=500, detail="Memory store service not available")
                return await self.memory_store.list_conversations()  # type: ignore[no-any-return]
            except Exception as e:
                self.app.logger.error(f"List conversations error: {e}")
                raise HTTPException(status_code=500, detail=str(e)) from None

        @self._fastapi_app.get("/conversations/{conversation_id}/history")  # type: ignore[misc]
        async def get_conversation_history(
            conversation_id: str,
            limit: int = 50
        ) -> list[dict[str, Any]]:
            """Get conversation history."""
            try:
                if self.memory_store is None:
                    raise HTTPException(status_code=500, detail="Memory store service not available")
                return await self.memory_store.get_conversation_history(  # type: ignore[no-any-return]
                    conversation_id=conversation_id,
                    limit=limit,
                    include_metadata=True
                )
            except ValueError as e:
                raise HTTPException(status_code=404, detail=str(e)) from None
            except Exception as e:
                self.app.logger.error(f"Get conversation history error: {e}")
                raise HTTPException(status_code=500, detail=str(e)) from None

        @self._fastapi_app.get("/conversations/{conversation_id}")  # type: ignore[misc]
        async def get_conversation_info(conversation_id: str) -> dict[str, Any]:
            """Get conversation information."""
            try:
                if self.memory_store is None:
                    raise HTTPException(status_code=500, detail="Memory store service not available")
                return await self.memory_store.get_conversation_info(conversation_id)  # type: ignore[no-any-return]
            except ValueError as e:
                raise HTTPException(status_code=404, detail=str(e)) from None
            except Exception as e:
                self.app.logger.error(f"Get conversation info error: {e}")
                raise HTTPException(status_code=500, detail=str(e)) from None

        @self._fastapi_app.delete("/conversations/{conversation_id}")  # type: ignore[misc]
        async def delete_conversation(conversation_id: str) -> dict[str, bool]:
            """Delete a conversation."""
            try:
                if self.memory_store is None:
                    raise HTTPException(status_code=500, detail="Memory store service not available")
                deleted = await self.memory_store.delete_conversation(conversation_id)
                if not deleted:
                    raise HTTPException(status_code=404, detail="Conversation not found")
                return {"deleted": True}
            except HTTPException:
                raise
            except Exception as e:
                self.app.logger.error(f"Delete conversation error: {e}")
                raise HTTPException(status_code=500, detail=str(e)) from None

        @self._fastapi_app.get("/models")  # type: ignore[misc]
        async def get_model_info() -> dict[str, Any]:
            """Get AI model information."""
            try:
                if self.chat_ai is None:
                    raise HTTPException(status_code=500, detail="Chat AI service not available")
                return await self.chat_ai.get_model_info()  # type: ignore[no-any-return]
            except Exception as e:
                self.app.logger.error(f"Get model info error: {e}")
                raise HTTPException(status_code=500, detail=str(e)) from None

    @service()
    async def start_server(self, host: str = "0.0.0.0", port: int = 8000) -> None:
        """
        Start the FastAPI server.

        Args:
            host: Host to bind the server to
            port: Port to bind the server to
        """
        if not self._fastapi_app:
            self.app.logger.error("FastAPI not available, cannot start server")
            return

        if self._server_task and not self._server_task.done():
            self.app.logger.warning("Server already running")
            return

        try:
            import uvicorn

            self._server_host = host
            self._server_port = port

            # Create server config
            config = uvicorn.Config(
                app=self._fastapi_app,
                host=host,
                port=port,
                log_level="info"
            )

            # Start server in background task
            server = uvicorn.Server(config)
            self._server_task = asyncio.create_task(server.serve())

            self.app.logger.info(f"FastAPI server started on http://{host}:{port}")

        except ImportError:
            self.app.logger.error("uvicorn not installed, cannot start server")
        except Exception as e:
            self.app.logger.error(f"Failed to start server: {e}")

    @service()
    async def stop_server(self) -> None:
        """Stop the FastAPI server."""
        if self._server_task and not self._server_task.done():
            self._server_task.cancel()
            try:
                await self._server_task
            except asyncio.CancelledError:
                pass
            self.app.logger.info("FastAPI server stopped")

    @service()
    async def get_health_status(self) -> dict[str, str]:
        """
        Get API health status for /health endpoint.

        Returns:
            Health status information
        """
        try:
            # Check dependencies with type guards
            if self.memory_store is None or self.chat_ai is None or self.frontend_ui is None:
                return {
                    "status": "unhealthy",
                    "error": "Required services not available",
                    "timestamp": str(time.time())
                }

            memory_stats = await self.memory_store.get_storage_stats()
            model_info = await self.chat_ai.get_model_info()

            return {
                "status": "healthy",
                "timestamp": str(time.time()),
                "version": "1.0.0",
                "memory_store": "available",
                "chat_ai": "available",
                "frontend_ui": "available",
                "server_host": self._server_host,
                "server_port": str(self._server_port),
                "total_conversations": str(memory_stats.get("total_conversations", 0)),
                "ai_model": model_info.get("current_model", "unknown")
            }
        except Exception as e:
            self.app.logger.error(f"Health check error: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": str(time.time())
            }

    @service()
    async def get_server_info(self) -> dict[str, Any]:
        """
        Get server information and statistics.

        Returns:
            Server information and metrics
        """
        return {
            "host": self._server_host,
            "port": self._server_port,
            "running": self._server_task is not None and not self._server_task.done(),
            "fastapi_available": self._fastapi_app is not None,
            "plugin_version": "1.0.0"
        }

    @on_event("chat.response_generated")
    async def handle_chat_response(self, event_data: dict[str, Any]) -> None:
        """
        Handle chat response events for logging and monitoring.

        Args:
            event_data: Event data containing response information
        """
        conversation_id = event_data.get("conversation_id")
        response_time = event_data.get("response_time")

        if conversation_id and response_time:
            self.app.logger.debug(
                f"Chat response generated for {conversation_id} "
                f"in {response_time:.2f}s"
            )
