manifest_version: "1.0.0"
metadata:
  name: "frontend_ui"
  version: "1.0.0"
  description: "Frontend UI Plugin for serving static files and HTML templates"
  author: "Plugginger Framework"
  license: "MIT"
  tags: ["frontend", "ui", "html", "static"]

runtime:
  plugginger_version: ">=0.9.0"
  execution_mode: "thread"
  python_version: ">=3.11"

services:
  - name: "get_chat_html"
    description: "Get the main chat interface HTML"
    returns:
      type: "str"
      description: "HTML content for the chat interface"

  - name: "get_static_file_content"
    description: "Get static file content by filename"
    parameters:
      - name: "filename"
        type: "str"
        required: true
        description: "Name of the static file to retrieve"
    returns:
      type: "str | None"
      description: "File content as string, or None if not found"

event_listeners: []

events_emitted: []

dependencies: []

config_schema:
  type: "object"
  properties: {}
  additionalProperties: false
