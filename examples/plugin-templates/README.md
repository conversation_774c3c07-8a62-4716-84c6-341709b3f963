# Plugin Templates with Docstring Convention

**Status**: ✅ Available (v6.0+)  
**Sprint**: S2.3 - Template Updates  
**Purpose**: Reference templates for AI agents and developers

## Overview

This directory contains comprehensive plugin templates that demonstrate the Plugginger Docstring Convention. Each template includes:

- **Complete service implementations** with proper docstrings
- **AI-agent optimized metadata** for autonomous development
- **Working examples** and test cases
- **Best practices** for different plugin types

## Available Templates

### 🔧 Basic Service Plugin
**Path**: `basic-service/`  
**Use Case**: Simple service plugins with minimal dependencies

```bash
# Generate basic service plugin
plugginger new plugin my-service --template basic

# Generated service includes:
@service()
async def hello(self) -> str:
    """Hello service.
    
    Returns a greeting message from the plugin.
    
    Returns:
        str: Greeting message from the plugin
        
    Example:
        >>> result = await plugin.hello()
        >>> print(result)
        "Hello from my-service!"
        
    AI_METADATA:
        complexity: low
        dependencies: []
        side_effects: none
        idempotent: true
        async_safe: true
    """
```

### 📊 Data Processing Plugin
**Path**: `data-processor/`  
**Use Case**: Data transformation and processing services

```bash
# Generate data processor plugin
plugginger new plugin my-processor --template data_processor

# Generated service includes:
@service()
async def process_data(self, input_data: dict[str, Any], options: ProcessOptions | None = None) -> ProcessResult:
    """Process Data service.
    
    Transforms input data using configurable processing algorithms
    and returns structured results with metadata.
    
    Args:
        input_data: Raw data dictionary with 'content' key required
        options: Processing configuration options (optional)
        
    Returns:
        ProcessResult containing:
            - processed_data: Transformed data structure
            - metadata: Processing statistics and timing
            - status: Success/error status indicator
            
    AI_METADATA:
        complexity: medium
        dependencies: ["validator", "processor"]
        side_effects: none
        idempotent: true
        async_safe: true
        rate_limit: 100/minute
        timeout: 30
    """
```

### 🌐 API Client Plugin
**Path**: `api-client/`  
**Use Case**: External API integration with retry logic and caching

```bash
# Generate API client plugin
plugginger new plugin my-client --template api_client

# Generated service includes:
@service()
async def fetch_data(self, endpoint: str, params: dict[str, Any] | None = None) -> APIResponse:
    """Fetch Data service.
    
    Retrieves data from external APIs with automatic retry logic,
    error handling, and response caching.
    
    AI_METADATA:
        complexity: high
        dependencies: ["http_client", "cache"]
        side_effects: network
        idempotent: true
        async_safe: true
        rate_limit: 50/minute
        timeout: 60
        retry_policy: exponential_backoff
        circuit_breaker: true
    """
```

### 🎯 Event Handler Plugin
**Path**: `event-handler/`  
**Use Case**: Event processing with validation and side effects

```bash
# Generate event handler plugin
plugginger new plugin my-handler --template event_handler

# Generated service includes:
@service()
async def handle_event(self, event_data: dict[str, Any]) -> EventResult:
    """Handle Event service.
    
    Processes incoming events with validation, transformation,
    and optional side effects like notifications or logging.
    
    AI_METADATA:
        complexity: medium
        dependencies: ["event_validator", "notification_service"]
        side_effects: write
        idempotent: false
        async_safe: true
        rate_limit: 200/minute
    """
```

## Template Features

### 📋 Docstring Convention Compliance
- **Google-style base format** with Plugginger extensions
- **Mandatory sections**: Summary, Args, Returns, Example
- **AI metadata sections**: Complexity, dependencies, side effects
- **Validation ready**: All templates pass docstring validation

### 🤖 AI-Agent Optimized
- **Structured metadata** for autonomous development
- **Clear complexity indicators** (low/medium/high)
- **Dependency declarations** for service resolution
- **Side effect documentation** for safety analysis

### 🧪 Test Integration
- **Complete test suites** for each template
- **Example usage patterns** in test cases
- **Quality gate compliance** (pytest/mypy/ruff)
- **Coverage targets** (≥90% for new modules)

## Usage Guide

### For Developers

```bash
# 1. Choose appropriate template
plugginger new plugin my-plugin --template data_processor

# 2. Customize generated code
cd my-plugin
# Edit plugin file and tests

# 3. Validate docstrings
python -c "
from plugginger.core.docstring_convention import DocstringParser
parser = DocstringParser()
# Validate your service docstrings
"

# 4. Run quality gates
pytest && mypy --strict . && ruff check .
```

### For AI Agents

```python
# 1. Analyze template patterns
from plugginger.cli.cmd_new import _generate_service_template

# Generate service with proper docstring
service_code = _generate_service_template(
    service_name="my_service",
    template_type="data_processor",
    plugin_name="my_plugin"
)

# 2. Validate generated docstring
from plugginger.core.docstring_convention import DocstringParser
parser = DocstringParser()
# Extract and validate docstring from generated code

# 3. Customize for specific use case
# Modify args, returns, raises sections as needed
```

## Template Structure

Each template directory contains:

```
template-name/
├── README.md                    # Template-specific documentation
├── plugin.py                    # Main plugin implementation
├── tests/
│   ├── __init__.py
│   └── test_plugin.py          # Comprehensive test suite
├── manifest.yaml               # Plugin manifest
├── pyproject.toml              # Project configuration
└── examples/
    └── usage_example.py        # Usage examples
```

## Best Practices

### 1. Service Documentation
- **Clear summaries** (≤80 characters, no period)
- **Complete examples** with expected output
- **All parameters documented** with types and descriptions
- **Exception handling** documented in Raises section

### 2. AI Metadata Guidelines
- **Complexity**: `low` (simple logic), `medium` (some complexity), `high` (complex algorithms)
- **Dependencies**: List all required services/plugins
- **Side effects**: `none`, `read`, `write`, `network`
- **Idempotent**: `true` if safe to retry, `false` if not

### 3. Template Customization
- **Preserve docstring structure** when modifying
- **Update AI metadata** to reflect actual complexity
- **Add specific examples** for your use case
- **Include error handling** in implementation

## Integration with Scaffold CLI

The templates are integrated into the `plugginger new plugin` command:

```bash
# Basic template (default)
plugginger new plugin my-plugin

# Specific template
plugginger new plugin my-plugin --template data_processor
plugginger new plugin my-plugin --template api_client
plugginger new plugin my-plugin --template event_handler

# With additional options (future)
plugginger new plugin my-plugin --template data_processor --with-tests --with-examples
```

## Validation and Quality

### Automated Validation
All templates include validation for:
- **Docstring format compliance** via DocstringParser
- **Type safety** via mypy --strict
- **Code style** via ruff
- **Test coverage** ≥90% for new modules

### Manual Review Checklist
- [ ] Service docstring follows Plugginger convention
- [ ] AI metadata accurately reflects service characteristics
- [ ] Examples are executable and demonstrate main functionality
- [ ] All parameters and return values documented
- [ ] Exception handling documented and implemented

## Contributing

### Adding New Templates
1. **Create template directory** under `examples/plugin-templates/`
2. **Implement service** with complete docstring
3. **Add comprehensive tests** with ≥90% coverage
4. **Update CLI integration** in `cmd_new.py`
5. **Document template** in this README

### Template Requirements
- **Docstring convention compliance** (validated automatically)
- **Working implementation** (not just TODO comments)
- **Complete test suite** with realistic test cases
- **AI metadata accuracy** reflecting actual complexity
- **Clear documentation** with usage examples

## Troubleshooting

### Common Issues
1. **"Docstring validation failed"**
   - Check summary length (≤80 chars)
   - Ensure example section is present
   - Verify AI metadata format

2. **"Template generation error"**
   - Verify template type is supported
   - Check plugin name format (no special chars)
   - Ensure output directory is writable

3. **"Import errors in generated code"**
   - Check dataclass imports for complex templates
   - Verify typing imports for generic types
   - Ensure plugginger API imports are correct

### Debug Commands
```bash
# Validate docstring format
python -c "
from plugginger.core.docstring_convention import DocstringParser
parser = DocstringParser()
result = parser.parse(your_docstring)
print(result.validate())
"

# Test template generation
python -c "
from plugginger.cli.cmd_new import _generate_service_template
template = _generate_service_template('test', 'basic', 'test-plugin')
print(template)
"
```

---

**Status**: ✅ Templates available and integrated  
**Next Phase**: Validation tooling and IDE integration  
**Documentation**: Complete with examples and troubleshooting
